import os


def get_apella_server_environment() -> str:
    return os.getenv("APELLA_API_SERVER_ENVIRONMENT", "dev")


def get_dynamic_case_end_service() -> str:
    return os.getenv("DYNAMIC_CASE_END_SERVICE", "http://localhost:9992/predict_many")


def get_static_start_offset_service() -> str:
    return os.getenv("STATIC_START_OFFSET_SERVICE", "http://localhost:9997/predict_many")


def get_turnover_duration_service() -> str:
    return os.getenv("TURNOVER_DURATION_SERVICE", "http://localhost:9998/predict_many_cases")


def get_event_model_forecasts_service() -> str:
    return os.getenv("EVENT_MODEL_FORECASTS_SERVICE", "http://localhost:9995/predict_many")


def get_bayesian_case_duration_service() -> str:
    return os.getenv("BAYESIAN_CASE_DURATION_SERVICE", "http://localhost:9980/predict_many_cases")


def get_gcp_project_id() -> str:
    return os.getenv("GCP_PROJECT", "dev-data-platform-439b4c")


def get_gcp_web_project_id() -> str:
    return os.getenv("WEB_API_GCP_PROJECT", "dev-web-api-72f12b")


def get_redis_host() -> str:
    return os.getenv("REDIS_HOST", "localhost")


def get_redis_port() -> int:
    return int(os.getenv("REDIS_PORT", "6379"))


def get_launch_darkly_sdk_key_secret() -> str:
    return os.getenv("LAUNCHDARKLY_KEY_SECRET", "")


def get_dev_stub() -> str:
    return os.getenv("DEV_STUB", "0")


def is_dev_stub() -> bool:
    """
    Check if the DEV_STUB environment variable is set to "1".
    """
    return get_dev_stub() == "1"


def get_project_version() -> str:
    return os.getenv("PROJECT_VERSION", "None")


def get_store_forecast_result() -> bool:
    return os.getenv("STORE_FORECAST_RESULT", "0") == "1"


def get_worker_count() -> int:
    return int(os.getenv("WORKER_COUNT", "5"))


def get_request_timeout_seconds() -> int:
    return int(os.getenv("REQUEST_TIMEOUT_SECONDS", "10"))


def is_spec_generation() -> bool:
    """
    Because we don't currently separate our API code from our serivce code we need to
    specify a run-time environment variable to determine if we are generating the OPENAPI spec
    or actually running the service.
    If SPEC_GENERATION=1, we are generating the spec and not setting up the server.
    """
    return os.getenv("SPEC_GENERATION", "0") == "1"


def get_pre_populator_schedule_interval() -> int:
    return int(os.getenv("PRE_POPULATOR_SCHEDULE_INTERVAL_SECONDS", "10"))
