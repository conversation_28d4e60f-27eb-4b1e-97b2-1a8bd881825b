"""
Tracing utilities for services.
"""

from opentelemetry import trace


def get_request_tracing_headers() -> dict[str, str]:
    """
    Get the tracing headers for the current request.

    This method is necessary as the AioHttpClientInstrumentor isn't
    properly instrumenting all aiohttp requests for some reason.
    """
    return {
        "x-b3-traceid": hex(trace.get_current_span().get_span_context().trace_id)[2:],
        "x-b3-spanid": hex(trace.get_current_span().get_span_context().span_id)[2:],
        "x-b3-sampled": "1"
        if trace.get_current_span().get_span_context().trace_flags.sampled
        else "0",
    }
