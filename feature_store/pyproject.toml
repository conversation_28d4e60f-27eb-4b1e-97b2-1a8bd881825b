[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "feature-store"
version = "0.0.0"  # This is set dynamically during the build process
description = "An online feature store for storing and retrieving features for our ML models"
authors = ["Apella Engineering <<EMAIL>>"]
readme = "README.md"

[tool.mypy]
strict = true

[[tool.mypy.overrides]]
module = [
    "clearml.*",
    "dill.*",
    "dagster_gcp.*",
    "dagster_slack.*",
    "docker.*",
    "google.cloud.bigtable.*",
    "google.cloud.storage.*",
    "google.rpc.*",
    "joblib.*",
    "matplotlib.*",
    "msgpack.*",
    "pandas.*",
    "pg8000.*",
    "pydantic._internal._model_construction",
    "sgqlc.*",
    "sklearn.*",
    "sksurv.*",
]
ignore_missing_imports = true

[tool.poetry.dependencies]
python = "^3.10"
msgpack = "^1.1.0"
# DATA-2052: Pin grpcio to a version that doesn't include this bug: https://github.com/grpc/grpc/issues/36265
# The bug in question seems to cause SecretManagerServiceClient and BigTable to randomly hang. It's
# should be addressed in the next release of grpcio after 1.62.1.
grpcio = "~1.67.1"
google-cloud-bigtable = "^2.29.0"
# Support Pandas 1 until realtime-dags can move off of it. Right now, it's stuck because the dynamic
# model can only be unpickled in a Pandas 1 environment. Once that's moved to a service, we can move
# realtime-dags forward and drop support for Pandas 1 in the feature store.
pandas = ">=1.5.3, <3.0.0"
pandera = "^0.23.1"

[tool.poetry.group.dev.dependencies]
docker = "^7.1.0"
mypy = "^1.15.0"
pytest = "^8.3.5"
pytest-cov = "^4.0.0"
ruff = "^0.11.9"
pandas-stubs = "^2.2.3.250308"

[tool.poetry-dynamic-versioning]
 # Add an indicator to the version string if the repo has uncommitted changes at the time of
 # publication. Useful for ensuring what gets published is definitely committed in the repo.
dirty = true 
enable = true
# This repo prefixes release tags with the name of the project, so we have to use a custom pattern.
pattern = "^feature-store-v((?P<epoch>\\d+)!)?(?P<base>\\d+(\\.\\d+)*)$"

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
    # Trust ruff format to get line length right. Without this, there are cases where it won't
    # reflow a line that's too long (e.g. comments) and ruff check complains.
    "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]
