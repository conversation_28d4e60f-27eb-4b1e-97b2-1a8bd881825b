import enum
from dataclasses import dataclass, field
from datetime import date, datetime
from enum import Enum
from typing import List

from pydantic import BaseModel


@dataclass
class VersionInfo:
    case_start_offset_model_version: str | None = None
    case_duration_model_version: str | None = None
    case_turnover_duration_model_version: str | None = None
    event_model_forecasts_model_version: str | None = None
    case_dynamic_end_model_version: str | None = None
    bayesian_case_duration_model_version: str | None = None


@dataclass
class ScheduledCaseInfo:
    case_id: str
    scheduled_start_time: datetime
    scheduled_end_time: datetime
    preceding_case_id: str
    version_info: VersionInfo = field(default_factory=VersionInfo)


class CaseStartSource(Enum):
    STATIC_START_OFFSET = "static_start_offset"
    TURNOVER = "turnover"
    FOLLOWING = "following"


@dataclass
class DebugInfo:
    static_start_offset_minutes: float | None = None
    turnover_duration_minutes: float | None = None
    static_duration_minutes: float | None = None
    pythia_duration_minutes: float | None = None
    bayesian_duration_minutes: float | None = None
    bayesian_duration_full_posterior: list[float] | None = None

    transformer_end_time: datetime | None = None
    pythia_end_time: datetime | None = None
    static_duration_end_time: datetime | None = None
    bayesian_duration_end_time: datetime | None = None

    is_auto_follow: bool = False
    is_overtime: bool = False

    pythia_prediction_tag: str | None = None
    case_start_source: CaseStartSource = CaseStartSource.STATIC_START_OFFSET


class ForecastVariant(enum.Enum):
    BAYESIAN_STATIC_FORECAST = "bayesian_static_forecast"


@dataclass
class ForecastedCaseOutput:
    case_id: str
    start_time: datetime
    end_time: datetime
    debug_info: DebugInfo = field(default_factory=DebugInfo)
    version_info: VersionInfo = field(default_factory=VersionInfo)


class RoomOutput(BaseModel):
    cases: list[ForecastedCaseOutput]


class Output(BaseModel):
    rooms: dict[str, RoomOutput]
    forecast_variant: ForecastVariant
    service_version: str


class Temporality(Enum):
    FUTURE = "future"
    DAY_OF = "day_of"


class APIInputs(BaseModel):
    site_id: str
    date: date
    forecast_variant: ForecastVariant = ForecastVariant.BAYESIAN_STATIC_FORECAST


class APIRequest(BaseModel):
    inputs: APIInputs


@dataclass
class PhaseInfo:
    case_id: str
    room_id: str
    start_time: datetime
    end_time: datetime | None


@dataclass
class ForecastedCase:
    case_id: str
    forecast_start_time: datetime
    forecast_end_time: datetime
    preceding_case_id: str

    debug_info: DebugInfo = field(default_factory=DebugInfo)
    version_info: VersionInfo = field(default_factory=VersionInfo)


@dataclass
class RoomCasesInfo:
    forecasts: List[ForecastedCase]
    room_id: str
