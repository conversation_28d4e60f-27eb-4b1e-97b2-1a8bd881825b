from dataclasses import InitVar, dataclass, field
from datetime import datetime, timedelta
from typing import List, Optional, Tuple

import pandas as pd

from forecast_combiner.types import (
    CaseStartSource,
    DebugInfo,
    ForecastedCase,
    ForecastVariant,
    PhaseInfo,
    RoomCasesInfo,
    ScheduledCaseInfo,
    VersionInfo,
)

MINUTES_TO_EXTEND_CURRENT_CASE = 5
MINUTES_MINIMUM_DYNAMIC_VALUE_ADDED = 2

VALID = "VALID"
INVALID = "INVALID"

BAYESIAN_FORECAST_VARIANTS = [ForecastVariant.BAYESIAN_STATIC_FORECAST]


def calculate_dynamic_end_time(
    static_end_time_prediction: datetime,
    pythia_end_time_prediction: datetime | None,
    xformer_end_time_prediction: datetime | None,
) -> datetime:
    return xformer_end_time_prediction or pythia_end_time_prediction or static_end_time_prediction


@dataclass
class ForecastModelCombiner:
    # TODO: allow for model outputs to be None and don't rely on 0 being no response
    case_duration_minutes_by_case_id: dict[str, float]
    case_start_offset_minutes_by_case_id: dict[str, float]
    case_turnover_minutes_by_case_id: dict[str, float]
    case_event_model_forecasts_time_by_case_id: dict[str, datetime]
    case_dynamic_end_minutes_by_case_id: dict[str, float]
    case_dynamic_end_pred_tag_by_case_id: dict[str, str]
    bayesian_case_duration_minutes_by_case_id: dict[str, float]
    bayesian_case_duration_full_posterior_by_case_id: dict[str, list[float]]
    cases: List[ScheduledCaseInfo]
    predicted_phases: InitVar[list[PhaseInfo]]
    forecast_variant: ForecastVariant
    predicted_phases_by_case_id: dict[str, PhaseInfo] = field(init=False)
    site_id: str
    case_scheduled_start_time_by_case_id: dict[str, datetime] = field(init=False)

    def __post_init__(self, predicted_phases: list[PhaseInfo]) -> None:
        self.predicted_phases_by_case_id = {p.case_id: p for p in predicted_phases}
        self.case_status = self.determine_case_status()
        self.case_actual_start_time_by_case_id = {}

        # Update actual start/end times for completed cases
        for case in self.cases:
            if self.case_status[case.case_id] == "COMPLETE":
                actual_start_time = self.__find_actual_start_end_time(case.case_id)
                self.case_actual_start_time_by_case_id[case.case_id] = actual_start_time

        self.case_scheduled_start_time_by_case_id = {
            case.case_id: case.scheduled_start_time for case in self.cases
        }
        # Define a priority for each status
        status_priority = {"COMPLETE": 0, "STARTED": 1, "NOT_STARTED": 2}

        self.sorted_cases = sorted(
            self.cases,
            key=lambda case_to_check: (
                status_priority[self.case_status[case_to_check.case_id]],
                self.case_actual_start_time_by_case_id.get(
                    case_to_check.case_id, case_to_check.scheduled_start_time
                ),
            ),
        )

    def generate_starting_schedule_for_room(self) -> List[ForecastedCase]:
        """
        Generate the initial schedule for a room based on static case durations and start offsets.

        This method creates a list of ForecastedCase objects for each case in the room. It uses
        the static case duration and start offset values, defaulting to scheduled times if these
        values are not present. The method also applies overlap removal and offset addition to
        ensure a feasible schedule.

        Returns:
            List[ForecastedCase]: A list of ForecastedCase objects representing the initial
            schedule for the room. If there are no cases, an empty list is returned.
        """
        if self.cases == []:
            return []

        forecasted_cases: list[ForecastedCase] = []
        for case in self.sorted_cases:
            bayesian_end_time = None
            static_end_time = None
            # scheduled_duration to be used as default
            scheduled_duration = case.scheduled_end_time - case.scheduled_start_time

            static_start_offset_minutes = (
                self.case_start_offset_minutes_by_case_id.get(case.case_id, 0) or 0
            )
            forecast_case_start = case.scheduled_start_time + timedelta(
                seconds=static_start_offset_minutes * 60
            )
            static_duration_minutes = self.case_duration_minutes_by_case_id.get(case.case_id, 0)
            forecast_case_end = forecast_case_start + timedelta(
                seconds=static_duration_minutes * 60
                if static_duration_minutes
                else scheduled_duration.total_seconds()
            )
            bayesian_end_time = forecast_case_end

            debug_info = DebugInfo(
                bayesian_duration_minutes=self.bayesian_case_duration_minutes_by_case_id.get(
                    case.case_id
                ),
                bayesian_duration_full_posterior=self.bayesian_case_duration_full_posterior_by_case_id.get(
                    case.case_id
                ),
                static_duration_end_time=static_end_time,
                bayesian_duration_end_time=bayesian_end_time,
                static_start_offset_minutes=static_start_offset_minutes,
            )
            this_forecast = ForecastedCase(
                forecast_start_time=forecast_case_start,
                forecast_end_time=forecast_case_end,
                case_id=case.case_id,
                preceding_case_id=case.preceding_case_id,
                debug_info=debug_info,
                version_info=case.version_info,
            )
            forecasted_cases.append(this_forecast)

        # correct for overlap
        forecasted_cases = ForecastModelCombiner.remove_overlap_and_add_offset(
            forecasted_cases, self.case_turnover_minutes_by_case_id
        )
        for forecast_case in forecasted_cases:
            if (
                self.forecast_variant in BAYESIAN_FORECAST_VARIANTS
                and forecast_case.debug_info.bayesian_duration_end_time
            ):
                forecast_case.debug_info.bayesian_duration_end_time = (
                    forecast_case.forecast_end_time
                )
            elif forecast_case.debug_info.static_duration_end_time:
                forecast_case.debug_info.static_duration_end_time = forecast_case.forecast_end_time

        return forecasted_cases

    def update_schedule_based_on_dynamic_model(
        self,
        forecasted_cases: list[ForecastedCase],
        current_time: datetime,
    ) -> list[ForecastedCase]:
        """
        Update the schedule based on dynamic model forecasts.

        This method updates the schedule for a room based on dynamic model forecasts. It adjusts
        the start and end times of cases according to the predicted phases and the event model
        forecasts. It also handles the case where a case is completed and needs to be set to the
        actual start and end times. It also extends the end time of the current case if it is within the allowed timeframe.

        Args:
            forecasted_cases (list[ForecastedCase]): The list of forecasted cases to update.
            current_time (datetime): The current time to use for dynamic model adjustments.
            case_event_model_forecasts_by_case_id (dict[str, datetime]): Dictionary mapping case_id to event model forecast.
            case_dynamic_end_by_case_id (dict[str, float]): Dictionary mapping case_id to dynamic end.

        Returns:
            List[ForecastedCase]: The updated list of forecasted cases with dynamic model adjustments.
        """
        # Cycle through the forecasted cases
        seen_first_incomplete_case = False
        dynamic_forecast_cases = []

        for forecasted_case in forecasted_cases:  # These are ordered by time
            if self.case_status[forecasted_case.case_id] == "COMPLETE":
                new_start_time, new_end_time = self.__find_actual_start_end_time(
                    forecasted_case.case_id
                )

                if new_start_time is not None:
                    forecasted_case.forecast_start_time = new_start_time
                if new_end_time is not None:
                    forecasted_case.forecast_end_time = new_end_time

                dynamic_forecast_cases.append(forecasted_case)

            elif self.case_status[forecasted_case.case_id] == "STARTED":
                # need to:
                # 1. set the start time to the actual time
                # 2. add the delta start times to the end time
                # 3. if there is a dynamic end time from pythia or the xformer event model, use that
                # 4. if end time is before current time, set to current time + 5

                new_start_time = self.predicted_phases_by_case_id[
                    forecasted_case.case_id
                ].start_time
                new_end_time = forecasted_case.forecast_end_time + (
                    new_start_time - forecasted_case.forecast_start_time
                )
                (
                    pythia_pred_minutes,
                    pythia_end_time_prediction,
                    xformer_end_time_prediction,
                ) = self.__find_dynamic_end_model_predictions(
                    new_start_time, forecasted_case.case_id
                )

                if self.forecast_variant in BAYESIAN_FORECAST_VARIANTS:
                    forecasted_case.debug_info.bayesian_duration_end_time = new_end_time
                else:
                    forecasted_case.debug_info.static_duration_end_time = new_end_time

                forecasted_case.debug_info.pythia_duration_minutes = pythia_pred_minutes
                forecasted_case.debug_info.pythia_end_time = pythia_end_time_prediction
                forecasted_case.debug_info.pythia_prediction_tag = (
                    self.case_dynamic_end_pred_tag_by_case_id.get(forecasted_case.case_id)
                )
                forecasted_case.debug_info.transformer_end_time = xformer_end_time_prediction

                dynamic_end_time = calculate_dynamic_end_time(
                    static_end_time_prediction=new_end_time,
                    pythia_end_time_prediction=pythia_end_time_prediction,
                    xformer_end_time_prediction=xformer_end_time_prediction,
                )

                # dynamic_end_time is never None but it IS possible that it is before current_time
                if dynamic_end_time > current_time:
                    new_end_time = dynamic_end_time

                if new_end_time <= current_time:  # Extend the current case
                    forecasted_case.debug_info.is_overtime = True
                    new_end_time = current_time + timedelta(minutes=MINUTES_TO_EXTEND_CURRENT_CASE)

                forecasted_case.forecast_start_time = new_start_time
                forecasted_case.forecast_end_time = new_end_time
                dynamic_forecast_cases.append(forecasted_case)

                # so we know that the next NOT_STARTED isn't the first non-complete case
                seen_first_incomplete_case = True

            elif self.case_status[forecasted_case.case_id] == "NOT_STARTED":
                # Here we update the forecasted start time to account for changes in the schedule caused by dynamic forecasts
                if forecasted_case.case_id in self.case_scheduled_start_time_by_case_id:
                    forecast_duration = (
                        forecasted_case.forecast_end_time - forecasted_case.forecast_start_time
                    )

                    static_start_offset_minutes = (
                        self.case_start_offset_minutes_by_case_id.get(forecasted_case.case_id, 0)
                        or 0
                    )
                    forecasted_case.forecast_start_time = self.case_scheduled_start_time_by_case_id[
                        forecasted_case.case_id
                    ] + timedelta(seconds=static_start_offset_minutes * 60)
                    forecasted_case.forecast_end_time = (
                        forecasted_case.forecast_start_time + forecast_duration
                    )
                    if (
                        self.forecast_variant in BAYESIAN_FORECAST_VARIANTS
                        and forecasted_case.debug_info.bayesian_duration_end_time
                    ):
                        forecasted_case.debug_info.bayesian_duration_end_time = (
                            forecasted_case.forecast_end_time
                        )
                    elif forecasted_case.debug_info.static_duration_end_time:
                        forecasted_case.debug_info.static_duration_end_time = (
                            forecasted_case.forecast_end_time
                        )

                    forecasted_case.debug_info.static_start_offset_minutes = (
                        static_start_offset_minutes
                    )

                if not seen_first_incomplete_case:
                    # need to:
                    # 1. check if the current time is past the forecasted time
                    #    yes: apply the MINUTES_TO_EXTEND_CURRENT_CASE to the start time
                    #    no: do nothing
                    if current_time >= forecasted_case.forecast_start_time:
                        forecasted_case.debug_info.is_overtime = True

                        new_start_time = current_time + timedelta(
                            minutes=MINUTES_TO_EXTEND_CURRENT_CASE
                        )
                        new_end_time = forecasted_case.forecast_end_time + (
                            new_start_time - forecasted_case.forecast_start_time
                        )

                        forecasted_case.forecast_start_time = new_start_time
                        forecasted_case.forecast_end_time = new_end_time
                        if (
                            self.forecast_variant in BAYESIAN_FORECAST_VARIANTS
                            and forecasted_case.debug_info.bayesian_duration_end_time
                        ):
                            forecasted_case.debug_info.bayesian_duration_end_time = new_end_time
                        elif forecasted_case.debug_info.static_duration_end_time:
                            forecasted_case.debug_info.static_duration_end_time = new_end_time

                        # so we know that the next NOT_STARTED isn't the first non-complete case
                        seen_first_incomplete_case = True
                dynamic_forecast_cases.append(forecasted_case)

        # Lastly, remove any overlap and reshuffle based on short turnovers
        dynamic_forecast_cases = ForecastModelCombiner.remove_overlap_and_add_offset(
            dynamic_forecast_cases,
            self.case_turnover_minutes_by_case_id,
            self.case_status,
            current_time,
        )
        return dynamic_forecast_cases

    def remove_completed_cases(
        self, forecasted_cases: list[ForecastedCase]
    ) -> list[ForecastedCase]:
        """
        Remove completed cases from the forecasted cases.
        """
        final_forecasts = [
            case for case in forecasted_cases if self.case_status[case.case_id] != "COMPLETE"
        ]
        return final_forecasts

    @staticmethod
    def remove_overlap_and_add_offset(
        cases: list[ForecastedCase],
        case_turnover_by_case_id: dict[str, float],
        case_status: Optional[dict[str, str]] = None,
        current_time: Optional[datetime] = None,
    ) -> list[ForecastedCase]:
        """
        Remove overlap and add minimum turnovers to the schedule.

        This method adjusts the schedule for cases to ensure there is no overlap and to add
        the minimum turnovers required between cases. It also handles the case where the
        preceding case ends early, causing subsequent cases to adjust their start times.

        Args:
            cases (list[ForecastedCase]): The list of forecasted cases to adjust.
            case_turnover_by_case_id (dict[str, float]): Dictionary mapping case_id to turnover duration.

        Returns:
            List[ForecastedCase]: The adjusted list of forecasted cases.
        """
        minimum_turnovers = ForecastModelCombiner.compute_minimum_turnovers(
            cases, case_turnover_by_case_id
        )

        # first_case_is_unchanging
        corrected_cases: list[ForecastedCase] = [cases[0]]
        prev_case_end_time = cases[0].forecast_end_time
        previous_case_id = cases[0].case_id
        min_turnover = minimum_turnovers[cases[0].case_id]

        for this_case in cases[1:]:
            # Do not adjust completed cases
            if case_status and case_status[this_case.case_id] == "COMPLETE":
                corrected_cases.append(this_case)
                continue

            additive_time = timedelta(minutes=0)

            time_between_cases = this_case.forecast_start_time - prev_case_end_time
            if time_between_cases < min_turnover:
                additive_time = min_turnover - time_between_cases
                this_case.debug_info.case_start_source = CaseStartSource.TURNOVER

            # This logic handles adjustments to the schedule for 'to follow' cases
            # when the preceding case ends early
            elif (
                time_between_cases > min_turnover
                and this_case.preceding_case_id == previous_case_id
            ):
                desired_new_forecast_start_time = prev_case_end_time + min_turnover
                # If the current time is not None, we don't want to forecast anything before it
                # This prevents a bug with auto follow cases
                if current_time is not None and desired_new_forecast_start_time < current_time:
                    desired_new_forecast_start_time = current_time

                additive_time = desired_new_forecast_start_time - this_case.forecast_start_time
                this_case.debug_info.is_auto_follow = True
                this_case.debug_info.case_start_source = CaseStartSource.FOLLOWING

            corrected_start_time = this_case.forecast_start_time + additive_time
            corrected_end_time = this_case.forecast_end_time + additive_time
            prev_case_end_time = corrected_end_time
            corrected_case = ForecastedCase(
                forecast_start_time=corrected_start_time,
                forecast_end_time=corrected_end_time,
                case_id=this_case.case_id,
                preceding_case_id=this_case.preceding_case_id,
                debug_info=this_case.debug_info,
                version_info=this_case.version_info,
            )
            corrected_cases.append(corrected_case)

            # set the minimum turnover before next iteration, as it is associated with this case
            min_turnover = minimum_turnovers[this_case.case_id]
            previous_case_id = this_case.case_id

        return corrected_cases

    @staticmethod
    def compute_minimum_turnovers(
        cases: list[ForecastedCase], case_turnover_by_case_id: dict[str, float]
    ) -> dict[str, timedelta]:
        """
        Compute the minimum turnovers for each case.

        This method calculates the minimum turnovers required between cases based on their
        forecasted start and end times. It also handles the case where no turnover is specified
        for a case, in which case it defaults to a minimum value based on the case duration.

        Logic:
        - if case is less than 30 minutes long then add 20 minutes
        - if case is less than 60 minutes long then add 25 minutes
        - else add 30 minutes

        EXCEPTION: right now we have some sites that don't have a forecast and they can scheduled
           things back to back. we don't want to correct anything for those.

        Args:
            cases (list[ForecastedCase]): The list of forecasted cases.
            case_turnover_by_case_id (dict[str, float]): Dictionary mapping case_id to turnover duration.

        Returns:
            dict[str, timedelta]: A dictionary with case_id as key and minimum_turnover as value.
        """
        minimum_turnovers = {}

        for case in cases:
            if (
                turnover_duration_minutes := case_turnover_by_case_id.get(case.case_id)
            ) is not None and turnover_duration_minutes >= 0:
                minimum_turnovers[case.case_id] = timedelta(seconds=turnover_duration_minutes * 60)
                case.debug_info.turnover_duration_minutes = turnover_duration_minutes
            else:
                case_duration_minutes = (
                    case.forecast_end_time - case.forecast_start_time
                ) / timedelta(minutes=1)

                if case_duration_minutes < 30:
                    min_val = 20.0
                elif case_duration_minutes < 60:
                    min_val = 25.0
                else:
                    min_val = 30.0
                minimum_turnovers[case.case_id] = timedelta(minutes=min_val)
                case.debug_info.turnover_duration_minutes = min_val

        return minimum_turnovers

    def determine_case_status(self) -> dict[str, str]:
        """
        Determine the status of each case.

        This method determines the status of each case based on the predicted phases.
        It returns a dictionary with case_id as key and case_status as value.

        Returns:
            dict[str, str]: A dictionary with case_id as key and case_status as value.
        """
        case_statuses = dict[str, str]()
        for case in self.cases:
            predicted_phase = self.predicted_phases_by_case_id.get(case.case_id)
            if predicted_phase is None:
                case_status = "NOT_STARTED"
            elif predicted_phase.end_time is None:
                case_status = "STARTED"
            else:
                case_status = "COMPLETE"

            case_statuses[case.case_id] = case_status

        return case_statuses

    def __find_actual_start_end_time(self, case_id: str) -> Tuple[datetime | None, datetime | None]:
        """
        Find the actual start and end time of a case.

        This method retrieves the actual start and end time of a case based on the predicted phases.
        It returns a tuple with the start and end times.

        Args:
            case_id (str): The ID of the case.

        Returns:
            Tuple[datetime | None, datetime | None]: A tuple with the start and end times.
        """
        predicted_phase = self.predicted_phases_by_case_id.get(case_id)

        if predicted_phase is None:
            return (None, None)

        return predicted_phase.start_time, predicted_phase.end_time

    def __find_dynamic_end_model_predictions(
        self, start_time: datetime, case_id: str
    ) -> Tuple[float | None, datetime | None, datetime | None]:
        """
        Find the dynamic end times of the cases.
        """
        pythia_pred_minutes = self.case_dynamic_end_minutes_by_case_id.get(case_id)
        pythia_end_time_prediction = (
            start_time + timedelta(seconds=pythia_pred_minutes * 60)
            if pythia_pred_minutes is not None
            else None
        )

        xformer_end_time_prediction = self.case_event_model_forecasts_time_by_case_id.get(case_id)

        return (pythia_pred_minutes, pythia_end_time_prediction, xformer_end_time_prediction)


def convert_df_to_combiner_inputs(
    df: pd.DataFrame,
) -> Tuple[
    dict[str, float],  # case_duration_minutes_by_case_id
    dict[str, float],  # case_start_offset_minutes_by_case_id
    dict[str, float],  # case_turnover_duration_minutes_by_case_id
    dict[str, datetime],  # case_event_model_forecasts_time_by_case_id
    dict[str, float],  # case_dynamic_end_minutes_by_case_id
    dict[str, str],  # case_dynamic_end_pred_tag_by_case_id
    dict[str, float],  # bayesian_case_duration_minutes_by_case_id
    dict[str, list[float]],  # bayesian_case_duration_full_posterior_by_case_id
    list[ScheduledCaseInfo],  # cases
]:
    """
    Convert a DataFrame to inputs required for the ForecastModelCombiner.

    This function processes a DataFrame containing case information and converts it into
    the required input formats for the ForecastModelCombiner.

    Args:
        df (pd.DataFrame): A DataFrame containing case information with columns:
            case_id, case_duration, start_offset, turnover_duration, event_model_forecast,
            dynamic_case_end, scheduled_start_time, scheduled_end_time, preceding_case_id,
            start_offset_version, case_duration_version, turnover_duration_version,
            event_model_forecast_version, dynamic_case_end_version.

    Returns:
        Tuple[dict, dict, dict, dict, dict, dict, list]:
            - case_duration_minutes_by_case_id: Dictionary mapping case_id to case duration.
            - case_start_offset_minutes_by_case_id: Dictionary mapping case_id to start offset.
            - case_turnover_duration_minutes_by_case_id: Dictionary mapping case_id to turnover duration.
            - case_event_model_forecasts_time_by_case_id: Dictionary mapping case_id to event model forecast.
            - case_dynamic_end_minutes_by_case_id: Dictionary mapping case_id to dynamic case end.
            - case_dynamic_end_pred_tag_by_case_id: Dictionary mapping case_id to dynamic case end prediction tag.
            - cases: List of ScheduledCaseInfo objects.
    """
    # setup inputs
    case_duration_minutes_by_case_id = dict(zip(df.case_id, df.case_duration))
    case_start_offset_minutes_by_case_id = dict(zip(df.case_id, df.start_offset))
    case_turnover_duration_minutes_by_case_id = dict(zip(df.case_id, df.turnover_duration))
    case_event_model_forecasts_time_by_case_id = dict(zip(df.case_id, df.event_model_forecast))
    case_dynamic_end_minutes_by_case_id = dict(zip(df.case_id, df.dynamic_case_end))
    case_dynamic_end_pred_tag_by_case_id = dict(zip(df.case_id, df.dynamic_case_end_prediction_tag))
    bayesian_case_duration_minutes_by_case_id = dict(zip(df.case_id, df.bayesian_case_duration))
    bayesian_case_duration_full_posterior_by_case_id = dict(
        zip(df.case_id, df.bayesian_case_duration_original)
    )

    cases = []
    for _, x in df[
        [
            "case_id",
            "scheduled_start_time",
            "scheduled_end_time",
            "preceding_case_id",
            "start_offset_version",
            "turnover_duration_version",
            "event_model_forecast_version",
            "dynamic_case_end_version",
            "bayesian_case_duration_version",
        ]
    ].iterrows():
        cases.append(
            ScheduledCaseInfo(
                case_id=x["case_id"],
                scheduled_start_time=x["scheduled_start_time"],
                scheduled_end_time=x["scheduled_end_time"],
                preceding_case_id=x["preceding_case_id"],
                version_info=VersionInfo(
                    case_start_offset_model_version=x["start_offset_version"],
                    case_turnover_duration_model_version=x["turnover_duration_version"],
                    event_model_forecasts_model_version=x["event_model_forecast_version"],
                    case_dynamic_end_model_version=x["dynamic_case_end_version"],
                    bayesian_case_duration_model_version=x["bayesian_case_duration_version"],
                ),
            )
        )

    return (
        case_duration_minutes_by_case_id,
        case_start_offset_minutes_by_case_id,
        case_turnover_duration_minutes_by_case_id,
        case_event_model_forecasts_time_by_case_id,
        case_dynamic_end_minutes_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_minutes_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
    )


def forecast_for_room(
    room_id: str,
    site_id: str,
    now: datetime,
    room_df: pd.DataFrame,
    predicted_phases: list[PhaseInfo],
    forecast_variant: ForecastVariant,
) -> RoomCasesInfo:
    """
    Generate a forecast for a specific room based on multiple prediction models.

    This function combines outputs from various models to create a comprehensive forecast:
    - Case duration model
    - Static start offset model
    - Turnover model
    - Event model forecasts
    - Dynamic case end model

    The function applies these models in a specific order and under certain conditions:

    1. It starts by computing a static model for all cases in the room.
    2. It then checks if the current case hasn't started but was expected to:
       - If yes: it calculates an offset and applies it to all subsequent cases that day.
       - If no: no action is taken.
    3. It checks if there's a case currently in progress:
       - If yes: it calculates a time shift for the rest of the schedule, checks for a dynamic
         end time, and applies shifts to all subsequent cases that day.
       - If no: no action is taken.

    The MINUTES_TO_EXTEND_CURRENT_CASE is used when:
    1. The current time is after the scheduled start time of the case.
    2. The case hasn't started yet.
    In this situation, it's only applied to the affected case.

    For in-progress cases, the function will:
    1. Shift the end estimate (and subsequent start/end times) by the difference between
       the predicted start time and the actual start time.
    2. Update the end time based on the forecast provided by the event model (if available).

    Args:
        room_id (str): Identifier for the room.
        site_id (str): Identifier for the site.
        now (datetime): The current time.
        room_df (pd.DataFrame): DataFrame containing case information for the room.
        predicted_phases (list[PhaseInfo]): List of predicted phase information.
        forecast_variant (ForecastVariant): The forecasting variant to use (e.g., Bayesian or static).

    Returns:
        RoomCasesInfo: Object containing the forecasted cases for the room.
    """
    # If there are no cases in this room, don't do anything else.
    if len(room_df) == 0:
        return RoomCasesInfo([], room_id)

    (
        case_duration_minutes_by_case_id,
        case_start_offset_minutes_by_case_id,
        case_turnover_minutes_by_case_id,
        case_event_model_forecasts_time_by_case_id,
        case_dynamic_end_minutes_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_minutes_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
    ) = convert_df_to_combiner_inputs(room_df)

    # Start with the static model:
    forecast_model_combiner = ForecastModelCombiner(
        case_duration_minutes_by_case_id=case_duration_minutes_by_case_id,
        case_start_offset_minutes_by_case_id=case_start_offset_minutes_by_case_id,
        case_turnover_minutes_by_case_id=case_turnover_minutes_by_case_id,
        case_event_model_forecasts_time_by_case_id=case_event_model_forecasts_time_by_case_id,
        case_dynamic_end_minutes_by_case_id=case_dynamic_end_minutes_by_case_id,
        case_dynamic_end_pred_tag_by_case_id=case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_minutes_by_case_id=bayesian_case_duration_minutes_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id=bayesian_case_duration_full_posterior_by_case_id,
        cases=cases,
        predicted_phases=predicted_phases,
        site_id=site_id,
        forecast_variant=forecast_variant,
    )

    forecasted_cases = forecast_model_combiner.generate_starting_schedule_for_room()

    # apply the dynamic model
    dynamic_forecast_cases = forecast_model_combiner.update_schedule_based_on_dynamic_model(
        forecasted_cases,
        now,
    )

    # convert them for backwards compatibility -- and remove the ones that are completed.
    # map_forecasts_for_room() relies on these forecasts being missing in order to mark them as
    # INVALID in the API server.
    final_forecasts = forecast_model_combiner.remove_completed_cases(dynamic_forecast_cases)
    return RoomCasesInfo(final_forecasts, room_id)
