from datetime import datetime, timedelta, timezone

import pytest

from forecast_combiner.forecast_model_combiner import (
    ForecastModelCombiner,
    calculate_dynamic_end_time,
)
from forecast_combiner.types import (
    CaseStartSource,
    DebugInfo,
    ForecastedCase,
    ForecastVariant,
    ScheduledCaseInfo,
    VersionInfo,
)


@pytest.mark.parametrize(
    "current_time,static_end_time,pythia_pred,xformer_pred,site_id,expected",
    [
        # Test case: No pythia/xformer predictions, return static prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            None,
            None,
            "HMH-DUNN03",
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return Pythia prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, 5, tzinfo=timezone.utc),
            None,
            "HMH-DUNN03",
            datetime(2023, 1, 1, 3, 5, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return Pythia prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 2, tzinfo=timezone.utc),
            None,
            "HMH-DUNN03",
            datetime(2023, 1, 1, 2, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, xformer prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 3, 30, tzinfo=timezone.utc),
            "HMH-DUNN03",
            datetime(2023, 1, 1, 3, 30, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, xformer prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
            "HMH-DUNN03",
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 30, tzinfo=timezone.utc),
            "HMH-DUNN03",
            datetime(2023, 1, 1, 0, 30, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 2, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
            "HMH-DUNN03",
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 3, 5, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
            "HMH-DUNN03",
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
        ),
    ],
)
def test_calculate_dynamic_end_time_base_case(
    current_time: datetime,
    static_end_time: datetime,
    pythia_pred: datetime | None,
    xformer_pred: datetime | None,
    site_id: str,
    expected: datetime,
) -> None:
    result = calculate_dynamic_end_time(static_end_time, pythia_pred, xformer_pred)
    assert result == expected


@pytest.mark.parametrize(
    "current_time,static_end_time,pythia_pred,xformer_pred,site_id,expected",
    [
        # Test case: No pythia/xformer predictions, return static prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            None,
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return static prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
            None,
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return pythia prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            None,
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, xformer prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, xformer prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 35, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
            "HMH-HMBT-OR",
            datetime(2023, 1, 1, 0, 55, tzinfo=timezone.utc),
        ),
    ],
)
def test_calculate_dynamic_end_time_static_pythia_threshold_less_than_pythia_xformer_threshold(
    current_time: datetime,
    static_end_time: datetime,
    pythia_pred: datetime | None,
    xformer_pred: datetime | None,
    site_id: str,
    expected: datetime,
) -> None:
    result = calculate_dynamic_end_time(static_end_time, pythia_pred, xformer_pred)
    assert result == expected


@pytest.mark.parametrize(
    "current_time,static_end_time,pythia_pred,xformer_pred,site_id,expected",
    [
        # Test case: No pythia/xformer predictions, return static prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            None,
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return Pythia prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
            None,
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
        ),
        # Test case: No xformer prediction, Pythia prediction, return pythia prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            None,
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, xformer prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 45, tzinfo=timezone.utc),
        ),
        # Test case: No pythia prediction, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            None,
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 25, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
        ),
        # Test case: Both predictions, return xformer prediction
        (
            datetime(2023, 1, 1, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 50, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 35, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
            "HMH-HMW-LD",
            datetime(2023, 1, 1, 0, 15, tzinfo=timezone.utc),
        ),
    ],
)
def test_calculate_dynamic_end_time_xformer_turned_off(
    current_time: datetime,
    static_end_time: datetime,
    pythia_pred: datetime | None,
    xformer_pred: datetime | None,
    site_id: str,
    expected: datetime,
) -> None:
    result = calculate_dynamic_end_time(static_end_time, pythia_pred, xformer_pred)
    assert result == expected


@pytest.mark.parametrize(
    "cases,expected_sources",
    [
        # Test case 1: Case following previous case
        (
            [
                ForecastedCase(
                    forecast_start_time=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    forecast_end_time=datetime(2023, 1, 1, 11, 0, tzinfo=timezone.utc),
                    case_id="case1",
                    preceding_case_id=None,
                    debug_info=DebugInfo(),
                    version_info=VersionInfo(),
                ),
                ForecastedCase(
                    forecast_start_time=datetime(
                        2023, 1, 1, 11, 30, tzinfo=timezone.utc
                    ),  # This will be adjusted
                    forecast_end_time=datetime(2023, 1, 1, 12, 30, tzinfo=timezone.utc),
                    case_id="case2",
                    preceding_case_id="case1",
                    debug_info=DebugInfo(),
                    version_info=VersionInfo(),
                ),
            ],
            [CaseStartSource.STATIC_START_OFFSET, CaseStartSource.FOLLOWING],
        ),
        # Test case 2: Case needing turnover time
        (
            [
                ForecastedCase(
                    forecast_start_time=datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc),
                    forecast_end_time=datetime(2023, 1, 1, 11, 0, tzinfo=timezone.utc),
                    case_id="case1",
                    preceding_case_id=None,
                    debug_info=DebugInfo(),
                    version_info=VersionInfo(),
                ),
                ForecastedCase(
                    forecast_start_time=datetime(
                        2023, 1, 1, 11, 0, tzinfo=timezone.utc
                    ),  # Overlapping with case1
                    forecast_end_time=datetime(2023, 1, 1, 12, 0, tzinfo=timezone.utc),
                    case_id="case2",
                    preceding_case_id=None,
                    debug_info=DebugInfo(),
                    version_info=VersionInfo(),
                ),
            ],
            [CaseStartSource.STATIC_START_OFFSET, CaseStartSource.TURNOVER],
        ),
    ],
)
def test_case_start_source_remove_overlap_and_add_offset(
    cases: list[ForecastedCase], expected_sources: list[CaseStartSource | None]
) -> None:
    """Test different case start source scenarios.

    Tests three scenarios:
    1. A case following a previous case (FOLLOWING)
    2. A case needing turnover time (TURNOVER)
    """
    # Create a combiner with minimal required inputs
    combiner = ForecastModelCombiner(
        case_duration_minutes_by_case_id={},
        case_start_offset_minutes_by_case_id={},
        case_turnover_minutes_by_case_id={},
        case_event_model_forecasts_time_by_case_id={},
        case_dynamic_end_minutes_by_case_id={},
        case_dynamic_end_pred_tag_by_case_id={},
        bayesian_case_duration_minutes_by_case_id={},
        bayesian_case_duration_full_posterior_by_case_id={},
        cases=[],
        predicted_phases=[],
        site_id="test-site",
        forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
    )

    # Process the cases
    result = combiner.remove_overlap_and_add_offset(
        cases,
        {
            "case1": 10,
        },
    )

    # Verify the case start sources
    for case, expected_source in zip(result, expected_sources):
        assert case.debug_info.case_start_source == expected_source


def test_generate_starting_schedule_for_room_static_start_offset() -> None:
    """Test that generate_starting_schedule_for_room sets STATIC_START_OFFSET for all cases."""
    # Create test data
    case_id = "test_case"
    scheduled_start = datetime(2023, 1, 1, 10, 0, tzinfo=timezone.utc)
    scheduled_end = datetime(2023, 1, 1, 11, 0, tzinfo=timezone.utc)
    start_offset = 15  # 15 minutes offset

    # Create a combiner with test data
    combiner = ForecastModelCombiner(
        case_duration_minutes_by_case_id={case_id: 60},  # 60 minutes duration
        case_start_offset_minutes_by_case_id={case_id: start_offset},
        case_turnover_minutes_by_case_id={},
        case_event_model_forecasts_time_by_case_id={},
        case_dynamic_end_minutes_by_case_id={},
        case_dynamic_end_pred_tag_by_case_id={},
        bayesian_case_duration_minutes_by_case_id={},
        bayesian_case_duration_full_posterior_by_case_id={},
        cases=[
            ScheduledCaseInfo(
                case_id=case_id,
                scheduled_start_time=scheduled_start,
                scheduled_end_time=scheduled_end,
                preceding_case_id="",
                version_info=VersionInfo(),
            )
        ],
        predicted_phases=[],
        site_id="test-site",
        forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
    )

    # Generate the schedule
    result = combiner.generate_starting_schedule_for_room()

    # Verify results
    assert len(result) == 1
    case = result[0]

    # Verify the case start source is STATIC_START_OFFSET
    assert case.debug_info.case_start_source == CaseStartSource.STATIC_START_OFFSET

    # Verify the start time was adjusted by the offset
    expected_start = scheduled_start + timedelta(minutes=start_offset)
    assert case.forecast_start_time == expected_start

    # Verify the end time was adjusted accordingly
    expected_end = expected_start + timedelta(minutes=60)  # 60 minutes duration
    assert case.forecast_end_time == expected_end
