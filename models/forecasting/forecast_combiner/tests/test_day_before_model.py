from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Tuple

import pandas as pd
import pytest
from zoneinfo import ZoneInfo

from forecast_combiner.forecast_model_combiner import (
    ForecastModelCombiner,
    convert_df_to_combiner_inputs,
)
from forecast_combiner.types import (
    CaseStartSource,
    DebugInfo,
    ForecastedCase,
    ForecastVariant,
    VersionInfo,
)

la_time = ZoneInfo("America/Los_Angeles")
# pd.set_option("display.max_columns", None)


################################################################################################
# What follows are functions to help with testing the actual business logic of these functions #
################################################################################################
@dataclass
class TestApellaCase:
    id: str
    sched_start_time: str
    sched_end_time: str
    duration_minutes: float
    start_offset_minutes: float
    turnover_duration_minutes: float | None = None
    following_case_id: str = ""
    preceding_case_id: str = ""

    def __post_init__(self) -> None:
        start_time = datetime.strptime(self.sched_start_time, "%H:%M")
        self.start_datetime: datetime = datetime(
            2022, 10, 1, start_time.hour, start_time.minute, tzinfo=la_time
        )

        end_time = datetime.strptime(self.sched_end_time, "%H:%M")
        self.end_datetime: datetime = datetime(
            2022, 10, 1, end_time.hour, end_time.minute, tzinfo=la_time
        )

        if self.duration_minutes is not None:
            self.forecasted_duration = self.duration_minutes

        if self.start_offset_minutes is not None:
            self.forecasted_start_offset = self.start_offset_minutes

        if self.turnover_duration_minutes is not None:
            self.turnover_duration_minutes = self.turnover_duration_minutes


def generate_inputs_for_test(
    cases: list[TestApellaCase],
    forecast_variant: ForecastVariant = ForecastVariant.BAYESIAN_STATIC_FORECAST,
) -> Tuple[
    str,  # room_id
    str,  # site_id
    datetime,  # now
    pd.DataFrame,  # room_df
]:
    room_id = "Room"
    site_id = "SITE"
    now = datetime(2022, 9, 28, 0, 0, tzinfo=la_time)

    # Function to take the TestApellaCases and turn them into the inputs of forecast_for_room
    room_info = []
    for case in cases:
        this_entry = {
            "case_id": case.id,
            "scheduled_start_time": case.start_datetime,
            "scheduled_end_time": case.end_datetime,
            "xgboost_case_duration": case.forecasted_duration + 10,
            "xgboost_case_duration_version": "v1.1.1",
            "start_offset": case.forecasted_start_offset,
            "start_offset_version": "v1.2.3",
            "turnover_duration_version": "v2.3.4",
            "turnover_duration": case.turnover_duration_minutes,
            "event_model_forecast": None,
            "event_model_forecast_version": "v3.4.5",
            "following_case_id": case.following_case_id,
            "preceding_case_id": case.preceding_case_id,
            "dynamic_case_end": None,
            "dynamic_case_end_version": "v4.5.6",
            "dynamic_case_end_prediction_tag": None,
            "case_duration": case.forecasted_duration,
            "bayesian_case_duration": case.forecasted_duration,
            "bayesian_case_duration_original": [
                case.forecasted_duration - 10,
                case.forecasted_duration,
                case.forecasted_duration + 20,
            ],
            "bayesian_case_duration_version": "v5.6.7",
        }
        if forecast_variant == ForecastVariant.BAYESIAN_STATIC_FORECAST:
            this_entry["case_duration"] = this_entry["bayesian_case_duration"]
        room_info.append(this_entry)
    room_df = pd.DataFrame(room_info)

    return room_id, site_id, now, room_df


@pytest.fixture
def sample_future_forecast_generator() -> ForecastModelCombiner:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=120,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        duration_minutes=100,
        start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        duration_minutes=90,
        start_offset_minutes=0,
    )

    room_id, site_id, now, room_df = generate_inputs_for_test([caseA, caseB, caseC])

    (
        case_duration_minutes_by_case_id,
        case_start_offset_by_case_id,
        case_turnover_duration_by_case_id,
        case_event_model_forecasts_by_case_id,
        case_dynamic_end_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
    ) = convert_df_to_combiner_inputs(room_df)

    forecast_generator = ForecastModelCombiner(
        case_duration_minutes_by_case_id,
        case_start_offset_by_case_id,
        case_turnover_duration_by_case_id,
        case_event_model_forecasts_by_case_id,
        case_dynamic_end_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
        [],
        ForecastVariant.BAYESIAN_STATIC_FORECAST,
        site_id,
    )
    return forecast_generator


def test_compute_minimum_turnovers(
    sample_future_forecast_generator: ForecastModelCombiner,
) -> None:
    # make cases with varying lengths: 5, 15, 35, 65 minutes

    cases = [
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8),
            forecast_end_time=datetime(2022, 11, 1, 8, 5),
            preceding_case_id="",
            case_id="a",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 5),
            forecast_end_time=datetime(2022, 11, 1, 8, 20),
            preceding_case_id="",
            case_id="b",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 15),
            forecast_end_time=datetime(2022, 11, 1, 8, 50),
            preceding_case_id="",
            case_id="c",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 45),
            forecast_end_time=datetime(2022, 11, 1, 10, 10),
            preceding_case_id="",
            case_id="d",
        ),
    ]

    min_durations = sample_future_forecast_generator.compute_minimum_turnovers(cases, {})
    assert min_durations == {
        "a": timedelta(minutes=20),
        "b": timedelta(minutes=20),
        "c": timedelta(minutes=25),
        "d": timedelta(minutes=30),
    }


def test_compute_minimum_turnover_for_exceptions(
    sample_future_forecast_generator: ForecastModelCombiner,
) -> None:
    # make cases with varying lengths: 5, 15, 35, 65 minutes

    cases = [
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8),
            forecast_end_time=datetime(2022, 11, 1, 8, 5),
            preceding_case_id="",
            case_id="a",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 5),
            forecast_end_time=datetime(2022, 11, 1, 8, 20),
            preceding_case_id="",
            case_id="b",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 15),
            forecast_end_time=datetime(2022, 11, 1, 8, 50),
            preceding_case_id="",
            case_id="c",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 45),
            forecast_end_time=datetime(2022, 11, 1, 10, 10),
            preceding_case_id="",
            case_id="d",
        ),
    ]

    min_durations = sample_future_forecast_generator.compute_minimum_turnovers(cases, {})
    assert min_durations == {
        "a": timedelta(minutes=20),
        "b": timedelta(minutes=20),
        "c": timedelta(minutes=25),
        "d": timedelta(minutes=30),
    }


def test_remove_overlap() -> None:
    # case 1: 8:00-8:05 (need 20 min turnover)
    # case 2: 8:05-8:20 (need 20 min turnover)
    # case 3: 8:15-8:50 (need 25 min turnover)
    # case 4: 8:45-10:10 (need 30 min turnover)
    # case 5: 10:30-13:00 (need 30 min turnover)
    # case 6: 15:00-17:00 (need 30 min)
    cases = [
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8),
            forecast_end_time=datetime(2022, 11, 1, 8, 5),
            preceding_case_id="",
            case_id="a",
            version_info=VersionInfo(
                case_start_offset_model_version="",
                case_duration_model_version="",
                case_turnover_duration_model_version="",
                event_model_forecasts_model_version="",
                case_dynamic_end_model_version="",
            ),
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 5),
            forecast_end_time=datetime(2022, 11, 1, 8, 20),
            preceding_case_id="",
            case_id="b",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 15),
            forecast_end_time=datetime(2022, 11, 1, 8, 50),
            preceding_case_id="",
            case_id="c",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 8, 45),
            forecast_end_time=datetime(2022, 11, 1, 10, 10),
            preceding_case_id="",
            case_id="d",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 10, 30),
            forecast_end_time=datetime(2022, 11, 1, 13, 0),
            preceding_case_id="",
            case_id="e",
        ),
        ForecastedCase(
            forecast_start_time=datetime(2022, 11, 1, 15, 0),
            forecast_end_time=datetime(2022, 11, 1, 17, 0),
            preceding_case_id="",
            case_id="f",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=None,
                is_overtime=False,
            ),
            version_info=VersionInfo(
                case_start_offset_model_version="v1.2.3",
                case_duration_model_version="v1.1.1",
                case_turnover_duration_model_version="v2.3.4",
                event_model_forecasts_model_version="v3.4.5",
                case_dynamic_end_model_version="v4.5.6",
            ),
        ),
    ]

    # expectation:
    # case 1: 8:00-8:05
    # case 2: 8:25-8:40 (added 20)
    # case 3: 9:00-9:35 (added 20)
    # case 4: 10:00-12:25 (added 25)
    # case 5: 12:55-15:25 (added 30)
    # case 6: 15:00-17:00 (no need to add anything)
    expected_cases = [
        ForecastedCase(
            case_id="a",
            forecast_start_time=datetime(2022, 11, 1, 8, 0),
            forecast_end_time=datetime(2022, 11, 1, 8, 5),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=20.0,
                is_overtime=False,
            ),
            version_info=VersionInfo(
                case_start_offset_model_version="",
                case_duration_model_version="",
                case_turnover_duration_model_version="",
                event_model_forecasts_model_version="",
                case_dynamic_end_model_version="",
            ),
        ),
        ForecastedCase(
            case_id="b",
            forecast_start_time=datetime(2022, 11, 1, 8, 25),
            forecast_end_time=datetime(2022, 11, 1, 8, 40),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=20.0,
                is_overtime=False,
                case_start_source=CaseStartSource.TURNOVER,
            ),
        ),
        ForecastedCase(
            case_id="c",
            forecast_start_time=datetime(2022, 11, 1, 9, 0),
            forecast_end_time=datetime(2022, 11, 1, 9, 35),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=25.0,
                is_overtime=False,
                case_start_source=CaseStartSource.TURNOVER,
            ),
        ),
        ForecastedCase(
            case_id="d",
            forecast_start_time=datetime(2022, 11, 1, 10, 0),
            forecast_end_time=datetime(2022, 11, 1, 11, 25),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=30.0,
                is_overtime=False,
                case_start_source=CaseStartSource.TURNOVER,
            ),
        ),
        ForecastedCase(
            case_id="e",
            forecast_start_time=datetime(2022, 11, 1, 11, 55),
            forecast_end_time=datetime(2022, 11, 1, 14, 25),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=30.0,
                is_overtime=False,
                case_start_source=CaseStartSource.TURNOVER,
            ),
        ),
        ForecastedCase(
            case_id="f",
            forecast_start_time=datetime(2022, 11, 1, 15, 0),
            forecast_end_time=datetime(2022, 11, 1, 17, 0),
            preceding_case_id="",
            debug_info=DebugInfo(
                static_duration_minutes=None,
                static_start_offset_minutes=None,
                turnover_duration_minutes=30.0,
                is_overtime=False,
            ),
            version_info=VersionInfo(
                case_start_offset_model_version="v1.2.3",
                case_duration_model_version="v1.1.1",
                case_turnover_duration_model_version="v2.3.4",
                event_model_forecasts_model_version="v3.4.5",
                case_dynamic_end_model_version="v4.5.6",
            ),
        ),
    ]
    results = ForecastModelCombiner.remove_overlap_and_add_offset(cases, {})
    assert results == expected_cases


def forecast_combiner_generator(
    test_cases: list[TestApellaCase],
    forecast_variant: ForecastVariant = ForecastVariant.BAYESIAN_STATIC_FORECAST,
) -> ForecastModelCombiner:
    _, site_id, _, room_df = generate_inputs_for_test(test_cases, forecast_variant=forecast_variant)
    (
        case_duration_minutes_by_case_id,
        start_offsets,
        turnovers,
        case_event_model_forecasts_by_case_id,
        case_dynamic_end_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
    ) = convert_df_to_combiner_inputs(room_df)

    return ForecastModelCombiner(
        case_duration_minutes_by_case_id,
        start_offsets,
        turnovers,
        case_event_model_forecasts_by_case_id,
        case_dynamic_end_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
        [],
        forecast_variant,
        site_id,
    )


##############################
# Scenario 0:  We have a mix and match of results (as in, they don't exist in the model values)
# case A scheduled  8:00- 9:30; duration of 2:00 hours, start offset missing
# case B scheduled 10:30-11:30, duration missing, start_offset 15
# case C scheduled 13:00-15:00, duration missing, start offset missing
#
# expected:
# case A  8:00-10:00
# case B 10:45-11:45 (repeat 60 minutes from schedule)
# case C 13:00-15:00 (repeat schedule)
def test_generate_schedule_missing_values() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=120,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        duration_minutes=100,
        start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        duration_minutes=90,
        start_offset_minutes=0,
    )

    room_id, site_id, now, room_df = generate_inputs_for_test([caseA, caseB, caseC])
    _, _, _, _, _, _, _, _, cases = convert_df_to_combiner_inputs(room_df)

    # Set the values of the duration / start_offsets
    case_duration_minutes_by_case_id = {"a": 120.0}
    start_offsets = {"b": 15.0}
    turnovers = {"c": 15.0}
    case_event_model_forecasts_by_case_id: dict[str, datetime] = {}
    case_dynamic_end_by_case_id: dict[str, float] = {}
    case_dynamic_end_pred_tag_by_case_id: dict[str, str] = {}
    bayesian_case_duration_by_case_id: dict[str, float] = {"a": 120.0}
    bayesian_case_duration_full_posterior_by_case_id: dict[str, list[float]] = {}

    forecast_generator = ForecastModelCombiner(
        case_duration_minutes_by_case_id,
        start_offsets,
        turnovers,
        case_event_model_forecasts_by_case_id,
        case_dynamic_end_by_case_id,
        case_dynamic_end_pred_tag_by_case_id,
        bayesian_case_duration_by_case_id,
        bayesian_case_duration_full_posterior_by_case_id,
        cases,
        [],
        ForecastVariant.BAYESIAN_STATIC_FORECAST,
        site_id,
    )

    results = forecast_generator.generate_starting_schedule_for_room()
    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 10, 0, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 45, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 45, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 15, 0, tzinfo=la_time)


##############################
# Scenario 1:  Happy Path (no overlap, some variation)
# case A scheduled  8:00- 9:30; duration of 2:00 hours
# case B scheduled 10:30-11:30, duration of 1:40 hour
# case C scheduled 13:00-15:00, duration of 1:30 hours
#
# expected:
# case A  8:00-10:00
# case B 10:30-12:10
# case C 13:00-14:30
def test_generate_schedule_for_room_1() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=120,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:30",
        sched_end_time="11:30",
        duration_minutes=100,
        start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="13:00",
        sched_end_time="15:00",
        duration_minutes=90,
        start_offset_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()
    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 10, 0, tzinfo=la_time)
    assert results[0].version_info.case_start_offset_model_version == "v1.2.3"
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 30, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 12, 10, tzinfo=la_time)
    assert results[1].version_info.bayesian_case_duration_model_version == "v5.6.7"
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)


##############################
# Scenario 2: (Surgeons faster than schedule)
# case A scheduled  8:00- 9:30; duration of 1:15 hours, start_offset 0
# case B scheduled 10:00-11:30, duration of 1:20 hour, start_offset -15
# case C scheduled 12:30-14:30, duration of 1:30 hours, start_offset -20
#
# expected:
# case A  8:00-09:15
# case B  9:45-11:05
# case C 12:10-13:40
##############################
def test_scenario2() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=75,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:00",
        sched_end_time="11:30",
        duration_minutes=80,
        start_offset_minutes=-15,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="12:30",
        sched_end_time="14:30",
        duration_minutes=90,
        start_offset_minutes=-20,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 15, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 9, 45, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 5, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 12, 10, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 13, 40, tzinfo=la_time)


##############################
# Scenario 3: (Surgeons slower than schedule)
# case A scheduled  8:00- 9:30; duration of 2:00 hours, start_offset 10
# case B scheduled 10:00-11:30, duration of 1:45 hour, start_offset 20
# case C scheduled 12:30-14:30, duration of 1:45 hours, start_offset +30
#
# expected:
# case A  8:10-10:10
# case B 10:20-12:05 / 10:40-12:25 when adding turnover
# case C 13:00-14:45
##############################
def test_scenario3() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=120,
        start_offset_minutes=10,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:00",
        sched_end_time="11:30",
        duration_minutes=105,
        start_offset_minutes=20,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="12:30",
        sched_end_time="14:30",
        duration_minutes=105,
        start_offset_minutes=30,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 10, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 10, 10, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 40, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 12, 25, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 13, 00, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 14, 45, tzinfo=la_time)


##############################
# Scenario 4: (First case eaerly, rest are slow)
# case A scheduled  8:00- 9:30; duration of 2:00 hours, start_offset -10
# case B scheduled 10:00-11:30, duration of 1:45 hour, start_offset 5
# case C scheduled 12:30-14:00, duration of 2:00 hours, start_offset +20
#
# expected:
# case A  7:50- 9:50
# case B 10:05-11:50 / 10:20-12:05 (when adding turnover)
# case C 12:50-14:50
##############################
def test_scenario4() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=120,
        start_offset_minutes=-10,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="10:00",
        sched_end_time="11:30",
        duration_minutes=105,
        start_offset_minutes=5,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="12:30",
        sched_end_time="14:00",
        duration_minutes=120,
        start_offset_minutes=20,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 7, 50, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 50, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 20, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 12, 5, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 12, 50, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 14, 50, tzinfo=la_time)


##############################
# Scenario 5: (Long breaks and cases go long)
# case A scheduled  8:00- 9:30; duration of 2:30 hours, start_offset 0
# case B scheduled 11:00-13:00, duration of 3:00 hour, start_offset 0
# case C scheduled 14:30-15:30, duration of 1:00 hours, start_offset 0
#
# expected:
# case A  8:00-10:30
# case B 11:00-14:00
# case C 14:30-15:30
##############################
def test_scenario5() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=150,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="11:00",
        sched_end_time="13:00",
        duration_minutes=180,
        start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="14:30",
        sched_end_time="15:30",
        duration_minutes=60,
        start_offset_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 10, 30, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 11, 0, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 14, 0, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 14, 30, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 15, 30, tzinfo=la_time)


##############################
# Scenario 6: (schedules have many "to-follow" cases (i.e., no break)
# case A scheduled  8:00- 9:30; duration of 1:00 hours, start_offset 0
# case B scheduled  9:30-11:00, duration of 2:00 hour, start_offset 5
# case C scheduled 11:00-14:00, duration of 1:00 hours, start_offset -5
#
# expected:
# case A  8:00-9:00
# case B  9:35-11:35
# case C 11:35-12:35  / 12:05 - 13:05 (add 30 for turnover)
#
# TODO: CHECK WITH JEN THIS IS THE EXPECTED BEHAVIOR
##############################
def test_scenario6() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=60,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="9:30",
        sched_end_time="11:00",
        duration_minutes=120,
        start_offset_minutes=5,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="11:00",
        sched_end_time="14:00",
        duration_minutes=60,
        start_offset_minutes=-5,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 0, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 9, 35, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 35, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 12, 5, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 13, 5, tzinfo=la_time)


##############################
# Scenario 7: (overlapping scheduled cases)
# case A scheduled  8:00- 9:30; duration of 1:30 hours, start_offset 0
# case B scheduled  9:00-10:00, duration of 1:00 hour, start_offset 0
# case C scheduled  9:00-11:00, duration of 2:00 hours, start_offset 0
#
# expected:
# case A  8:00-9:30
# case B  9:30-10:30  // 10:00-11:00 with turnover
# case C 10:30-12:30  // 11:30-13:30 with turnover
##############################
def test_scenario7() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=90,
        start_offset_minutes=0,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="09:00",
        sched_end_time="10:00",
        duration_minutes=60,
        start_offset_minutes=0,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="09:00",
        sched_end_time="11:00",
        duration_minutes=120,
        start_offset_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 0, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 0, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 11, 30, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 13, 30, tzinfo=la_time)


##############################
# Scenario 8: (short cases in close succession)
# case A scheduled  7:30- 8:00; duration of 0:14 hours, start_offset 7
# case B scheduled  8:00-8:37, duration of 0:18 hour, start_offset +19
# case C scheduled  8:37-8:57, duration of 0:20 hours, start_offset +17
# case D scheduled  8:57-9:37, duration of 0:19 hours, start_offset +17
#
# expected:
# case A  7:37-7:51
# case B  8:19-8:37  (no turnover needed)
# case C  8:54-9:14  (8:57-9:17 wit 20 min turnover)
# case D  9:14-9:33  (9:37-9:56 with turnover)
##############################
def test_scenario8() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="07:30",
        sched_end_time="8:00",
        duration_minutes=14,
        start_offset_minutes=7,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="08:00",
        sched_end_time="08:37",
        duration_minutes=18,
        start_offset_minutes=19,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="08:37",
        sched_end_time="08:57",
        duration_minutes=20,
        start_offset_minutes=17,
    )
    caseD = TestApellaCase(
        id="d",
        sched_start_time="08:57",
        sched_end_time="09:37",
        duration_minutes=19,
        start_offset_minutes=17,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC, caseD])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 4
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 7, 37, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 7, 51, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 8, 19, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 8, 37, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 8, 57, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 9, 17, tzinfo=la_time)
    assert results[3].forecast_start_time == datetime(2022, 10, 1, 9, 37, tzinfo=la_time)
    assert results[3].forecast_end_time == datetime(2022, 10, 1, 9, 56, tzinfo=la_time)


##############################
# Scenario 9: Turnover pushes case
# case A scheduled  8:00- 9:30; duration of 1:30 hours, start_offset 0
# case B scheduled  9:00- 10:00, duration of 1:00 hour, start_offset 0, turnover 60
# case C scheduled  10:00-11:00, duration of 1:00 hours, start_offset 0
#
# expected:
# case A  8:00-9:30
# case B  9:30-10:30  // 10:00-11:00 with turnover
# case C 10:30-12:30  // 12:00-13:00 with turnover
##############################
def test_scenario9() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=90,
        start_offset_minutes=0,
        turnover_duration_minutes=30,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="09:00",
        sched_end_time="10:00",
        duration_minutes=60,
        start_offset_minutes=0,
        turnover_duration_minutes=60,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="10:00",
        sched_end_time="11:00",
        duration_minutes=60,
        start_offset_minutes=0,
        turnover_duration_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 0, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 0, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 12, 0, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)


##############################
# Scenario 10: Negative turnover is ignored
# case A scheduled  8:00- 9:30; duration of 1:30 hours, start_offset 0
# case B scheduled  9:00- 10:00, duration of 1:00 hour, turnover 60
# case C scheduled  10:00-11:00, duration of 1:00 hours, start_offset 0
#
# expected:
# case A  8:00-9:30
# case B  9:30-10:30  // 10:00-11:00 with turnover
# case C 10:30-12:30  // 12:00-13:00 with turnover
##############################
def test_scenario10() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=90,
        start_offset_minutes=0,
        turnover_duration_minutes=-5,
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="09:00",
        sched_end_time="10:00",
        duration_minutes=60,
        start_offset_minutes=0,
        turnover_duration_minutes=60,
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="10:00",
        sched_end_time="11:00",
        duration_minutes=60,
        start_offset_minutes=0,
        turnover_duration_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 0, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 0, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 12, 0, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)


##############################
# Scenario 11: Following is moved up
# case A scheduled  8:00- 9:30; duration of 1:30 hours, has to follow case
# case B scheduled  9:30- 10:30, duration of 1:00 hour
# case C scheduled  11:00-12:00, duration of 1:00 hour
#
# expected:
# case A  8:00-8:45
# case B  9:15-10:30
# case C 11:00-12:00
##############################
def test_scenario11() -> None:
    caseA = TestApellaCase(
        id="a",
        sched_start_time="08:00",
        sched_end_time="9:30",
        duration_minutes=45,
        start_offset_minutes=0,
        turnover_duration_minutes=30,
        following_case_id="b",
    )
    caseB = TestApellaCase(
        id="b",
        sched_start_time="09:30",
        sched_end_time="10:30",
        duration_minutes=75,
        start_offset_minutes=15,
        turnover_duration_minutes=30,
        preceding_case_id="a",
    )
    caseC = TestApellaCase(
        id="c",
        sched_start_time="11:00",
        sched_end_time="12:00",
        duration_minutes=60,
        start_offset_minutes=0,
        turnover_duration_minutes=0,
    )

    forecast_generator = forecast_combiner_generator([caseA, caseB, caseC])

    results = forecast_generator.generate_starting_schedule_for_room()

    assert len(results) == 3
    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
    assert results[0].forecast_end_time == datetime(2022, 10, 1, 8, 45, tzinfo=la_time)
    assert results[1].forecast_start_time == datetime(2022, 10, 1, 9, 15, tzinfo=la_time)
    assert results[1].forecast_end_time == datetime(2022, 10, 1, 10, 30, tzinfo=la_time)
    assert results[2].forecast_start_time == datetime(2022, 10, 1, 11, 0, tzinfo=la_time)
    assert results[2].forecast_end_time == datetime(2022, 10, 1, 12, 0, tzinfo=la_time)


# TODO: Eventually, when we move away from these estimators, we will allow for a result
#    from a model to be None (i.e., it didn't have a response). The logic works
#    so that we can just pass it and it will repeat the schedule
#    However, making this change right now leads to a lot of mypy errors in the Estimators
#    and I just can't anymore with it.
###############################
## Scenario 9:  No actual values from the models
## case A scheduled  8:00- 9:30; duration of None; start offset of None
## case B scheduled 10:30-11:30, duration of None; start offset of None
## case C scheduled 13:00-15:00, duration of None; start offset of None
##
## expected: (the schedule)
## case A  8:00- 9:30
## case B 10:30-11:30
## case C 13:00-15:00
###############################
# @pytest.mark.skip(
#    reason="Right now the estimators can't handle null values if a model does not produce a result. Dealing with the mypy repercussions of this is just too much right now"
# )
# @mock.patch(
#    "case_forecasting_baseline.helpers.forecast_model_combiner.datetime",
#    MagicMock(
#        now=MagicMock(
#            return_value=datetime(2022, 9, 30, 6, 0, 0, tzinfo=ZoneInfo(key="America/Los_Angeles"))
#        )
#    ),
# )
# def test_scenario9() -> None:
#    caseA = TestApellaCase(
#        id="a",
#        sched_start_time="08:00",
#        sched_end_time="9:30",
#        duration=None,
#        start_offset=None,
#        day_start=None,
#    )
#    caseB = TestApellaCase(
#        id="b",
#        sched_start_time="10:30",
#        sched_end_time="11:30",
#        duration=None,
#        start_offset=None,
#        day_start=None,
#    )
#    caseC = TestApellaCase(
#        id="c",
#        sched_start_time="13:00",
#        sched_end_time="15:00",
#        duration=None,
#        start_offset=None,
#        day_start=None,
#    )
#
#    case_dtos, durations, start_offsets, day_of_starts = generate_inputs_for_test(
#        [caseA, caseB, caseC]
#    )
#
#    forecast_generator = ForecastModelCombiner(
#        durations, start_offsets, "model1", "model2", case_dtos
#    )
#    results = forecast_generator.generate_starting_schedule_for_room()
#
#    assert len(results) == 3
#    assert results[0].forecast_start_time == datetime(2022, 10, 1, 8, 0, tzinfo=la_time)
#    assert results[0].forecast_end_time == datetime(2022, 10, 1, 9, 30, tzinfo=la_time)
#    assert results[1].forecast_start_time == datetime(2022, 10, 1, 10, 30, tzinfo=la_time)
#    assert results[1].forecast_end_time == datetime(2022, 10, 1, 11, 30, tzinfo=la_time)
#    assert results[2].forecast_start_time == datetime(2022, 10, 1, 13, 0, tzinfo=la_time)
#    assert results[2].forecast_end_time == datetime(2022, 10, 1, 15, 0, tzinfo=la_time)
#
