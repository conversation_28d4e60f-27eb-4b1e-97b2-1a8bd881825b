"""
Tests for the FastAPI app endpoints.

This module contains unit tests for the FastAPI endpoints in the forecast combiner service.
Each test focuses on a specific endpoint and uses isolated test environments.
"""

from http import HTTPStatus

import pytest
from fastapi import FastAPI, Response
from fastapi.testclient import TestClient

from forecast_combiner.types import ForecastVariant, Output


@pytest.fixture
def mock_output() -> Output:
    """Return a mock output object for testing."""
    return Output(
        rooms={},
        forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
        service_version="test-version",
    )


@pytest.fixture
def mock_app() -> FastAPI:
    """Return a mock FastAPI app for isolated testing."""
    return FastAPI()


@pytest.fixture
def mock_app_client(mock_app: FastAPI) -> TestClient:
    """Return a TestClient for the mock application."""
    return TestClient(mock_app)


class TestForecastVariantsEndpoint:
    """Tests for the forecast variants endpoint."""

    def test_v4_returns_structured_format(
        self, mock_app: FastAPI, mock_app_client: TestClient
    ) -> None:
        """Test that the v4 endpoint returns the expected structured format."""
        expected_variants = [{"variant_name": variant.name} for variant in ForecastVariant]

        @mock_app.get("/v4/forecasting/variants")
        def forecast_variants_v4() -> dict[str, list[dict[str, str]]]:
            return {"forecast_variants": expected_variants}

        response = mock_app_client.get("/v4/forecasting/variants")

        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, dict)
        assert "forecast_variants" in data
        assert isinstance(data["forecast_variants"], list)

        variant_names = [item["variant_name"] for item in data["forecast_variants"]]
        expected_variant_names = [item["variant_name"] for item in expected_variants]

        assert set(variant_names) == set(expected_variant_names)
        assert len(variant_names) == len(expected_variant_names)

    def test_legacy_returns_list_format(
        self, mock_app: FastAPI, mock_app_client: TestClient
    ) -> None:
        """Test that the legacy endpoint returns the expected list format."""
        expected_variants = [variant.value for variant in ForecastVariant]

        @mock_app.get("/forecasting/variants")
        def forecast_variants_legacy() -> list[str]:
            return expected_variants

        response = mock_app_client.get("/forecasting/variants")

        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)
        assert set(data) == set(expected_variants)
        assert len(data) == len(expected_variants)


class TestPredictForSiteEndpoint:
    """Tests for the predict for site endpoint."""

    def test_returns_expected_output(self, mock_app: FastAPI, mock_output: Output) -> None:
        """Test that the endpoint returns the expected output."""

        @mock_app.post("/v4/predict/site")
        async def predict_site_endpoint() -> Output:
            return mock_output

        client = TestClient(mock_app)
        response = client.post(
            "/v4/predict/site", json={"site_id": "test-site", "date": "2025-01-01"}
        )

        assert response.status_code == 200

        assert response.json() == {
            "rooms": {},
            "forecast_variant": "bayesian_static_forecast",
            "service_version": "test-version",
        }


class TestHealthCheckEndpoints:
    """Tests for the health check endpoints."""

    def test_livez_returns_200(self, mock_app: FastAPI, mock_app_client: TestClient) -> None:
        """Test that the livez endpoint returns 200 OK."""

        @mock_app.get("/livez")
        def livez() -> Response:
            return Response(status_code=HTTPStatus.OK, content="Service is running")

        response = mock_app_client.get("/livez")

        assert response.status_code == 200
        assert response.text == "Service is running"

    def test_readyz_returns_200_when_ready(
        self, mock_app: FastAPI, mock_app_client: TestClient
    ) -> None:
        """Test that the readyz endpoint returns 200 when the service is ready."""

        @mock_app.get("/readyz")
        async def readyz() -> Response:
            return Response(status_code=HTTPStatus.OK, content="OK")

        response = mock_app_client.get("/readyz")

        assert response.status_code == 200
        assert response.text == "OK"

    def test_readyz_returns_503_when_not_ready(
        self, mock_app: FastAPI, mock_app_client: TestClient
    ) -> None:
        """Test that the readyz endpoint returns 503 when the service is not ready."""

        @mock_app.get("/readyz")
        async def readyz() -> Response:
            return Response(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE, content="HTTP session not ready"
            )

        response = mock_app_client.get("/readyz")

        assert response.status_code == 503
        assert response.text == "HTTP session not ready"
