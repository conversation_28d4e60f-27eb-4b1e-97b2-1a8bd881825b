"""
Tests for the OpenAPI-generated Forecast Combiner Client.

This module contains unit tests for the OpenAPI-generated Forecast Combiner Client.
Note: These tests will be skipped if the client has not been generated yet.
"""

from datetime import date
from unittest.mock import MagicMock, patch

import pytest

# Skip tests if the client has not been generated yet
try:
    import forecast_combiner_client

    # Import specific classes to avoid mypy errors
    from forecast_combiner_client.api.default_api import <PERSON>fault<PERSON><PERSON>
    from forecast_combiner_client.api_client import ApiClient
    from forecast_combiner_client.configuration import Configuration
    from forecast_combiner_client.exceptions import ApiException
    from forecast_combiner_client.models.api_inputs import APIInputs
    from forecast_combiner_client.models.api_request import APIRequest
    from forecast_combiner_client.models.forecast_variant import ForecastVariant

    CLIENT_GENERATED = True
except ImportError:
    # Define dummy classes for type checking when client is not generated
    Configuration = MagicMock  # type: ignore
    ApiClient = MagicMock  # type: ignore
    DefaultApi = MagicMock  # type: ignore
    APIInputs = MagicMock  # type: ignore
    APIRequest = MagicMock  # type: ignore
    ForecastVariant = MagicMock  # type: ignore
    ApiException = Exception  # type: ignore
    CLIENT_GENERATED = False


@pytest.mark.skipif(not CLIENT_GENERATED, reason="Client not generated yet")
class TestOpenAPIClient:
    """Tests for the OpenAPI-generated Forecast Combiner Client."""

    def setup_method(self) -> None:
        """Set up the test environment."""
        self.configuration = MagicMock()
        self.api_client = MagicMock()
        self.api_instance = MagicMock()

        with patch("forecast_combiner_client.ApiClient", return_value=self.api_client):
            with patch("forecast_combiner_client.DefaultApi", return_value=self.api_instance):
                self.configuration = forecast_combiner_client.Configuration()  # type: ignore
                self.configuration.host = "http://localhost:3001"
                self.api_client = forecast_combiner_client.ApiClient(self.configuration)  # type: ignore
                self.api_instance = forecast_combiner_client.DefaultApi(self.api_client)  # type: ignore

    def test_get_forecast_variants(self) -> None:
        """Test getting forecast variants."""
        mock_response = MagicMock()
        self.api_instance.get_forecast_variants.return_value = mock_response

        response = self.api_instance.get_forecast_variants()

        self.api_instance.get_forecast_variants.assert_called_once()
        assert response == mock_response

    def test_predict_for_site(self) -> None:
        """Test predicting for a site."""
        mock_response = MagicMock()
        self.api_instance.predict_site.return_value = mock_response

        api_inputs = APIInputs(
            site_id="test-site",
            date=date(2024, 6, 14),
            forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
        )
        api_request = APIRequest(inputs=api_inputs)

        response = self.api_instance.predict_site(api_request)

        self.api_instance.predict_site.assert_called_once_with(api_request)
        assert response == mock_response

    def test_get_version_info(self) -> None:
        """Test getting version info."""
        mock_response = MagicMock()
        self.api_instance.version_info_get.return_value = mock_response

        response = self.api_instance.version_info_get()

        self.api_instance.version_info_get.assert_called_once()
        assert response == mock_response

    def test_check_health(self) -> None:
        """Test checking health."""
        mock_response = MagicMock()
        self.api_instance.livez_get.return_value = mock_response

        response = self.api_instance.livez_get()

        self.api_instance.livez_get.assert_called_once()
        assert response == mock_response

    def test_api_exception(self) -> None:
        """Test handling API exceptions."""
        self.api_instance.livez_get.side_effect = ApiException(
            status=500, reason="Internal Server Error"
        )

        with pytest.raises(ApiException) as excinfo:
            self.api_instance.livez_get()

        assert excinfo.value.status == 500
        assert excinfo.value.reason == "Internal Server Error"
