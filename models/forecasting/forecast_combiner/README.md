# Forecast Combiner

The Forecast Combiner is a service that, given a site id and a date, pieces together predictions from multiple forecasting models into a predicted case schedule using heuristics. The selection of which models to use is based on the forecast variant.

## Forecast Variants

The Forecast Combiner supports the following variants:

| Variant                       | Static Offset | Turnover Duration | Event Model Forecast (Transformer) | Dynamic Case End (Pythia) | Bayesian Case Duration |
| ----------------------------- | ------------- | ----------------- | ---------------------------------- | ------------------------- | ---------------------- |
| `bayesian_static_forecast`    | ✅             | ✅                 | ✅                                  | ✅                         | ✅ |



## Making a call to forecast combiner in the upper environment in k8s

Configure your Kubectl to connect to the correct cluster:

* dev: `gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`
* prod: `gcloud container clusters get-credentials prod-internal-gke --region us-central1 --project prod-internal-c5ac6b`

Get the list of contexts with the following command:
```
kubectl config get-contexts

# gke_dev-internal-b2aa9f_us-central1_dev-internal-gke
# gke_prod-internal-c5ac6b_us-central1_prod-internal-gke
```

Connect to the correct cluster using the following command:
```
kubectl config use-context gke_<project-id>_<region>_<cluster-name>
```

Create a tunnel so you can call the service from your machine. In this example, it'll make the
service available on localhost:3001 :

```
kubectl -n forecast-combiner port-forward service/forecast-combiner 3001:80
```

Now you can use curl to call the service:
```
curl -X 'POST' \
  'http://localhost:3001/predict_for_site' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "inputs": {
    "site_id": "HMH-OPC19",
    "date": "2024-06-14"
  }
}'
```

Alternatively, you can visit the auto-generated landing page at http://localhost:3001/ to see the
list of endpoints and examples of how to use them.

## Test local changes

Sometimes it is useful to test the local changes to the service by running a local instance of forecast-combiner that connects to its forecasting models in the upper environments.

Forecast-combiner makes calls to the following services:
* Case Duration
* Turnover Duration
* Static Start Offset
* Event Model Forecasts
* Dynamic Case End

Set up the tunnel for all the services that forecast-combiner makes calls to (using separate terminals for each service):

```
# Turnover Duration
kubectl -n model-turnover port-forward service/model-turnover 9998:80

# Static Start Offset
kubectl -n model-static-start-offset port-forward service/model-static-start-offset 9997:80

# Event Model Forecasts
kubectl -n model-event-model-forecasts port-forward service/model-event-model-forecasts 9995:80

# Dynamic Case End
kubectl -n model-dynamic-case-end port-forward service/model-dynamic-case-end 9992:80

# Bayesian Case Duration
kubectl -n model-bayesian-case-duration port-forward service/model-bayesian-case-duration 9980:80
```

Then, run the forecast-combiner service locally:

```
# Run the service
make run-local

# OR run with auto-reload for development
make run-local-reload
```

### API Endpoints

The service provides the following endpoints:

* `/v4/predict/site` - Generate forecasts for all rooms in a site for a specific date
* `/v4/forecasting/variants` - Get the supported forecast variants
* `/livez` - Health check endpoint
* `/readyz` - Readiness probe endpoint

Example API call:

```
curl -X 'POST' \
  'http://localhost:3000/v4/predict/site' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "site_id": "HMH-OPC19",
  "date": "2024-06-14"
}'
```

You can also visit the auto-generated API documentation at http://localhost:3000/docs to explore the available endpoints.

### Troubleshooting

If you are reading this from another project (that is not `forecast-combiner`), the `namespace` has to be available
in the cluster (or you will get `Error from server (NotFound): namespaces "whatever you used" not found`).
We may not have the whole infrastructure in dev. To make sure we do have the namespace in the env you selected run
`kubectl get namespaces` and then the command above should be
`kubectl -n <namespace> port-forward service/<namespace> 3001:80`

#### Service URL
It should be <service-name>.<namespace>.svc.cluster.local:<service-port>
some examples are:
* http://forecast-combiner.forecast-combiner.svc.cluster.local/predict_for_site
* http://model-bayesian-case-duration.model-bayesian-case-duration.svc.cluster.local/predict_many
