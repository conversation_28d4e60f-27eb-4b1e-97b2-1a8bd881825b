"""
Example usage of the Forecast Combiner Client.

This script demonstrates how to use the OpenAPI-generated client to interact with
the Forecast Combiner Service.
"""

import logging
from datetime import date
from pprint import pprint

# Import specific classes to avoid mypy errors
import forecast_combiner_client  # type: ignore
from forecast_combiner_client.exceptions import ApiException  # type: ignore


def main() -> None:
    """Run the example."""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    configuration = forecast_combiner_client.Configuration()
    configuration.host = "http://localhost:3000"

    api_client = forecast_combiner_client.ApiClient(configuration)

    health_api = forecast_combiner_client.HealthApi(api_client)
    metadata_api = forecast_combiner_client.MetadataApi(api_client)
    variants_api = forecast_combiner_client.VariantsApi(api_client)
    prediction_api = forecast_combiner_client.PredictionApi(api_client)

    try:
        health_api.livez_livez_get()
        print("Service is healthy: True")
    except ApiException as e:
        print(f"Service is not healthy: {e}")
        return

    try:
        version_info = metadata_api.version_info_version_info_get()
        print("Service version:")
        pprint(version_info)
    except ApiException as e:
        print(f"Error getting version info: {e}")

    try:
        variants = variants_api.get_forecast_variants()
        print("Available forecast variants:")
        pprint(variants)
    except ApiException as e:
        print(f"Error getting forecast variants: {e}")

    try:
        # Import here to avoid mypy errors
        from forecast_combiner_client.models.api_inputs import APIInputs
        from forecast_combiner_client.models.api_request import APIRequest
        from forecast_combiner_client.models.forecast_variant import ForecastVariant
        from forecast_combiner_client.models.output import Output

        # Create the request using Pydantic models
        api_inputs = APIInputs(
            site_id="HMH-OPC19",
            date=date(2024, 6, 14),
            forecast_variant=ForecastVariant.BAYESIAN_STATIC_FORECAST,
        )
        api_request = APIRequest(inputs=api_inputs)

        # Validate the request
        print("Request validation:")
        print(f"  Is valid: {api_request.model_validate(api_request.model_dump())}")
        print(f"  JSON: {api_request.model_dump_json(indent=2)}")

        # Make the API call
        result: Output = prediction_api.predict_site(api_request)

        # The result is a Pydantic model
        print("Forecast result:")
        print(f"  Result type: {type(result)}")
        print(f"  Is Output model: {isinstance(result, Output)}")
        print(f"  Forecast variant: {result.forecast_variant}")
        print(f"  Service version: {result.service_version}")
        print(f"  Number of rooms: {len(result.rooms)}")

        # Convert to dict for pretty printing
        pprint(result.model_dump())
    except ApiException as e:
        print(f"Error generating forecasts: {e}")


if __name__ == "__main__":
    main()
