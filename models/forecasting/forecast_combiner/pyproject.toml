[tool.ruff]
lint.ignore = [
   # Trust black to get line length right.
   # Without this, there are cases where black won't reflow a
   # line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
lint.select = ["E", "F", "I001"]
line-length = 100

[tool.mypy]
strict = true
exclude = ["^forecast_combiner_client/", "^tests/fixtures/", "^examples/"]

[[tool.mypy.overrides]]
module = [
    "google.*",
    "sklearn.*",
    "joblib",
    "sgqlc.*",
    "retry.*",
    "forecast_combiner_client.*",
]
ignore_missing_imports = true

[tool.poetry]
name = "forecast_combiner"
version = "0.1.0"
description = ""
authors = ["Apella Engineering <<EMAIL>>", "Data Science & Machine Learning <<EMAIL>>"]
readme = "README.md"
packages = [{include = "forecast_combiner"}]

[tool.poetry.dependencies]
python = "~3.10"
apella-cloud-api = "^22.36.0"
cloudpickle = "^3.0.0"
dill = "^0.3.7"
google-cloud-storage = "^2.15.0"
pandas = "^2.2.1"
pydantic = "^2.6.3"
sgqlc = "^16.3"
numpy = "^1.26.4"
aiohttp = "^3.9.5"
aiohttp-retry = "^2.8.3"
feature_store = { path = "../../../feature_store", develop = true}
pandera = "^0.23.1"

retry = "^0.9.2"
ddtrace = "^2.9.2"
aiodns = "^3.2.0"
launchdarkly-server-sdk = "^9.7.1"
google-cloud-secret-manager = "^2.20.2"
prometheus-client = "^0.20.0"
fastapi = "^0.115.12"
uvicorn = {extras = ["standard"], version = "^0.29.0"}
prometheus-fastapi-instrumentator = "^7.0.0"
openapi-generator-cli = "^7.11.0.post0,<7.13.0"

[tool.poetry.group.dev.dependencies]
isort = "^5.13.2"
mypy = "^1.8.0"
pandas-stubs = "^2.2.0.240218"
pytest = "^8.0.2"
pytest-cov = "^4.1.0"
ruff = "^0.3.0"
types-requests = "^2.31.0.20240406"
pytest-asyncio = "^0.23.7"
twine = "^6.1.0"
build = "^1.2.2.post1"

[tool.poetry.group.serving.dependencies]
serving_utils = {path = "../../../serving_utils", develop = true }

[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"
