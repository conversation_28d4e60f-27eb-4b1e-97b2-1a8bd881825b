SHELL := /bin/bash
PORT := 3001
PWD := $(shell pwd)

# Define the container name
REQUEST_TIMEOUT_SECONDS=30
OTEL_CONTAINER_NAME=otel-collector

# Check if Docker is running (optional but good practice)
check-docker:
	@if ! docker info > /dev/null 2>&1; then \
		echo "Docker does not seem to be running, run it first and retry"; \
		exit 1; \
	fi

# Target to run the OpenTelemetry Collector if not already running
run-otel-collector: check-docker
	@# Check if a container with the name exists and is stopped
	@if [ "$$(docker ps -aq -f status=exited -f name=$(OTEL_CONTAINER_NAME))" ]; then \
		echo "Removing existing stopped $(OTEL_CONTAINER_NAME) container..."; \
		docker rm $(OTEL_CONTAINER_NAME); \
	fi
	@# Check if the container is running
	@if [ -z "$$(docker ps -q -f status=running -f name=$(OTEL_CONTAINER_NAME))" ]; then \
		echo "Starting $(OTEL_CONTAINER_NAME) container..."; \
		docker run -d --name $(OTEL_CONTAINER_NAME) \
			-p 4318:4318 \
			otel/opentelemetry-collector-contrib:latest; \
	else \
		echo "$(OTEL_CONTAINER_NAME) container is already running."; \
	fi


format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format .
	poetry run mypy --exclude openapi_client .

test:
	poetry run pytest -vv tests/

test-with-client: generate-openapi-client
	poetry run pytest -vv tests/

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

dev-local-reload: run-otel-collector
	REQUEST_TIMEOUT_SECONDS=$(REQUEST_TIMEOUT_SECONDS) APELLA_API_SERVER_ENVIRONMENT=prod DEV_STUB=1 poetry run fastapi run --port $(PORT) --reload

dev-local: run-otel-collector
	REQUEST_TIMEOUT_SECONDS=$(REQUEST_TIMEOUT_SECONDS) APELLA_API_SERVER_ENVIRONMENT=prod DEV_STUB=1 poetry run fastapi run --port $(PORT) --workers 2

run-local-reload: run-otel-collector
	REQUEST_TIMEOUT_SECONDS=$(REQUEST_TIMEOUT_SECONDS) APELLA_API_SERVER_ENVIRONMENT=prod poetry run fastapi run --port $(PORT) --reload

run-local: run-otel-collector
	REQUEST_TIMEOUT_SECONDS=$(REQUEST_TIMEOUT_SECONDS) FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd APELLA_API_SERVER_ENVIRONMENT=prod poetry run fastapi run --port $(PORT) --workers 2

run-uvicorn:
	REQUEST_TIMEOUT_SECONDS=$(REQUEST_TIMEOUT_SECONDS) FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd APELLA_API_SERVER_ENVIRONMENT=prod poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

generate-openapi-spec:
	SPEC_GENERATION=1 poetry run python -m scripts.generate_openapi_spec

generate-openapi-client:generate-openapi-spec
generate-openapi-client:
	docker run \
		-v $(PWD):/local openapitools/openapi-generator-cli generate \
		-i local/openapi-spec.json \
		-g python \
		-o local \
		--additional-properties=disallowAdditionalPropertiesIfNotPresent=false,packageName=forecast_combiner_client,useOneOfDiscriminatorLookup=true,usePydanticV2=true

generate-openapi-client-ci: generate-openapi-spec
generate-openapi-client-ci:
	openapi-generator-cli generate \
		-i openapi-spec.json \
		-g python \
		-o . \
		--additional-properties=disallowAdditionalPropertiesIfNotPresent=false,packageName=forecast_combiner_client,useOneOfDiscriminatorLookup=true,usePydanticV2=true

generate-client-for-ci: generate-openapi-client-ci
	rm -rf build_artifacts
	mkdir build_artifacts
	mv forecast_combiner_client build_artifacts/
	mv setup.py build_artifacts/
	cp utils/client_utils/pyproject.toml build_artifacts/
	cp utils/client_utils/README.md build_artifacts/


.PHONY: run-otel-collector check-docker format lint test test-cov dev-local-reload dev-local run-local-reload run-local run-uvicorn generate-openapi-spec generate-openapi-client generate-mock-openapi-spec generate-openapi-client-ci generate-client-for-ci
