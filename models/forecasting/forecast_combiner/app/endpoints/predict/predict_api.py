"""
Defines the API endpoints for the forecast combiner service
© Apella Inc 2025
"""

import asyncio
import functools
import socket
from datetime import datetime, time, timedelta, timezone
from http import HTTPStatus
from typing import Final, List

import apella_cloud_api
import numpy as np
import pandas as pd
import serving_utils.config as config
from aiohttp import (
    AsyncResolver,
    ClientSession,
    ClientTimeout,
    TCPConnector,
)
from fastapi import APIRouter, Header, Response
from prometheus_client import Counter, Histogram
from pydantic import BaseModel
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import get_service_version
from zoneinfo import ZoneInfo

from forecast_combiner.data_extractor import (
    get_predicted_phases_for_site,
    get_scheduled_cases_for_site,
    get_timezone_for_site,
)
from forecast_combiner.features import (
    BayesianStaticQuerySchema,
    EventModelQuerySchema,
    PythiaQuerySchema,
    StaticStartOffsetQuerySchema,
    TurnoverQuerySchema,
)
from forecast_combiner.forecast_model_combiner import (
    forecast_for_room,
)
from forecast_combiner.sub_service_fetcher.many_case_sub_service_fetcher import (
    ManyCaseSubServiceFetcher,
)
from forecast_combiner.sub_service_fetcher.per_case_sub_service_fetcher import (
    PerCaseSubServiceFetcher,
)
from forecast_combiner.sub_service_fetcher.services import (
    BAYESIAN_CASE_DURATION_SERVICE,
    DYNAMIC_CASE_END_SERVICE,
    EVENT_MODEL_FORECASTS_SERVICE,
    STATIC_START_OFFSET_SERVICE,
    TURNOVER_DURATION_SERVICE,
)
from forecast_combiner.sub_service_fetcher.sub_service_fetcher import (
    SubServiceFetcher,
)
from forecast_combiner.types import (
    APIRequest,
    ForecastedCaseOutput,
    ForecastVariant,
    Output,
    PhaseInfo,
    RoomOutput,
    Temporality,
)

_ML_SERVICE_REQUEST_TIMEOUT_S = 5

_NAIVE_FORECASTS: Final = Counter(
    "naive_forecasts",
    "Number of missing notifications sent",
    [
        "site_id",
        "temporality",
        "feature",
    ],
)

_NON_NAIVE_FORECASTS: Final = Counter(
    "non_naive_forecasts",
    "Number of missing notifications sent",
    [
        "site_id",
        "temporality",
        "feature",
    ],
)

_FEATURE_AGE_METRIC: Final = Histogram(
    "feature_age_hours",
    "Age of a feature in minutes",
    [
        "site_id",
        "temporality",
        "feature_name",
        "schedule_date",
    ],
)

logger = setup_json_logger(logger_name="ForecastCombinerService")


def get_series_median(prediction_series: pd.Series) -> pd.Series:  # type: ignore
    """
    Used to find the median of the bayesian case duration service prediction
    """
    # Filter out the None values
    return prediction_series.apply(
        lambda x: np.median(x) if x is not None and len(x) > 0 else pd.NA  # type: ignore
    )


class VariantItem(BaseModel):
    """Schema for a single forecast variant item."""

    variant_name: ForecastVariant


class ForecastVariantResponse(BaseModel):
    """Schema for the forecast variants response."""

    forecast_variants: List[VariantItem]


class ForecastCombinerService:
    """Service class for forecast combiner."""

    static_offset_fetcher: SubServiceFetcher
    turnover_duration_fetcher: SubServiceFetcher
    event_model_forecast_fetcher: SubServiceFetcher
    dynamic_case_end_fetcher: SubServiceFetcher
    bayesian_case_duration_fetcher: SubServiceFetcher
    http_session: ClientSession | None = None

    def __init__(self) -> None:
        self._setup_router()
        if config.is_spec_generation():
            return

        logger.info(f"Apella client environment is {config.get_apella_server_environment()}")
        self.apella_client = apella_cloud_api.Client(
            environment=config.get_apella_server_environment(),
            timeout_seconds=60,
            permissions=["event:read:any", "case:read:any"],
        )
        self.version_information = self.version_info()
        logger.info("ForecastCombinerService initializing (session not started)")

        # session will be created in start_session during app lifespan startup
        self.http_session = None

        logger.info("ForecastCombinerService initialized")

    def _setup_router(self) -> None:
        self.router = APIRouter()

        # Health check endpoints
        self.router.add_api_route(
            "/livez",
            self.livez,
            methods=["GET"],
            tags=["health"],
            summary="Liveness Check",
            description="Health check endpoint that verifies the service is running.",
        )
        self.router.add_api_route(
            "/readyz",
            self.readyz,
            methods=["GET"],
            tags=["health"],
            summary="Readiness Check",
            description="Readiness probe that verifies model and device are properly configured.",
        )

        self.router.add_api_route(
            "/v4/predict/site",
            self.predict_for_site,
            methods=["POST"],
            tags=["v4", "Prediction"],
            summary="Generate Site Forecasts",
            description="Generate forecasts for all rooms in a site for a specific date.",
        )
        self.router.add_api_route(
            "/v4/forecasting/variants",
            self.forecast_variants_v4,
            methods=["GET"],
            tags=["v4", "Variants"],
            summary="Get Forecast Variants",
            description="Returns the supported forecast variants in a structured format.",
        )
        self.router.add_api_route(
            "/version_info",
            self.version_info,
            methods=["GET"],
            tags=["metadata"],
            summary="Get Version Info",
            description="Returns version information about the service.",
        )

        # Legacy routes for compatibility
        self.router.add_api_route(
            "/predict_for_site",
            self.predict_for_site,
            methods=["POST"],
            tags=["legacy"],
            summary="Generate Site Forecasts (Legacy)",
            description="Legacy endpoint for generating forecasts for all rooms in a site for a specific date.",
            deprecated=True,
        )
        self.router.add_api_route(
            "/forecasting/variants",
            self.forecast_variants_legacy,
            methods=["GET"],
            tags=["legacy"],
            summary="Get Forecast Variants (Legacy)",
            description="Legacy endpoint for returning the supported forecast variants as a simple list.",
            deprecated=True,
        )

    async def start_session(self) -> None:
        """Creates the aiohttp ClientSession."""
        if self.http_session is None:
            logger.info("Creating aiohttp ClientSession")
            self.http_session = ClientSession(
                timeout=ClientTimeout(total=_ML_SERVICE_REQUEST_TIMEOUT_S),
                connector=TCPConnector(
                    resolver=AsyncResolver(),
                    # the resolver uses the generic AF_UNSPEC family, which the async loop
                    # seems to have issues resolving locally for IPv6 ::1
                    family=socket.AddressFamily.AF_INET,
                ),
            )
            # Re-initialize fetchers that depend on the session
            await self._initialize_fetchers()
        else:
            logger.warning("Attempted to start session when one already exists.")

    async def close_session(self) -> None:
        """Closes the aiohttp ClientSession."""
        if self.http_session:
            logger.info("Closing aiohttp ClientSession")
            await self.http_session.close()
            self.http_session = None
        else:
            logger.warning("Attempted to close session when none exists.")

    async def _initialize_fetchers(self) -> None:
        """Initializes sub-service fetchers requiring the http_session."""
        if not self.http_session:
            logger.error("Cannot initialize fetchers without an active http_session.")
            # Or raise an exception
            raise RuntimeError("HTTP session not initialized before initializing fetchers.")

        logger.info("Initializing sub-service fetchers")
        (
            self.static_offset_fetcher,
            self.turnover_duration_fetcher,
            self.event_model_forecast_fetcher,
            self.dynamic_case_end_fetcher,
        ) = [
            ManyCaseSubServiceFetcher(
                self.http_session,
                service_url,
                prediction_field,
                feature_map,
                result_model,
                converter,
            )
            for service_url, prediction_field, feature_map, result_model, converter in (
                (
                    STATIC_START_OFFSET_SERVICE,
                    "start_offset",
                    {
                        "static_start_offset_forecast": "start_offset",
                        "static_start_offset_version": "start_offset_version",
                        "static_start_offset_service_version": "start_offset_service_version",
                        "static_start_offset_model_version": "start_offset_model_version",
                    },
                    StaticStartOffsetQuerySchema,
                    None,
                ),
                (
                    TURNOVER_DURATION_SERVICE,
                    "turnover_duration",
                    {
                        "turnover_duration_forecast": "turnover_duration",
                        "turnover_duration_version": "turnover_duration_version",
                        "turnover_duration_service_version": "turnover_duration_service_version",
                        "turnover_duration_model_version": "turnover_duration_model_version",
                    },
                    TurnoverQuerySchema,
                    None,
                ),
                (
                    EVENT_MODEL_FORECASTS_SERVICE,
                    "event_model_forecast",
                    {
                        "patient_wheels_out_forecast": "event_model_forecast",
                        "patient_wheels_out_version": "event_model_forecast_version",
                        "patient_wheels_out_service_version": "event_model_forecast_service_version",
                        "patient_wheels_out_model_version": "event_model_forecast_model_version",
                    },
                    EventModelQuerySchema,
                    pd.to_datetime,
                ),
                (
                    DYNAMIC_CASE_END_SERVICE,
                    "dynamic_case_end",
                    {
                        "pythia_duration_forecast": "dynamic_case_end",
                        "pythia_duration_version": "dynamic_case_end_version",
                        "pythia_duration_service_version": "dynamic_case_end_service_version",
                        "pythia_duration_model_version": "dynamic_case_end_model_version",
                        "pythia_forecast_tag": "dynamic_case_end_prediction_tag",
                    },
                    PythiaQuerySchema,
                    None,
                ),
            )
        ]

        self.bayesian_case_duration_fetcher = PerCaseSubServiceFetcher(
            self.http_session,
            BAYESIAN_CASE_DURATION_SERVICE,
            "bayesian_case_duration",
            {
                "bayesian_duration_forecast": "bayesian_case_duration",
                "bayesian_duration_version": "bayesian_case_duration_version",
                "bayesian_duration_service_version": "bayesian_case_duration_service_version",
                "bayesian_duration_model_version": "bayesian_case_duration_model_version",
            },
            BayesianStaticQuerySchema,
            get_series_median,
        )

    @functools.lru_cache()
    def _cached_get_timezone_for_site(self, site_id: str) -> ZoneInfo | None:
        return get_timezone_for_site(self.apella_client, site_id)

    def livez(self) -> Response:
        """
        Health check endpoint that verifies the service is running.

        This endpoint is used by Kubernetes liveness probe to determine if the service is alive.
        It always returns 200 OK if the service is running, regardless of the state of dependencies.

        Returns:
            Response: A 200 OK response with a simple message.
        """
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    async def readyz(self) -> Response:
        """
        Readiness probe that verifies the service is ready to handle requests.

        This endpoint is used by Kubernetes readiness probe to determine if the service
        is ready to receive traffic. It checks if the HTTP session is initialized and open.

        Returns:
            Response: A 200 OK response if ready, or a 503 Service Unavailable response if not ready.
        """
        # Check if the session is ready instead of models (assuming session implies readiness)
        if self.http_session is None or self.http_session.closed:
            return Response(
                status_code=HTTPStatus.SERVICE_UNAVAILABLE, content="HTTP session not ready"
            )

        return Response(status_code=HTTPStatus.OK, content="OK")

    async def predict_for_site(
        self,
        request: APIRequest,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> Output:
        """
        Generates forecasts for a given site and date based on scheduled cases and predicted phases.

        Args:
            request: The API request containing site ID, date, and forecast variant.
            run_id: Optional run ID header for tracing.

        Returns:
            An Output object containing the forecasts per room.

        Raises:
            Exception: If the timezone for the site cannot be found.
            RuntimeError: If the HTTP session is not initialized.
        """
        inputs = request.inputs
        site_timezone = self._cached_get_timezone_for_site(inputs.site_id)

        if site_timezone is None:
            logger.error(
                "Failed to get timezone for site",
                extra={
                    "site_id": inputs.site_id,
                    "error_type": "timezone_not_found",
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            # TODO: Raise specific HTTPException
            raise Exception(f"Could not find Timezone for site: {inputs.site_id}")

        # Ensure session exists before proceeding
        if not self.http_session:
            logger.error(
                "HTTP Session not available in predict_for_site",
                extra={
                    "site_id": inputs.site_id,
                    "error_type": "http_session_not_initialized",
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            # TODO: Raise specific HTTPException (e.g., 503 Service Unavailable)
            raise RuntimeError("HTTP session not initialized")

        # Set the start and end time of the period we want to forecast.
        # The input is a single day (a date), so we calculate the start and end time
        # of that day in the site's timezone.
        day_start = datetime.combine(inputs.date, time.min).replace(tzinfo=site_timezone)
        day_end = day_start + timedelta(days=1)

        # First, we need to get the cases and predicted phases for the site
        # Run blocking I/O in a separate thread
        cases_to_predict = await asyncio.to_thread(
            get_scheduled_cases_for_site, self.apella_client, day_start, day_end, inputs.site_id
        )

        forecast_variant = inputs.forecast_variant

        if cases_to_predict is None or cases_to_predict.empty:
            logger.debug(
                "No cases found for site",
                extra={
                    "site_id": inputs.site_id,
                    "date": inputs.date.isoformat(),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            return Output(rooms={}, forecast_variant=forecast_variant, **self.version_information)

        # Run blocking I/O in a separate thread
        predicted_phases = await asyncio.to_thread(
            get_predicted_phases_for_site,
            self.apella_client,
            day_start,
            day_end,
            inputs.site_id,
        )

        # Determine if the forecast is for the future or the current day
        temporality = (
            Temporality.FUTURE if day_start > datetime.now(site_timezone) else Temporality.DAY_OF
        )

        try:
            val = await self._get_predictions(
                site_id=inputs.site_id,
                cases_to_predict=cases_to_predict,
                predicted_phases=predicted_phases,
                temporality=temporality,
                forecast_variant=forecast_variant,
                run_id=run_id,  # Pass run_id down
            )
            logger.info(
                "Successfully generated forecasts",
                extra={
                    "site_id": inputs.site_id,
                    "date": inputs.date.isoformat(),
                    "forecast_variant": forecast_variant,
                    "timezone": str(site_timezone),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            return val
        except Exception as e:
            logger.exception(  # Use logger.exception to include stack trace
                "Failed to generate forecasts",
                extra={
                    "site_id": inputs.site_id,
                    "date": inputs.date.isoformat(),
                    "error_type": type(e).__name__,
                    "error_message": str(e),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            # TODO: Raise specific HTTPException based on the error type
            raise

    @staticmethod
    async def _update_naivety_metrics(
        df: pd.DataFrame,
        feature: str,
        site_id: str,
        temporality: str,
    ) -> None:
        _NON_NAIVE_FORECASTS.labels(site_id=site_id, temporality=temporality, feature=feature).inc(
            amount=len(df[df[feature].notnull()])
        )

        _NAIVE_FORECASTS.labels(site_id=site_id, temporality=temporality, feature=feature).inc(
            amount=len(df[~df[feature].notnull()])
        )

    async def _get_predictions_df(
        self,
        case_ids: list[str],
        cases_to_predict: pd.DataFrame,
        site_id: str,
        temporality: Temporality,
        forecast_variant: ForecastVariant,
        run_id: str | None = None,
    ) -> pd.DataFrame:
        # Ensure session exists before proceeding
        if not self.http_session:
            logger.error("HTTP Session not available in _get_predictions_df")
            raise RuntimeError("HTTP session not initialized")

        match forecast_variant:
            case ForecastVariant.BAYESIAN_STATIC_FORECAST:
                return await self._get_predictions_df_bayesian_static_forecast(
                    case_ids,
                    cases_to_predict,
                    site_id,
                    temporality,
                    run_id,
                )
            case _:
                raise Exception("Unsupported forecast_variant")

    async def _get_predictions_df_bayesian_static_forecast(
        self,
        case_ids: list[str],
        cases_to_predict: pd.DataFrame,
        site_id: str,
        temporality: Temporality,
        run_id: str | None = None,
    ) -> pd.DataFrame:
        # Ensure session exists before proceeding
        if not self.http_session:
            logger.error(
                "HTTP Session not available in _get_predictions_df_bayesian_static_forecast"
            )
            raise RuntimeError("HTTP session not initialized")

        now = datetime.now(timezone.utc)
        (
            static_start_offsets_df,
            turnover_after_case_durations_df,
            event_model_forecasts_df,
            dynamic_case_end_df,
            bayesian_case_duration_df,
        ) = await asyncio.gather(
            *[
                self.static_offset_fetcher.fetch(case_ids, now, run_id),
                self.turnover_duration_fetcher.fetch(case_ids, now, run_id),
                self.event_model_forecast_fetcher.fetch(case_ids, now, run_id),
                self.dynamic_case_end_fetcher.fetch(case_ids, now, run_id),
                self.bayesian_case_duration_fetcher.fetch(case_ids, now, run_id),
            ]
        )

        await asyncio.gather(
            *[
                self._update_naivety_metrics(
                    static_start_offsets_df,
                    "start_offset",
                    site_id,
                    temporality.name,
                ),
                self._update_naivety_metrics(
                    turnover_after_case_durations_df,
                    "turnover_duration",
                    site_id,
                    temporality.name,
                ),
                self._update_naivety_metrics(
                    event_model_forecasts_df,
                    "event_model_forecast",
                    site_id,
                    temporality.name,
                ),
                self._update_naivety_metrics(
                    dynamic_case_end_df,
                    "dynamic_case_end",
                    site_id,
                    temporality.name,
                ),
                self._update_naivety_metrics(
                    bayesian_case_duration_df,
                    "bayesian_case_duration",
                    site_id,
                    temporality.name,
                ),
            ]
        )

        result = (
            cases_to_predict.merge(static_start_offsets_df, on="case_id", how="left")
            .merge(turnover_after_case_durations_df, on="case_id", how="left")
            .merge(event_model_forecasts_df, on="case_id", how="left")
            .merge(dynamic_case_end_df, on="case_id", how="left")
            .merge(bayesian_case_duration_df, on="case_id", how="left")
        ).replace({pd.NA: None, np.nan: None})

        result["case_duration"] = result["bayesian_case_duration"]

        return result

    @staticmethod
    def _generate_empty_bayesian_case_duration_df(case_ids: list[str]) -> pd.DataFrame:
        return pd.DataFrame(
            {
                "case_id": case_ids,
                "bayesian_case_duration": [pd.NA] * len(case_ids),
                "bayesian_case_duration_original": [pd.NA] * len(case_ids),
                "bayesian_case_duration_version": ["naive_bayesian_case_duration"] * len(case_ids),
            }
        )

    @staticmethod
    def _generate_empty_dynamic_case_end_df(case_ids: list[str]) -> pd.DataFrame:
        return pd.DataFrame(
            {
                "case_id": case_ids,
                "dynamic_case_end": [pd.NA] * len(case_ids),
                "dynamic_case_end_version": ["naive_dynamic_case_end_model"] * len(case_ids),
                "dynamic_case_end_prediction_tag": [pd.NA] * len(case_ids),
            }
        )

    async def _get_predictions(
        self,
        site_id: str,
        cases_to_predict: pd.DataFrame,
        predicted_phases: list[PhaseInfo],
        temporality: Temporality,
        forecast_variant: ForecastVariant,
        run_id: str | None = None,
    ) -> Output:
        inference_time = datetime.now(timezone.utc)
        case_ids = cases_to_predict["case_id"].to_list()

        # Ensure session exists before proceeding
        if not self.http_session:
            logger.error("HTTP Session not available in _get_predictions")
            raise RuntimeError("HTTP session not initialized")

        predictions_df = await self._get_predictions_df(
            case_ids,
            cases_to_predict,
            site_id,
            temporality,
            forecast_variant,
            run_id,
        )

        # Now we can split this up by room and call our regular guys
        room_ids = predictions_df.room_id.unique()

        logger.debug(
            "Processing room-level forecasts",
            extra={
                "site_id": site_id,
                "num_rooms": len(room_ids),
                "prediction_data_shape": predictions_df.shape,
                "forecast_variant": forecast_variant,
            },
        )

        room_outputs = {}

        for room_id in room_ids:
            room_df = predictions_df[predictions_df.room_id == room_id]
            predicted_phases_for_room = [p for p in predicted_phases if p.room_id == room_id]
            room_forecast = forecast_for_room(
                room_id,
                site_id,
                inference_time,
                room_df,
                predicted_phases_for_room,
                forecast_variant,
            )
            case_forecasts = [
                ForecastedCaseOutput(
                    start_time=f.forecast_start_time,
                    end_time=f.forecast_end_time,
                    case_id=f.case_id,
                    debug_info=f.debug_info,
                    version_info=f.version_info,
                )
                for f in room_forecast.forecasts
            ]

            room_outputs[room_id] = RoomOutput(cases=case_forecasts)

        return Output(
            rooms=room_outputs, forecast_variant=forecast_variant, **self.version_information
        )

    def forecast_variants_v4(self) -> ForecastVariantResponse:
        """
        Returns the supported forecast variants in a structured format.

        This endpoint provides a list of all available forecast variants that can be used
        with the predict_for_site endpoint. Each variant represents a different forecasting
        methodology or model combination.

        Returns:
            ForecastVariantResponse: A structured response containing a list of forecast variants.
        """
        variants = [VariantItem(variant_name=variant) for variant in ForecastVariant]
        return ForecastVariantResponse(forecast_variants=variants)

    def forecast_variants_legacy(self) -> list[str]:
        """
        Returns the supported forecast variants as a simple list.

        This legacy endpoint provides a list of all available forecast variants that can be used
        with the predict_for_site endpoint.

        Returns:
            list[str]: A list of forecast variant identifiers as strings.
        """
        return [variant.value for variant in ForecastVariant]

    def version_info(self) -> dict[str, str]:
        """
        Returns version information about the service.

        This endpoint provides metadata about the current version of the service,
        which can be useful for debugging and tracking deployments.

        Returns:
            dict[str, str]: A dictionary containing version information.
        """
        return {
            "service_version": get_service_version(),
        }
