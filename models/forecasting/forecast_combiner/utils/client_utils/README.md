# Forecast Combiner Client

This is the Python client for the Forecast Combiner API.

## Installation

```bash
pip install forecast_combiner_client
```

## Usage

```python
from forecast_combiner_client import ApiClient, Configuration
from forecast_combiner_client.api import DefaultApi

# Configure the client
configuration = Configuration(host="https://your-api-host")
api_client = ApiClient(configuration)
api = DefaultApi(api_client)

# Call API methods
response = api.forecast_variants()
print(response)
```

## Development

This client is automatically generated from the OpenAPI specification of the Forecast Combiner API.
