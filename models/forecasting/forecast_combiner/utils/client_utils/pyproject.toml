[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "forecast-combiner-client"
version = "0.0.0" # This will be replaced by dynamic versioning
description = "Utilities and dependencies for Forecast Combiner Client generation/testing"
authors = ["Apella Engineering <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.10"
urllib3 = ">=2.0.0"
python-dateutil = ">=2.8.2"
pydantic = ">=2.0.0"