from __future__ import annotations

import json
import os
from collections.abc import Set
from datetime import datetime
from typing import List

import numpy as np
import pandas as pd
import pandera as pa
import serving_utils.config as config
from fastapi import APIRouter, HTTPException, Response
from feature_store import FeatureStore
from google.cloud.bigtable import Client as BTClient
from google.cloud.storage import Client as GCSClient
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import get_service_version
from training_utils.model_storage import ModelStorage

from dynamic_case_end.configs.prod_config import ModelTrainingConfig
from dynamic_case_end.features import (
    PythiaForecastFeatureQuerySchema,
    PythiaForecastFeatureSchema,
    PythiaResultSchema,
    PythiaSchema,
)
from dynamic_case_end.model.dynamic_case_end_model import (
    MODEL_TYPE,
    DynamicCaseEndModel,
    DynamicEndPredictorInputData,
    ModelInputPydanticFeatureSchema,
    ModelInputPydanticSchema,
)
from utils.types import (
    APIInputs,
    LegacyAPIInput,
    LegacyManyAPIInputs,
    ManyInputs,
    ManyOutputs,
    PredictedOutput,
)

logger = setup_json_logger(logger_name="DynamicCaseEndService")


class DynamicCaseEndService:
    def __init__(self) -> None:
        gcs_client = GCSClient()

        model_storage = ModelStorage(
            gcs_client,
            model_type=MODEL_TYPE,
            model_identifier="automatic_production_training/2025-05-23",
        )

        self.is_dev_stub = config.get_dev_stub() == "1"
        self.results_store = None
        if self.is_dev_stub:
            logger.warning("Running in dev stub mode. Not loading models.")
            self.models_by_org_id = {}
            self.locations_to_fit = ["nope"]
            self.model_identifier = "None"

        else:
            logger.info("Loading models. This may take a while")
            self.models_by_org_id = model_storage.load_models(serialization_package_name="joblib")
            self.model_training_config = ModelTrainingConfig(**model_storage.json_config)
            self.locations_to_fit = self.model_training_config.training_config.locations_to_fit
            self.model_identifier = self.model_training_config.model_identifier
            logger.info(f"Done loading models: {self.model_identifier}")

            bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
            instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])
            self.feature_store = FeatureStore(instance, "cases", PythiaSchema)
            self.forecast_duration_feature_store = FeatureStore(
                instance, "results", PythiaForecastFeatureSchema, prefix="ml_forecast"
            )

            if config.get_store_forecast_result():
                self.results_store = FeatureStore(
                    instance, "results", PythiaResultSchema, prefix="ml_forecast"
                )

        logger.info("DynamicCaseEnd service initialized")

        self.version_information = self.version_info()
        self.naive_version_information = self.naive_version_info()
        self.most_recent_timestamp = datetime.utcnow()

        self.router = APIRouter()

        self.router.add_api_route(
            "/model_version_deployed",
            self.model_version_deployed,
            methods=["GET"],
            tags=["v3", "Versioning"],
        )
        self.router.add_api_route(
            "/naive_version_info",
            self.naive_version_info,
            methods=["GET"],
            tags=["v3", "Versioning"],
        )
        self.router.add_api_route(
            "/version_info", self.version_info, methods=["GET"], tags=["v3", "Versioning"]
        )
        self.router.add_api_route(
            "/supported_locations",
            self.supported_locations,
            methods=["GET"],
            tags=["v3", "Versioning"],
        )

        self.router.add_api_route(
            "/predict", self.predict, methods=["POST"], tags=["v3", "Prediction"]
        )
        self.router.add_api_route(
            "/predict_many", self.predict_many, methods=["POST"], tags=["v3", "Prediction"]
        )

        self.router.add_api_route(
            "/test_inputs", self.test_inputs, methods=["POST"], tags=["v3", "Prediction"]
        )

        self.router.add_api_route(
            "/cooler_livez", self.livez, methods=["GET"], tags=["v3", "Infrastructure", "Health"]
        )
        self.router.add_api_route(
            "/livez", self.livez, methods=["GET"], tags=["v3", "Infrastructure", "Health"]
        )
        self.router.add_api_route(
            "/readyz", self.readyz, methods=["GET"], tags=["v3", "Infrastructure", "Health"]
        )
        self.router.add_api_route(
            "/shadow_livez",
            self.shadow_livez,
            methods=["GET"],
            tags=["v3", "Infrastructure", "Health", "Shadow"],
        )

    def get_features_for_cases(
        self, case_ids: Set[str]
    ) -> tuple[pa.typing.DataFrame[ModelInputPydanticSchema], pd.DataFrame]:
        features_result = self.feature_store.load_features(
            case_ids, ModelInputPydanticFeatureSchema
        )
        if len(features_result.entities_with_missing_features) > 0:
            logger.warning(
                f"Missing features for {len(features_result.entities_with_missing_features)} cases: {features_result.entities_with_missing_features}\n"
                f"Missing features with the following counts {json.dumps(features_result.missing_features_count)}\n"
            )
        if len(features_result.entities) == 0:
            return features_result.entities, features_result.bigtable_timestamps  # type: ignore

        feature_result_entities = features_result.entities
        # Create case_id column from index
        feature_result_entities["case_id"] = feature_result_entities.index
        # Need to append the final 4 columns to the feature dataframe
        feature_result_entities["obs_time"] = feature_result_entities[
            "actual_start_datetime_local"
        ].apply(lambda x: datetime.now(tz=x.tzinfo))

        feature_result_entities["actual_end_datetime_local"] = None
        feature_result_entities["wheels_out_occurred"] = 0
        feature_result_entities["num_times_wheels_out_in_case"] = 0

        # Get the static forecasted duration
        forecast_features_result = self.forecast_duration_feature_store.load_features(
            case_ids, PythiaForecastFeatureQuerySchema
        )
        if len(forecast_features_result.entities_with_missing_features) > 0:
            logger.warning(
                f"Missing features for {len(forecast_features_result.entities_with_missing_features)} cases: {forecast_features_result.entities_with_missing_features}\n"
                f"Missing features with the following counts {json.dumps(forecast_features_result.missing_features_count)}\n"
            )
        feature_result_entities["static_forecasted_duration"] = feature_result_entities[
            "scheduled_duration"
        ].astype(float)
        if len(forecast_features_result.entities) > 0:
            forecast_features_result_entities = forecast_features_result.entities
            forecast_features_result_entities["case_id"] = forecast_features_result_entities.index
            forecast_features_result_entities["static_forecasted_duration"] = (
                forecast_features_result_entities["bayesian_duration_median_forecast"]
            )
            # Replace the static forecasted duration with the one from the forecast features
            update_series = forecast_features_result_entities["static_forecasted_duration"]
            feature_result_entities.update(update_series)

        return feature_result_entities, features_result.bigtable_timestamps  # type: ignore

    def predict(self, inputs: LegacyAPIInput) -> PredictedOutput:
        return self._predict_many(ManyInputs([inputs.inputs])).root[0]

    def predict_many(self, inputs: LegacyManyAPIInputs) -> ManyOutputs:
        return self._predict_many(inputs.inputs)

    def _upsert_forecasts(self, results: list[PredictedOutput]) -> None:
        if self.results_store:
            results_df = pd.DataFrame(
                [
                    {
                        "pythia_duration_forecast": result.prediction,
                        "pythia_duration_version": result.version,
                        "pythia_duration_service_version": result.service_version,
                        "pythia_duration_model_version": result.model_version,
                        "pythia_forecast_tag": result.prediction_tag,
                    }
                    for result in results
                ],
                index=[result.case_id for result in results],
            )
            self.results_store.store_features(
                results_df,  # type: ignore
            )

    def _predict_many(self, inputs: ManyInputs) -> ManyOutputs:
        requested_case_ids = inputs.to_list()

        if self.is_dev_stub:
            logger.info(
                "Running in dev stub mode. Not loading models.",
                extra={
                    "requested_case_ids": requested_case_ids,
                },
            )
            results = [
                PredictedOutput(
                    case_id=case_id,
                    prediction_tag="dev",
                    prediction=3.14,
                    **self.version_information,  # type: ignore
                )
                for case_id in requested_case_ids
            ]

            self.most_recent_timestamp = datetime.utcnow()  # Store duration
            self._upsert_forecasts(results)

            return ManyOutputs(results)

        requested_case_ids_set = set(requested_case_ids)
        features, bigtable_timestamps = self.get_features_for_cases(requested_case_ids_set)

        if features.empty:
            results = [
                PredictedOutput(
                    case_id=case_id,
                    prediction_tag="unknown",
                    prediction=None,
                    **self.naive_version_information,  # type: ignore
                )
                for case_id in requested_case_ids
            ]
            self.most_recent_timestamp = datetime.utcnow()  # Store duration
            self._upsert_forecasts(results)
            # We have no features for these cases
            return ManyOutputs(results)

        if len(features["org_id"].unique()) > 1:
            logger.error(
                f"Multiple org_ids found in features: {features['org_id'].unique()}. Using the first one."
            )
            raise HTTPException(
                status_code=400,
                detail="Multiple org_ids found in features.",
            )

        org_id = str(features["org_id"].unique()[0])
        if org_id not in self.models_by_org_id:
            logger.warning(f"No model for org_id {org_id}")
            # We have no model for this org
            self.most_recent_timestamp = datetime.utcnow()  # Store duration
            return ManyOutputs(
                [
                    PredictedOutput(
                        case_id=case_id,
                        prediction_tag="unknown",
                        prediction=None,
                        **self.naive_version_information,  # type: ignore
                    )
                    for case_id in requested_case_ids
                ]
            )

        model: DynamicCaseEndModel = self.models_by_org_id[org_id]

        # Properly handle NaNs and pd.NA values
        predictions = model.run_model_prediction_for_df(features).replace(
            {pd.NA: None, None: None, np.NaN: None}
        )

        feature_timestamps = bigtable_timestamps.to_dict(orient="index")

        results = []
        for case_id, prediction in zip(features.index.to_list(), predictions.to_dict("records")):
            version_info = (
                self.version_information
                if prediction["prediction_tag"] == "dynamic_end_prediction"
                else self.naive_version_information
            )
            results.append(
                PredictedOutput(
                    case_id=case_id,
                    prediction_tag=prediction["prediction_tag"],
                    prediction=prediction["predicted_total_case_duration"],
                    feature_timestamps=feature_timestamps[case_id],
                    **version_info,
                )
            )

        missing_cases = requested_case_ids_set - set(features.index.to_list())
        for case_id in missing_cases:
            results.append(
                PredictedOutput(
                    case_id=case_id,
                    prediction_tag="unknown",
                    prediction=None,
                    **self.naive_version_information,  # type: ignore
                )
            )

        self._upsert_forecasts(results)

        self.most_recent_timestamp = datetime.utcnow()  # Store duration
        return ManyOutputs(results)

    def test_inputs(self, inputs: APIInputs) -> DynamicEndPredictorInputData:
        return DynamicEndPredictorInputData(**inputs.model_dump())

    def supported_locations(self) -> List[str]:
        return self.locations_to_fit

    def model_version_deployed(self) -> str:
        return self.model_identifier

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def livez(self) -> Response:
        """
        Livez endpoint for liveliness checks
        Returns a 200 status code if the service has successfully run a prediction in the last 65 seconds
        Otherwise returns a 500 status code
        """
        logger.info(f"Most recent prediction was at {self.most_recent_timestamp.isoformat()}")
        if self.most_recent_timestamp > datetime.utcnow() - pd.Timedelta(seconds=65):
            return Response(status_code=200)
        else:
            return Response(status_code=500)

    def shadow_livez(self) -> Response:
        """
        Shadow Livez endpoint for liveliness checks
        Always returns a 200 status code
        """
        return Response(status_code=200)

    def readyz(self) -> Response:
        """
        Healthz endpoint for readiness checks
        Returns a 200 status code if the service is healthy
        Otherwise returns a 500 status code
        """
        if self.is_dev_stub or self.models_by_org_id:
            return Response(status_code=200)
        else:
            logger.error("Service is not ready")
            return Response(status_code=200)
