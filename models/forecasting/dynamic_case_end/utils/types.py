from __future__ import annotations

from pydantic import BaseModel, Field, RootModel
from serving_utils.utils import VersionedModel


class APIInputs(BaseModel):
    case_id: str

    def to_list(self) -> list[str]:
        return list(vars(self).values())


class ManyInputs(RootModel[list[APIInputs]]):
    def to_list(self) -> list[str]:
        vals = [x.to_list() for x in self.root]
        return [item for row in vals for item in row]


class PredictedOutput(VersionedModel):
    case_id: str
    prediction: float | None
    prediction_tag: str
    feature_timestamps: dict[str, float] = Field(default={})


class ManyOutputs(RootModel[list[PredictedOutput]]):
    pass


class LegacyAPIInput(BaseModel):
    inputs: APIInputs


class LegacyManyAPIInputs(BaseModel):
    inputs: ManyInputs
