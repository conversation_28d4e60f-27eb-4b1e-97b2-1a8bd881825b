import math
from unittest.mock import Magic<PERSON>ock

import pandas as pd
import pytest

# clients
from google.cloud.bigquery import Client as BQ<PERSON><PERSON>
from training_utils.clearml_reporter import ClearMLReporter

from dynamic_case_end.configs.prod_config import DataSelectionConfig
from dynamic_case_end.training.data_selector import DataSelector


@pytest.fixture
def mock_bq_client() -> BQClient:
    """
    Fixture that simulates BQ client
    """
    bq_client = MagicMock(BQClient)
    return bq_client


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    mock_reporter = MagicMock(ClearMLReporter)
    mock_reporter.clearml_task = MagicMock()
    mock_reporter.clearml_task.get_logger = MagicMock()

    return mock_reporter


@pytest.fixture
def default_config() -> DataSelectionConfig:
    data_selection_config = DataSelectionConfig()
    data_selection_config.min_date = "2024-10-01"
    data_selection_config.max_date = "2024-12-01"
    return data_selection_config


@pytest.fixture
def default_selector(
    mock_bq_client: BQClient,
    default_config: DataSelectionConfig,
    mock_reporter: ClearMLReporter,
) -> DataSelector:
    return DataSelector(mock_bq_client, default_config, mock_reporter)


df_events_long = pd.DataFrame(
    [
        # events for case_id = 1, which has duplicate events
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_xfer_to_or_table",
            "event_datetime_local": "2024-02-01 11:05:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_draped",
            "event_datetime_local": "2024-02-01 11:10:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_undraped",
            "event_datetime_local": "2024-02-01 12:10:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_xfer_to_bed",
            "event_datetime_local": "2024-02-01 12:15:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        # duplicate patient_draped
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_draped",
            "event_datetime_local": "2024-02-01 12:30:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        # duplicate patient_undraped
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_undraped",
            "event_datetime_local": "2024-02-01 13:30:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        # another duplicate patient_undraped
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "event_type_id": "patient_undraped",
            "event_datetime_local": "2024-02-01 13:35:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        # events for case 2, which has missing events
        {
            "case_id": "2",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-03-01 11:00:00",
            "event_type_id": "patient_undraped",
            "event_datetime_local": "2024-03-01 13:30:00",
            "actual_end_datetime_local": "2024-03-01 14:00:00",
        },
    ]
)

for col in ["actual_start_datetime_local", "event_datetime_local", "actual_end_datetime_local"]:
    df_events_long[col] = pd.to_datetime(df_events_long[col])

df_events_pivoted = pd.DataFrame(
    [
        {
            "case_id": "1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
            "first_patient_xfer_to_or_table_datetime_local": "2024-02-01 11:05:00",
            "second_patient_xfer_to_or_table_datetime_local": "2024-02-01 11:05:00",
            "num_times_patient_xfer_to_or_table_in_case": 1,
            "first_patient_draped_datetime_local": "2024-02-01 11:10:00",
            "second_patient_draped_datetime_local": "2024-02-01 12:30:00",
            "num_times_patient_draped_in_case": 2,
            "first_patient_undraped_datetime_local": "2024-02-01 12:10:00",
            "second_patient_undraped_datetime_local": "2024-02-01 13:30:00",
            "num_times_patient_undraped_in_case": 2,
            "first_patient_xfer_to_bed_datetime_local": "2024-02-01 12:15:00",
            "second_patient_xfer_to_bed_datetime_local": "2024-02-01 12:15:00",
            "num_times_patient_xfer_to_bed_in_case": 1,
        },
        {
            "case_id": "2",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-03-01 11:00:00",
            "actual_end_datetime_local": "2024-03-01 14:00:00",
            "first_patient_xfer_to_or_table_datetime_local": None,
            "second_patient_xfer_to_or_table_datetime_local": None,
            "num_times_patient_xfer_to_or_table_in_case": None,
            "first_patient_draped_datetime_local": None,
            "second_patient_draped_datetime_local": None,
            "num_times_patient_draped_in_case": None,
            "first_patient_undraped_datetime_local": "2024-03-01 13:30:00",
            "second_patient_undraped_datetime_local": "2024-03-01 13:30:00",
            "num_times_patient_undraped_in_case": 1,
            "first_patient_xfer_to_bed_datetime_local": None,
            "second_patient_xfer_to_bed_datetime_local": None,
            "num_times_patient_xfer_to_bed_in_case": None,
        },
    ]
)

for col in [
    "actual_start_datetime_local",
    "actual_end_datetime_local",
    "first_patient_xfer_to_or_table_datetime_local",
    "second_patient_xfer_to_or_table_datetime_local",
    "first_patient_draped_datetime_local",
    "second_patient_draped_datetime_local",
    "first_patient_undraped_datetime_local",
    "second_patient_undraped_datetime_local",
    "first_patient_xfer_to_bed_datetime_local",
    "second_patient_xfer_to_bed_datetime_local",
]:
    df_events_pivoted[col] = pd.to_datetime(df_events_pivoted[col])


df_all_cases = pd.DataFrame(
    [
        # a case with duplicate events
        {
            "case_id": "1",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-02-01 11:00:00",
            "actual_end_datetime_local": "2024-02-01 14:00:00",
        },
        # a case with missing events
        {
            "case_id": "2",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-03-01 11:00:00",
            "actual_end_datetime_local": "2024-03-01 14:00:00",
        },
        # a case that had no events
        {
            "case_id": "3",
            "site_id": "site1",
            "org_id": "org1",
            "room_id": "room1",
            "actual_start_datetime_local": "2024-04-01 11:00:00",
            "actual_end_datetime_local": "2024-04-01 14:00:00",
        },
    ]
)

for col in ["actual_start_datetime_local", "actual_end_datetime_local"]:
    df_all_cases[col] = pd.to_datetime(df_all_cases[col])

# get rid of the third duplicate (to test functions downstream of take-first-two-events)
df_events_long_only_two_dupes = df_events_long[
    # time of the third duplicate patient_undraped
    df_events_long["event_datetime_local"] != pd.to_datetime("2024-02-01 13:35:00")
]


def test_pivot_events(default_selector: DataSelector) -> None:
    df_pivoted = default_selector.pivot_events(
        df_events_long=df_events_long, df_all_cases=df_all_cases
    )

    assert len(df_pivoted) == 3


def test_calculate_phase_duration(default_selector: DataSelector) -> None:
    phases = default_selector._calculate_phase_duration(
        df=df_events_pivoted,
        start_event_name="actual_start_datetime_local",
        end_event_name="first_patient_xfer_to_or_table_datetime_local",
    )

    # the first case in df_events_pivoted has 5 minutes between the start and end events
    assert phases[0] == 5
    # the second case in df_events_pivoted does not have the end event
    assert math.isnan(phases[1])


def test_calculate_individual_phase_durations(default_selector: DataSelector) -> None:
    df_cases_with_durations = default_selector.calculate_individual_phase_durations(
        df_events_pivoted
    )

    # only the first case will have non-null values for phase durations
    assert list(df_cases_with_durations["wheels_in_to_first_xfer_to_or_table"])[0] == 5
    assert list(df_cases_with_durations["first_xfer_to_or_table_to_first_draped"])[0] == 5
    assert list(df_cases_with_durations["first_draped_to_first_undraped"])[0] == 60
    assert list(df_cases_with_durations["first_undraped_to_first_xfer_to_bed"])[0] == 5
    assert list(df_cases_with_durations["first_xfer_to_bed_to_wheels_out"])[0] == 105

    # the second case has null phase durations
    assert math.isnan(list(df_cases_with_durations["wheels_in_to_first_xfer_to_or_table"])[1])


def test_get_pivoted_timestamps_for_event_type_duplicates(default_selector: DataSelector) -> None:
    # test pivoting for a duplicated event
    df_pivoted_patient_undraped = default_selector._get_pivoted_timestamps_for_event_type(
        df=df_events_long_only_two_dupes,  # third or higher order duplicates are already eliminated upstream
        event_type_name="patient_undraped",
    )

    first_case_patient_undraped = df_pivoted_patient_undraped[
        df_pivoted_patient_undraped["case_id"] == "1"
    ]
    second_case_patient_undraped = df_pivoted_patient_undraped[
        df_pivoted_patient_undraped["case_id"] == "2"
    ]
    assert "first_patient_undraped_datetime_local" in df_pivoted_patient_undraped.columns
    assert len(df_pivoted_patient_undraped) == 2
    assert (
        list(first_case_patient_undraped["first_patient_undraped_datetime_local"])[0].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        == "2024-02-01 12:10:00"
    )
    assert (
        list(first_case_patient_undraped["second_patient_undraped_datetime_local"])[0].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        == "2024-02-01 13:30:00"
    )
    assert list(first_case_patient_undraped["num_times_patient_undraped_in_case"])[0] == 2
    assert (
        list(second_case_patient_undraped["first_patient_undraped_datetime_local"])[0].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        == "2024-03-01 13:30:00"
    )
    assert (
        list(second_case_patient_undraped["second_patient_undraped_datetime_local"])[0].strftime(
            "%Y-%m-%d %H:%M:%S"
        )
        == "2024-03-01 13:30:00"
    )
    assert list(second_case_patient_undraped["num_times_patient_undraped_in_case"])[0] == 1


def test_get_pivoted_timestamps_for_event_type_no_duplicates_and_missing(
    default_selector: DataSelector,
) -> None:
    # test pivoting for a non-duplicated event
    df_pivoted_xfer_to_or_table = default_selector._get_pivoted_timestamps_for_event_type(
        df=df_events_long_only_two_dupes,  # third or higher order duplicates are already eliminated upstream
        event_type_name="patient_xfer_to_or_table",
    )

    first_case_xfer_to_or_table = df_pivoted_xfer_to_or_table[
        df_pivoted_xfer_to_or_table["case_id"] == "1"
    ]

    assert "first_patient_xfer_to_or_table_datetime_local" in df_pivoted_xfer_to_or_table.columns
    assert (
        len(df_pivoted_xfer_to_or_table) == 1
    )  # only 1 of the 2 cases in df_events_long has patient_xfer_to_or_table
    assert (
        list(first_case_xfer_to_or_table["first_patient_xfer_to_or_table_datetime_local"])[
            0
        ].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-02-01 11:05:00"
    )
    assert (
        list(first_case_xfer_to_or_table["second_patient_xfer_to_or_table_datetime_local"])[
            0
        ].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-02-01 11:05:00"
    )
    assert list(first_case_xfer_to_or_table["num_times_patient_xfer_to_or_table_in_case"])[0] == 1


def test_get_first_two_events_per_case_and_event_type(default_selector: DataSelector) -> None:
    first_two_events_per_event_type = default_selector.get_first_two_events_per_case_and_event_type(
        df_events_long
    )

    assert "event_timestamp_order" in first_two_events_per_event_type.columns
    assert max(first_two_events_per_event_type["event_timestamp_order"]) == 2
    # out of the 8 events in df_events_long, one of them is a "third duplicate" and should be removed
    assert len(first_two_events_per_event_type) == 7


def test_get_query_string_for_all_case_features(
    default_selector: DataSelector, default_config: DataSelectionConfig
) -> None:
    query = default_selector.get_query_string_for_all_case_features()

    assert default_config.min_date in query
    assert default_config.max_date in query


def test_get_query_string_for_raw_events(
    default_selector: DataSelector, default_config: DataSelectionConfig
) -> None:
    query = default_selector.get_query_string_for_raw_events()

    assert default_config.min_date in query
    assert default_config.max_date in query
