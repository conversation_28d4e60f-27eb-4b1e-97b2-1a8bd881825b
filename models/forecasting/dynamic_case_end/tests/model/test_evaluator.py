from unittest.mock import Magic<PERSON>ock

import pandas as pd
import pytest
from google.cloud.bigquery import Client as BQ<PERSON>lient
from training_utils.clearml_reporter import Clear<PERSON>Reporter

from dynamic_case_end.configs.prod_config import DynamicModelConfig, EvaluationConfig
from dynamic_case_end.model.dynamic_case_end_model import DynamicCaseEndModel
from dynamic_case_end.model.evaluator import Evaluator

df_transformer_predictions = pd.DataFrame(
    [
        {
            "site_id": "site1",
            "case_id": "case1",
            "obs_time_minutes_before_wheels_out": 5,
            "transformer_forecasted_duration": 100,
        }
    ]
)

df_aggregate_phase_data = pd.DataFrame(
    [
        {
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "site_id": "site1",
            "agg_wheels_in_to_xfer_to_or_table": 10.0,
            "agg_xfer_or_table_to_draped": 10.0,
            "agg_draped_to_undraped": 60.0,
            "agg_undraped_to_xfer_bed": 10.0,
            "agg_xfer_to_bed_to_wheels_out": 10.0,
            "surgeon_proc_combo_num_appeared_in_training": 1,
        },
        {
            "first_primary_surgeon": "SurgeonB",
            "first_primary_procedure": "ProcedureB",
            "procedure_count": 1,
            "site_id": "site1",
            "agg_wheels_in_to_xfer_to_or_table": 5.0,
            "agg_xfer_or_table_to_draped": 5.0,
            "agg_draped_to_undraped": 30.0,
            "agg_undraped_to_xfer_bed": 5.0,
            "agg_xfer_to_bed_to_wheels_out": 5.0,
            "surgeon_proc_combo_num_appeared_in_training": 1,
        },
    ]
)

# note that for df_test_raw, we already know all the timestamps
df_test_raw = pd.DataFrame(
    [
        # a case with no duplicates and no missing
        {
            "case_id": "case1",
            "site_id": "site1",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:15:00"),
            # df_test duplicates the first timestamp if there are no real duplicates
            # TODO: double check this
            "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:15:00"),
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:45:00"),
            "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:45:00"),
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 10:45:00"),
            "second_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 10:45:00"),
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:00:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:00:00"),
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 11:05:00"),
        },
        # a case with duplicates
        {
            "case_id": "case2",
            "site_id": "site1",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:15:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:15:00"),
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:45:00"),
            "second_patient_draped_datetime_local": pd.to_datetime(
                "2024-11-01 11:45:00"
            ),  # duplicate draped
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 10:45:00"),
            "second_patient_undraped_datetime_local": pd.to_datetime(
                "2024-11-01 12:45:00"
            ),  # duplicate undraped
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 12:50:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 12:50:00"),
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 12:55:00"),
        },
        # a case with missing event
        {
            "case_id": "case3",
            "site_id": "site1",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:45:00"),
            "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:45:00"),
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 10:45:00"),
            "second_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 10:45:00"),
            "first_patient_xfer_to_bed_datetime_local": pd.NaT,
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 12:55:00"),
        },
    ]
)


@pytest.fixture
def default_model_config() -> DynamicModelConfig:
    return DynamicModelConfig(pct_duplicate_cutoff=25, max_factor_allowed_for_running_duration=3)


@pytest.fixture
def default_evaluation_config() -> EvaluationConfig:
    default_evaluation_config = EvaluationConfig()
    default_evaluation_config.location_type_to_evaluate = "site_id"

    default_evaluation_config.evaluation_to_fit_mapping = {
        "site1": "org1",
    }
    return default_evaluation_config


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    mock_reporter = MagicMock(ClearMLReporter)
    mock_reporter.clearml_task = MagicMock()
    mock_reporter.clearml_task.get_logger = MagicMock()

    return mock_reporter


@pytest.fixture
def mock_bq_client() -> BQClient:
    mock_client = MagicMock(BQClient)

    return mock_client


@pytest.fixture
def default_dynamic_case_end_model(default_model_config: DynamicModelConfig) -> DynamicCaseEndModel:
    dynamic_case_end_model = DynamicCaseEndModel(
        location="org1",
        config=default_model_config,
    )
    dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    return dynamic_case_end_model


@pytest.fixture
def default_evaluator(
    default_dynamic_case_end_model: DynamicCaseEndModel,
    default_evaluation_config: EvaluationConfig,
    mock_reporter: ClearMLReporter,
    mock_bq_client: BQClient,
) -> Evaluator:
    default_evaluator = Evaluator(
        models={"org1": default_dynamic_case_end_model},
        df_test_raw=df_test_raw,
        df_transformer_predictions=df_transformer_predictions,
        evaluation_config=default_evaluation_config,
        sites_to_evaluate=["site1", "site2"],
        reporter=mock_reporter,
        bq_client=mock_bq_client,
    )
    return default_evaluator


def test_apply_static_forecasted_duration_heuristic_for_dynamic_end_prediction(
    default_evaluator: Evaluator,
) -> None:
    df_predictions = pd.DataFrame(
        [
            {
                "obs_time": pd.to_datetime("2024-11-01 11:00:00"),
                "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
                "predicted_total_case_duration": 120,
                "prediction_tag": "dynamic_end_prediction",
                "scheduled_duration": 100,
                "static_forecasted_duration": 100,
            }
        ]
    )

    df_result = default_evaluator.apply_static_forecasted_duration_heuristic(df_predictions)
    # since predition_tag = dynamic_end_prediction, should use the predicted duration of 120 minutes
    assert (
        list(df_result["predicted_end_datetime_local"])[0].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-11-01 12:00:00"
    )


def test_apply_static_forecasted_duration_heuristic_for_non_dynamic_end_prediction(
    default_evaluator: Evaluator,
) -> None:
    df_predictions = pd.DataFrame(
        [
            {
                "obs_time": pd.to_datetime("2024-11-01 11:00:00"),
                "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
                "predicted_total_case_duration": None,
                "prediction_tag": "surgeon_proc_combo_num_not_in_training",
                "scheduled_duration": 100,
                "static_forecasted_duration": 100,
            }
        ]
    )

    df_result = default_evaluator.apply_static_forecasted_duration_heuristic(df_predictions)
    # since predition_tag = surgeon_proc_combo_num_not_in_training, should use the scheduled duration of 100 minutes
    assert (
        list(df_result["predicted_end_datetime_local"])[0].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-11-01 11:40:00"
    )


def test_apply_static_forecasted_duration_heuristic_for_non_dynamic_end_prediction_exceeding_obs_time(
    default_evaluator: Evaluator,
) -> None:
    df_predictions = pd.DataFrame(
        [
            {
                # obs_time is already more than scheduled_duration after start time
                "obs_time": pd.to_datetime("2024-11-01 12:00:00"),
                "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
                "predicted_total_case_duration": None,
                "prediction_tag": "surgeon_proc_combo_num_not_in_training",
                "scheduled_duration": 100,
                "static_forecasted_duration": 100,
            }
        ]
    )

    df_result = default_evaluator.apply_static_forecasted_duration_heuristic(df_predictions)
    # since predition_tag = surgeon_proc_combo_num_not_in_training, initially use the scheduled duration of 100 minutes
    # which would give an end time of 11:40, but obs_time is already 12:00, so need to predict 12:05
    assert (
        list(df_result["predicted_end_datetime_local"])[0].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-11-01 12:05:00"
    )


def test_calculate_mae(default_evaluator: Evaluator) -> None:
    df_predictions_subset = pd.DataFrame(
        [
            # these first two columns will be grouped together in MAE calc, for avg mae of 15 at site1
            {
                "site_id": "site1",
                "case_id": "case1",
                "prediction_error": -10,
                "abs_prediction_error": 10,
                "static_abs_prediction_error": 30,
                "obs_time_minutes_before_wheels_out": 1,
                "obs_time_minutes_to_forecasted_wheels_out_category": 0,
            },
            {
                "site_id": "site1",
                "case_id": "case2",
                "prediction_error": 20,
                "abs_prediction_error": 20,
                "static_abs_prediction_error": 30,
                "obs_time_minutes_before_wheels_out": 1,
                "obs_time_minutes_to_forecasted_wheels_out_category": 0,
            },
            # these next two will not be grouped together b/c the obs_time_minutes_before_wheels_out are different
            # so site2 will have two mae rows
            {
                "site_id": "site2",
                "case_id": "case1",
                "prediction_error": -5,
                "abs_prediction_error": 5,
                "static_abs_prediction_error": 30,
                "obs_time_minutes_before_wheels_out": 1,
                "obs_time_minutes_to_forecasted_wheels_out_category": 0,
            },
            {
                "site_id": "site2",
                "case_id": "case2",
                "prediction_error": 10,
                "abs_prediction_error": 10,
                "static_abs_prediction_error": 30,
                "obs_time_minutes_before_wheels_out": 11,
                "obs_time_minutes_to_forecasted_wheels_out_category": 10,
            },
        ]
    )

    (df_mae_cleanest_relative_to_wheels_out, df_mae_cleanest_relative_to_forecasted) = (
        default_evaluator.calculate_mae(
            df_predictions_subset=df_predictions_subset,
            obs_time_vs_other_time_colnames=[
                "obs_time_minutes_before_wheels_out",
                "obs_time_minutes_to_forecasted_wheels_out_category",
            ],
        )
    )

    df_site1_results = df_mae_cleanest_relative_to_wheels_out.query('site_id == "site1"')
    df_site2_results = df_mae_cleanest_relative_to_wheels_out.query(
        'site_id == "site2"'
    ).sort_values("obs_time_minutes_before_wheels_out")

    assert len(df_mae_cleanest_relative_to_wheels_out) == 3
    assert list(df_site1_results["abs_prediction_error"])[0] == 15
    assert list(df_site2_results["abs_prediction_error"])[0] == 5
    assert list(df_site2_results["abs_prediction_error"])[1] == 10


def test_calculate_errors(default_evaluator: Evaluator) -> None:
    df_predictions = pd.DataFrame(
        [
            {
                "predicted_end_datetime_local": pd.to_datetime("2024-11-01 10:24:00"),
                "static_predicted_end_datetime_local": pd.to_datetime("2024-11-01 10:30:00"),
                "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:50:00"),
                "obs_time": pd.to_datetime("2024-11-01 10:19:00"),
                "obs_time_minutes_before_wheels_out": 31.0,
                "actual_duration": 100.0,
            }
        ]
    )

    df_predictions_with_errors = default_evaluator.calculate_errors(df_predictions=df_predictions)

    assert round(list(df_predictions_with_errors["prediction_error"])[0]) == -26
    assert round(list(df_predictions_with_errors["abs_prediction_error"])[0]) == 26
    assert (
        round(list(df_predictions_with_errors["abs_percent_prediction_error"])[0]) == 26
    )  # 100 * (26/100)
    assert (
        round(list(df_predictions_with_errors["obs_time_minutes_to_forecasted_wheels_out"])[0]) == 5
    )
    # just rounds to the nearest 10 minutes
    assert (
        round(
            list(df_predictions_with_errors["obs_time_minutes_to_forecasted_wheels_out_category"])[
                0
            ]
        )
        == 0
    )
    assert round(list(df_predictions_with_errors["obs_time_minutes_before_wheels_out"])[0]) == 31


def test_find_static_to_pythia_cutovers_happy_path(default_evaluator: Evaluator) -> None:
    df_preds = pd.DataFrame(
        [
            # before xfer to or table, pythia worse than static
            {
                "obs_time": pd.to_datetime("2025-02-01 09:01:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 09:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 5,
                "abs_prediction_error": 10,
            },
            # before draped, pythia worse than static
            {
                "obs_time": pd.to_datetime("2025-02-01 09:09:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 09:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 5,
                "abs_prediction_error": 10,
            },
            # after draped, pythia better than static
            {
                "obs_time": pd.to_datetime("2025-02-01 09:20:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 09:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 5,
                "abs_prediction_error": 2,
            },
        ]
    )
    cutover_event = default_evaluator.find_static_to_pythia_cutovers_by_event(df_preds)

    assert cutover_event == "first_patient_draped_datetime_local"


def test_find_static_to_pythia_cutovers_events_out_of_order(default_evaluator: Evaluator) -> None:
    df_preds = pd.DataFrame(
        [
            # before undraped, pythia worse than static
            {
                "obs_time": pd.to_datetime("2025-02-01 09:20:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 11:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 5,
                "abs_prediction_error": 10,
            },
            # after xfer to bed, pythia better than static
            {
                "obs_time": pd.to_datetime("2025-02-01 10:20:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 11:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 50,
                "abs_prediction_error": 10,
            },
            {
                "obs_time": pd.to_datetime("2025-02-01 12:00:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2025-02-01 09:06:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2025-02-01 09:15:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2025-02-01 11:50:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2025-02-01 10:00:00"),
                "static_abs_prediction_error": 50,
                "abs_prediction_error": 10,
            },
        ]
    )

    cutover_event = default_evaluator.find_static_to_pythia_cutovers_by_event(df_preds)

    assert cutover_event == "first_patient_xfer_to_bed_datetime_local"


def test_find_static_to_pythia_cutovers_skipped_event(default_evaluator: Evaluator) -> None:
    df_preds = pd.DataFrame(
        [
            {
                "obs_time": pd.to_datetime("2025-02-01 09:01:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": None,
                "first_patient_draped_datetime_local": None,
                "first_patient_undraped_datetime_local": None,
                "first_patient_xfer_to_bed_datetime_local": None,
                "static_abs_prediction_error": 50,
                "abs_prediction_error": 10,
            },
            {
                "obs_time": pd.to_datetime("2025-02-01 09:20:00"),
                "case_id": "case1",
                "actual_start_datetime_local": pd.to_datetime("2025-02-01 09:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": None,
                "first_patient_draped_datetime_local": None,
                "first_patient_undraped_datetime_local": None,
                "first_patient_xfer_to_bed_datetime_local": None,
                "static_abs_prediction_error": 5,
                "abs_prediction_error": 2,
            },
        ]
    )
    cutover_event = default_evaluator.find_static_to_pythia_cutovers_by_event(df_preds)

    assert cutover_event == "actual_start_datetime_local"


def test_explode_to_time_increments(default_evaluator: Evaluator) -> None:
    df_test_with_time_increments = default_evaluator.explode_to_time_increments()
    assert len(df_test_with_time_increments) == (
        125 + 235 + 235
    )  # sum of observed lengths of cases in df_test_raw
    assert "wheels_out_occurred" in df_test_with_time_increments.columns
    assert "num_times_wheels_out_in_case" in df_test_with_time_increments.columns
    assert max(df_test_with_time_increments["wheels_out_occurred"]) == 0
    assert max(df_test_with_time_increments["num_times_wheels_out_in_case"]) == 0


def test_calculate_time_increments(default_evaluator: Evaluator) -> None:
    # this represents the information for a single case
    row = pd.Series(
        {
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:25:00"),
        }
    )

    date_range = default_evaluator.calculate_time_increments(row=row)

    assert len(date_range) == 25
    assert "2024-11-01 10:10:00" in date_range  # random timestamp in between start and end
    assert "2024-11-01 10:00:00" in date_range
    # inclusive: right in pd.date_range means that the end time would not be included
    # we don't want to evaluate at the actual end time, makes our model look unfairly good
    assert "2024-11-01 10:25:00" not in date_range


def test_simulate_inference_features(default_evaluator: Evaluator) -> None:
    df_test_with_time_increments = pd.DataFrame(
        [
            {
                "case_id": "case1",
                "site_id": "site1",
                "obs_time_minutes_before_wheels_out": 60,
                "obs_time": pd.to_datetime("2024-11-01 10:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 09:05:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime(
                    "2024-11-01 10:10:00"
                ),  # hasn't happened yet
                "first_patient_undraped_datetime_local": pd.to_datetime(
                    "2024-11-01 11:10:00"
                ),  # hasn't happened yet
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime(
                    "2024-11-01 11:15:00"
                ),  # hasn't happened yet
                # there are no real duplicates here, but at the input df to simulate_inference_features copies
                # the first timestamp into the column for the second timestamp.
                "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 09:05:00"
                ),
                "second_patient_draped_datetime_local": pd.to_datetime(
                    "2024-11-01 10:10:00"
                ),  # hasn't happened yet
                "second_patient_undraped_datetime_local": pd.to_datetime(
                    "2024-11-01 11:10:00"
                ),  # hasn't happened yet
                "second_patient_xfer_to_bed_datetime_local": pd.to_datetime(
                    "2024-11-01 11:15:00"
                ),  # hasn't happened yet
            },
            {
                "case_id": "case1",
                "site_id": "site1",
                "obs_time_minutes_before_wheels_out": 60,
                "obs_time": pd.to_datetime("2024-11-01 12:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 09:05:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 10:10:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 11:10:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:15:00"),
                # hasn't happened yet, but is real duplicate
                "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 12:05:00"
                ),
                "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 10:10:00"),
                "second_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 11:10:00"),
                "second_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:15:00"),
            },
            {
                "case_id": "case1",
                "site_id": "site1",
                "obs_time_minutes_before_wheels_out": 60,
                "obs_time": pd.to_datetime("2024-11-01 13:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 09:05:00"
                ),
                "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 10:10:00"),
                "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 11:10:00"),
                "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:15:00"),
                # a real duplicate that has happened by obs_time
                "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 12:05:00"
                ),
                "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 10:10:00"),
                "second_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 11:10:00"),
                "second_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 11:15:00"),
            },
        ]
    )

    df_test_simulated_at_obs_time = default_evaluator.simulate_inference_features(
        df_test_with_time_increments
    )

    # in first row, undraped hasn't happened yet
    assert pd.isnull(
        list(df_test_simulated_at_obs_time["first_patient_undraped_datetime_local"])[0]
    )
    # in first row, there is no duplicate xfer to or table, so the second timestamp should be null
    assert pd.isnull(
        list(df_test_simulated_at_obs_time["second_patient_xfer_to_or_table_datetime_local"])[0]
    )
    assert list(df_test_simulated_at_obs_time["num_times_patient_xfer_to_or_table_in_case"])[0] == 1
    assert list(df_test_simulated_at_obs_time["num_times_patient_draped_in_case"])[0] == 0

    # in second row, the real duplicate hasn't happened yet so the "known" second timestamp is still null
    assert pd.isnull(
        list(df_test_simulated_at_obs_time["second_patient_xfer_to_or_table_datetime_local"])[1]
    )
    assert list(df_test_simulated_at_obs_time["num_times_patient_xfer_to_or_table_in_case"])[1] == 1

    # in third row, the real duplicate has happened
    assert (
        list(df_test_simulated_at_obs_time["second_patient_xfer_to_or_table_datetime_local"])[
            2
        ].strftime("%Y-%m-%d %H:%M:%S")
        == "2024-11-01 12:05:00"
    )
    assert list(df_test_simulated_at_obs_time["num_times_patient_xfer_to_or_table_in_case"])[2] == 2


def test_simulate_known_event_counts_at_inference(default_evaluator: Evaluator) -> None:
    # at this point, df contains only the timestamps known at observation time
    # duplicate timestamps are null if the second event doesn't exist and timestamps that haven't happened yet are null
    df = pd.DataFrame(
        [
            # at this obs_time, there are no events detected
            {
                "first_patient_draped_datetime_local": pd.NaT,
                "second_patient_draped_datetime_local": pd.NaT,
            },
            # at this obs_time, we only know of one event
            {
                "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 12:55:00"),
                "second_patient_draped_datetime_local": pd.NaT,
            },
            # at this obs_time, we detected duplicate events
            {
                "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 12:55:00"),
                "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 13:55:00"),
            },
        ]
    )
    known_event_counts = default_evaluator.simulate_known_event_counts_at_inference(
        df=df,
        first_timestamp_column="first_patient_draped_datetime_local",
        second_timestamp_column="second_patient_draped_datetime_local",
    )

    assert len(known_event_counts) == 3
    assert known_event_counts[0] == 0  # first row had no events observed
    assert known_event_counts[1] == 1  # second row had one event observed
    assert known_event_counts[2] == 2  # third row had duplicates observed


def test_calculate_coverage_data(default_evaluator: Evaluator) -> None:
    df_test_with_predictions = pd.DataFrame(
        [
            # site 1: coverage of is_cleanest is 33%, is_predicted is 67% for obs_time_minutes_before_wheels_out = 10
            {
                "site_id": "site1",
                "case_id": "case1",
                "prediction_tag": "dynamic_end_prediction",  # is_cleanest_case = true, is_predicted = true
                "obs_time_minutes_before_wheels_out": 10,
            },
            {
                "site_id": "site1",
                "case_id": "case2",
                "prediction_tag": "surgeon_proc_combo_num_not_in_training",  # is_cleanest_case = false, is_predicted = false
                "obs_time_minutes_before_wheels_out": 10,
            },
            {
                "site_id": "site1",
                "case_id": "case3",
                "prediction_tag": "detected_duplicate_event_in_this_case",  # is_cleanest_case = false,is_predicted = true
                "obs_time_minutes_before_wheels_out": 10,
            },
            # this will be ignored when we test for calcs of "obs_time_minutes_before_wheels_out" = 10
            {
                "site_id": "site1",
                "case_id": "case3",
                "prediction_tag": "detected_duplicate_event_in_this_case",  # is_cleanest_case = false,is_predicted = true
                "obs_time_minutes_before_wheels_out": 20,
            },
            # site2: coverage of is_cleanest and is_predicted is 100%
            {
                "site_id": "site2",
                "case_id": "case1",
                "prediction_tag": "dynamic_end_prediction",  # is_cleanest_case = true, is_predicted = true
                "obs_time_minutes_before_wheels_out": 10,
            },
        ]
    )

    df_coverage_long = default_evaluator.calculate_coverage_data(
        df_test_with_predictions=df_test_with_predictions,
        coverage_at_obs_time_minutes_before_wheels_out=10,
    )

    df_site1_cleanest_coverage = df_coverage_long.query(
        'site_id == "site1" and coverage_type == "pct_cleanest_cases"'
    )
    df_site1_predicted_coverage = df_coverage_long.query(
        'site_id == "site1" and coverage_type == "pct_predicted_cases"'
    )
    df_site2_cleanest_coverage = df_coverage_long.query(
        'site_id == "site2" and coverage_type == "pct_cleanest_cases"'
    )

    assert round(list(df_site1_cleanest_coverage["percent_coverage"])[0]) == 33
    assert round(list(df_site1_predicted_coverage["percent_coverage"])[0]) == 67
    assert round(list(df_site2_cleanest_coverage["percent_coverage"])[0]) == 100
