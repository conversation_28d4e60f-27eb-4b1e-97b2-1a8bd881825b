from datetime import datetime
from unittest.mock import Magic<PERSON>ock

import numpy as np
import pandas as pd
import pytest
from sklearn.pipeline import Pipeline

from dynamic_case_end.configs.prod_config import DynamicModelConfig
from dynamic_case_end.model.dynamic_case_end_model import (
    DynamicCaseEndModel,
    DynamicEndPredictorInputData,
)

df_aggregate_phase_data = pd.DataFrame(
    [
        {
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "site_id": "site1",
            "agg_wheels_in_to_xfer_to_or_table": 10.0,
            "agg_xfer_to_or_table_to_draped": 10.0,
            "agg_draped_to_undraped": 60.0,
            "agg_undraped_to_xfer_to_bed": 10.0,
            "agg_xfer_to_bed_to_wheels_out": 10.0,
            "surgeon_proc_combo_num_appeared_in_training": 1,
        },
        {
            "first_primary_surgeon": "SurgeonB",
            "first_primary_procedure": "ProcedureB",
            "procedure_count": 1,
            "site_id": "site1",
            "agg_wheels_in_to_xfer_to_or_table": 5.0,
            "agg_xfer_to_or_table_to_draped": 5.0,
            "agg_draped_to_undraped": 30.0,
            "agg_undraped_to_xfer_to_bed": 5.0,
            "agg_xfer_to_bed_to_wheels_out": 5.0,
            "surgeon_proc_combo_num_appeared_in_training": 1,
        },
    ]
)

df_percent_phase_in_total_duration = pd.DataFrame(
    [
        {
            "site_id": "site1",
            "static_duration_group": "61-180",
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
        }
    ]
)

df_train_location = pd.DataFrame(
    [
        {
            "site_id": "site1",
            "static_duration_group": "61-180",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "wheels_in_to_first_xfer_to_or_table": 30.0,
            "first_xfer_to_or_table_to_first_draped": 30.0,
            "first_draped_to_first_undraped": 180.0,
            "first_undraped_to_first_xfer_to_bed": 30.0,
            "first_xfer_to_bed_to_wheels_out": 30.0,
            "actual_duration": 300.0,
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "agg_wheels_in_to_xfer_to_or_table": 20,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "agg_xfer_to_or_table_to_draped": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "agg_draped_to_undraped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "agg_xfer_to_bed_to_wheels_out": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "static_forecasted_duration_is_naive": 1,
        },
        {
            "site_id": "site1",
            "static_duration_group": "61-180",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "wheels_in_to_first_xfer_to_or_table": 10.0,
            "first_xfer_to_or_table_to_first_draped": 10.0,
            "first_draped_to_first_undraped": 60.0,
            "first_undraped_to_first_xfer_to_bed": 10.0,
            "first_xfer_to_bed_to_wheels_out": 10.0,
            "actual_duration": 100.0,
            "static_forecasted_duration": 200,
            "static_minus_scheduled_duration": 100,
            "agg_wheels_in_to_xfer_to_or_table": 20,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "agg_xfer_to_or_table_to_draped": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "agg_draped_to_undraped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "agg_undraped_to_xfer_to_bed": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "agg_xfer_to_bed_to_wheels_out": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "static_forecasted_duration_is_naive": 0,
        },
        # this has a negative phase duration and should be eliminated before calculating aggs
        {
            "site_id": "site1",
            "static_duration_group": "61-180",
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
            "procedure_count": 1,
            "wheels_in_to_first_xfer_to_or_table": -10.0,
            "first_xfer_to_or_table_to_first_draped": 10.0,
            "first_draped_to_first_undraped": -60.0,
            "first_undraped_to_first_xfer_to_bed": 10.0,
            "first_xfer_to_bed_to_wheels_out": 10.0,
            "actual_duration": 100.0,
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "agg_wheels_in_to_xfer_to_or_table": 20,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "agg_xfer_to_or_table_to_draped": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "agg_draped_to_undraped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "agg_undraped_to_xfer_to_bed": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "agg_xfer_to_bed_to_wheels_out": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "static_forecasted_duration_is_naive": 1,
        },
    ]
)


df_train_location_raw = pd.DataFrame(
    [
        # this data frame will be used to test training functions AND as an example of inputs to model at inference
        # to simulate inference inputs, all the timestamps are before the "observed time" of 2024-11-01 10:00:00
        # we assume that all the num_times_ counts have already been observed, so the timestamps available correspond to
        # the num_times_ counts
        # first, we have three cases with at least one event that's duped
        # (but not the same event is duped across all cases)
        # there's also a None that should be ignored
        {
            "site_id": "siteA",
            "case_id": "dupe_1",
            "first_primary_procedure": "ProcedureDupe",
            "num_times_patient_xfer_to_or_table_in_case": 2,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:05:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:50:00"),
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 09:55:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        {
            "site_id": "siteA",
            "case_id": "dupe_2",
            "first_primary_procedure": "ProcedureDupe",
            "num_times_patient_xfer_to_or_table_in_case": 2,
            "num_times_patient_draped_in_case": 2,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:05:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:50:00"),
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:40:00"),
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 09:55:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        {
            "site_id": "siteA",
            "case_id": "dupe_3",
            "first_primary_procedure": "ProcedureDupe",
            "num_times_patient_xfer_to_or_table_in_case": 1,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 3,
            "num_times_patient_xfer_to_bed_in_case": 0,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:05:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:55:00"),
            "first_patient_xfer_to_bed_datetime_local": pd.NaT,
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        # next, we have three cases with no dupes; there are some null timestamps
        # that shouldn't figure into calculations
        {
            "site_id": "siteA",
            "case_id": "regular_1",
            "first_primary_procedure": "ProcedureA",
            "num_times_patient_xfer_to_or_table_in_case": 0,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 0,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.NaT,
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        {
            "site_id": "siteA",
            "case_id": "regular_2",
            "first_primary_procedure": "ProcedureA",
            "num_times_patient_xfer_to_or_table_in_case": 0,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 0,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.NaT,
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        {
            "site_id": "siteA",
            "case_id": "regular_3",
            "first_primary_procedure": "ProcedureA",
            "num_times_patient_xfer_to_or_table_in_case": 1,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:05:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 09:55:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": 100,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
        # static forecasted duration is None, so this case should be removed
        {
            "site_id": "siteA",
            "case_id": "regular_4",
            "first_primary_procedure": "ProcedureA",
            "num_times_patient_xfer_to_or_table_in_case": 1,
            "num_times_patient_draped_in_case": 1,
            "num_times_patient_undraped_in_case": 1,
            "num_times_patient_xfer_to_bed_in_case": 1,
            "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
            "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:05:00"),
            "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
            "second_patient_draped_datetime_local": pd.NaT,
            "first_patient_undraped_datetime_local": pd.to_datetime("2024-11-01 09:30:00"),
            "second_patient_undraped_datetime_local": pd.NaT,
            "first_patient_xfer_to_bed_datetime_local": pd.to_datetime("2024-11-01 09:55:00"),
            "second_patient_xfer_to_bed_datetime_local": pd.NaT,
            "actual_end_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
            "static_forecasted_duration": None,
            "scheduled_duration": 100,
            "apella_data": 1,
        },
    ]
)

df_with_aggs_no_heuristics_needed = pd.DataFrame(
    {
        "obs_time": pd.to_datetime("2024-11-01 09:20:00"),
        "agg_wheels_in_to_xfer_to_or_table": 5.0,
        "wheels_in_occurred": 1,
        "first_xfer_to_or_table_occurred": 1,
        "wheels_in_to_first_xfer_to_or_table": 10.0,
        "actual_start_datetime_local": pd.to_datetime("2024-11-01 09:00:00"),
        "agg_xfer_to_or_table_to_draped": 10.0,
        "first_draped_occurred": 1,
        "first_xfer_table_to_first_draped": 8.0,
        "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime("2024-11-01 09:10:00"),
        "agg_draped_to_undraped": 30.0,
        "first_undraped_occurred": 0,
        "first_draped_to_first_undraped": None,
        "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 09:18:00"),
        "agg_undraped_to_xfer_to_bed": 2.0,
        "first_xfer_to_bed_occurred": 0,
        "first_undraped_to_first_xfer_bed": 3.0,
        "first_patient_undraped_datetime_local": pd.NaT,
        "agg_xfer_to_bed_to_wheels_out": 2.0,
        "wheels_out_occurred": 0,
        "first_xfer_to_bed_to_wheels_out": None,
        "first_patient_xfer_to_bed_datetime_local": pd.NaT,
        "static_forecasted_duration": 100,
        "static_minus_scheduled_duration": 0,
        "procedure_count": 1,
        "static_forecasted_duration_is_naive": 1,
        "pct_wheels_in_to_first_xfer_to_or_table": 20,
        "pct_first_xfer_to_or_table_to_first_draped": 20,
        "first_xfer_to_or_table_to_first_draped": 20,
        "pct_first_draped_to_first_undraped": 20,
        "pct_first_undraped_to_first_xfer_to_bed": 20,
        "pct_first_xfer_to_bed_to_wheels_out": 20,
        "first_primary_surgeon": "SurgeonA",
        "first_primary_procedure": "ProcedureA",
    },
    index=[0],
)

for col in ["first_patient_undraped_datetime_local", "first_patient_xfer_to_bed_datetime_local"]:
    df_with_aggs_no_heuristics_needed[col] = pd.to_datetime(df_with_aggs_no_heuristics_needed[col])


@pytest.fixture
def mock_ml_model() -> Pipeline:
    mock_ml_model = MagicMock(Pipeline)
    mock_ml_model.predict.return_value = 10
    mock_ml_model.fit = MagicMock()
    return mock_ml_model


@pytest.fixture
def mock_ml_model_dict(mock_ml_model: Pipeline) -> dict[str, Pipeline]:
    # given return value of mock_ml_model, all phases will be predicted as 10 minutes
    model_dict = {
        "wheels_in_to_first_xfer_to_or_table": mock_ml_model,
        "first_xfer_to_or_table_to_first_draped": mock_ml_model,
        "first_draped_to_first_undraped": mock_ml_model,
        "first_undraped_to_first_xfer_to_bed": mock_ml_model,
        "first_xfer_to_bed_to_wheels_out": mock_ml_model,
    }
    return model_dict


@pytest.fixture
def default_dynamic_case_end_model(mock_ml_model_dict: dict[str, Pipeline]) -> DynamicCaseEndModel:
    dynamic_case_end_model = DynamicCaseEndModel(
        location="org1",
        config=DynamicModelConfig(
            pct_duplicate_cutoff=25,
            max_factor_allowed_for_running_duration=3,
            scheduled_duration_bins=[0, 60, 180, 270, 5000],
            scheduled_duration_bin_labels=["0-60", "61-180", "181-270", "271-5000"],
            num_features=[
                "static_forecasted_duration",
                "static_minus_scheduled_duration",
                "median_phase_by_surgeon_proc_num_procs",
                "pct_phase_in_total_duration",
                "procedure_count",
            ],
            cat_features=[
                "static_forecasted_duration_is_naive",
                "first_primary_procedure",
                "first_primary_surgeon",
            ],
        ),
    )
    dynamic_case_end_model.trained_models = mock_ml_model_dict

    return dynamic_case_end_model


def get_aggregate_phase_data(
    self: DynamicCaseEndModel, df_test_location: pd.DataFrame
) -> pd.DataFrame:
    return df_aggregate_phase_data


def test_make_single_prediction_with_actual_phase_over_limit(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 31),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        # wheels in to xfer to or table is over 3x the agg limit (30), but don't want to trigger a
        # suspected missing event b/c this actually happened
        first_patient_xfer_to_or_table_datetime_local=datetime(2024, 12, 2, 10, 30),
        num_times_patient_xfer_to_or_table_in_case=1,
        first_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=0,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
        actual_end_datetime_local=None,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    # ml estimate for all phases is 10 minutes
    # prediction = 90 (actual) + 10 + 10 + 10 + 10 = 130

    assert result.predicted_total_case_duration == 130.0
    assert result.prediction_tag == "dynamic_end_prediction"


def test_make_single_prediction_pythia_vs_static_fuse(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        static_forecasted_duration=300,  # static forecasted duration is very high compared to prediction
        obs_time=datetime(2024, 12, 2, 9, 20),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=datetime(2024, 12, 2, 9, 10),
        num_times_patient_xfer_to_or_table_in_case=1,
        first_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=0,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
        actual_end_datetime_local=None,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    # ml estimate for all phases is 10 minutes
    # prediction = 10 (actual) + 10 + 10 + 10 + 10 = 50
    # this is more than 90 minutes lower than 300 min static forecast
    assert result.prediction_tag == "dynamic_end_prediction_much_lower_than_static"


def test_make_single_prediction_after_case_ended(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 12, 0),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=datetime(2024, 12, 2, 9, 15),
        num_times_patient_xfer_to_or_table_in_case=1,
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 9, 30),
        num_times_patient_draped_in_case=1,
        first_patient_undraped_datetime_local=datetime(2024, 12, 2, 10, 30),
        num_times_patient_undraped_in_case=1,
        first_patient_xfer_to_bed_datetime_local=datetime(2024, 12, 2, 10, 45),
        num_times_patient_xfer_to_bed_in_case=1,
        actual_end_datetime_local=datetime(
            2024, 12, 2, 11, 0
        ),  # case ended one hour before obs_time
        wheels_out_occurred=1,
        num_times_wheels_out_in_case=1,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )

    # after case ended, we should predict the actual duration
    assert result.predicted_total_case_duration == 120.0
    assert result.prediction_tag == "dynamic_end_prediction"


def test_make_single_prediction_before_case_started(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # here, the case hasn't started so there are no timestamps
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 9, 6),
        actual_start_datetime_local=None,
        first_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
        first_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=0,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )

    # ml estimate for all phases is 10 minutes
    # prediction = 10 + 10 + 10 + 10 + 10 = 50
    # before the case starts, we simply predict the sum of the ml estimates
    assert result.predicted_total_case_duration == 50.0
    assert result.prediction_tag == "dynamic_end_prediction"


def test_make_single_prediction_missing_event1_completed_event2(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # here, the xfer to or table is missing. once the obs_time reaches 3x the agg value, this will
    # result in a suspected missing event (i.e. if 3x the historical agg has passed and xfer to table hasn't happened)
    # before 3x the agg value is reached, we don't suspect a missing event even after patient_draped has occurred
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 9, 6),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
        first_patient_draped_datetime_local=datetime(
            2024, 12, 2, 9, 5
        ),  # draped has happened, but xfer to bed hasn't
        num_times_patient_draped_in_case=1,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    assert result.prediction_tag == "suspected_missing_event"


def test_make_single_prediction_events_out_of_order_for_completed_phase(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 0),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=datetime(
            2024, 12, 2, 9, 15
        ),  # xfer to table is after draped
        num_times_patient_xfer_to_or_table_in_case=1,
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 9, 5),
        num_times_patient_draped_in_case=1,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    assert result.prediction_tag == "events_out_of_order"


def test_make_single_prediction_events_out_of_order_before_detection(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # this is an event-out-of-order situation, but before the first event is detected
    # so only the second event has occurred
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 0),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=None,  # this should have happened, but hasn't
        num_times_patient_xfer_to_or_table_in_case=0,
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 9, 5),
        num_times_patient_draped_in_case=1,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    assert result.prediction_tag == "suspected_missing_event"


def test_make_single_prediction_simple_dynamic_prediction(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    data_for_prediction = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 9, 20),
        actual_start_datetime_local=datetime(2024, 12, 2, 9, 0),
        first_patient_xfer_to_or_table_datetime_local=datetime(2024, 12, 2, 9, 5),
        num_times_patient_xfer_to_or_table_in_case=1,
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 9, 10),
        num_times_patient_draped_in_case=1,
        first_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
    )

    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )
    # ml estimate for all phases is 10 minutes
    # predicted phase durations: 5 (actual) + 5 (actual) + 10 + 10 + 10 = 40 minutes total
    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction, inference_mode=False
    )
    assert result.predicted_total_case_duration == 40
    assert result.prediction_tag == "dynamic_end_prediction"


def test_run_model_prediction_for_df(default_dynamic_case_end_model: DynamicCaseEndModel) -> None:
    df_test_site = pd.DataFrame(
        [
            # happy path
            {
                "case_id": "case1",
                "site_id": "site1",
                "first_primary_surgeon": "SurgeonA",
                "first_primary_procedure": "ProcedureA",
                "procedure_count": 1,
                "scheduled_duration": 100,
                "obs_time": pd.to_datetime("2024-11-01 10:40:00"),
                "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.to_datetime(
                    "2024-11-01 10:15:00"
                ),
                "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
                "num_times_patient_xfer_to_or_table_in_case": 1,
                "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 10:30:00"),
                "second_patient_draped_datetime_local": pd.NaT,
                "num_times_patient_draped_in_case": 1,
                "first_patient_undraped_datetime_local": pd.NaT,
                "second_patient_undraped_datetime_local": pd.NaT,
                "num_times_patient_undraped_in_case": 0,
                "first_patient_xfer_to_bed_datetime_local": pd.NaT,
                "second_patient_xfer_to_bed_datetime_local": pd.NaT,
                "num_times_patient_xfer_to_bed_in_case": 0,
                "wheels_out_occurred": 0,
                "num_times_wheels_out_in_case": 0,
                # these are not simulated at obs time; they're required for evaluation
                "actual_end_datetime_local": pd.to_datetime("2024-11-01 13:00:00"),
                "actual_duration": 180,
            },
            # suspected missing event
            {
                "case_id": "case2",
                "site_id": "site1",
                "first_primary_surgeon": "SurgeonA",
                "first_primary_procedure": "ProcedureA",
                "procedure_count": 1,
                "scheduled_duration": 100,
                "obs_time": pd.to_datetime("2024-11-01 11:00:00"),
                "actual_start_datetime_local": pd.to_datetime("2024-11-01 10:00:00"),
                "first_patient_xfer_to_or_table_datetime_local": pd.NaT,
                "second_patient_xfer_to_or_table_datetime_local": pd.NaT,
                "num_times_patient_xfer_to_or_table_in_case": 0,
                "first_patient_draped_datetime_local": pd.NaT,
                "second_patient_draped_datetime_local": pd.NaT,
                "num_times_patient_draped_in_case": 0,
                "first_patient_undraped_datetime_local": pd.NaT,
                "second_patient_undraped_datetime_local": pd.NaT,
                "num_times_patient_undraped_in_case": 0,
                "first_patient_xfer_to_bed_datetime_local": pd.NaT,
                "second_patient_xfer_to_bed_datetime_local": pd.NaT,
                "num_times_patient_xfer_to_bed_in_case": 0,
                "wheels_out_occurred": 0,
                "num_times_wheels_out_in_case": 0,
                # these are not simulated at obs time; they're required for evaluation
                "actual_end_datetime_local": pd.to_datetime("2024-11-01 13:00:00"),
                "actual_duration": 180,
            },
        ]
    )
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    df_predictions = default_dynamic_case_end_model.run_model_prediction_for_df(
        df_test_site, inference_mode=False
    )

    df_case1_predictions = df_predictions.iloc[0]
    df_case2_predictions = df_predictions.iloc[1]

    assert df_case1_predictions["prediction_tag"] == "dynamic_end_prediction"
    # 15 (actual) + 15 (actual) + 10 (ml estimate) + 10 + 10
    assert df_case1_predictions["predicted_total_case_duration"] == (15 + 15 + 10 + 10 + 10)
    assert df_case2_predictions["prediction_tag"] == "suspected_missing_event"
    assert pd.isnull(df_case2_predictions["predicted_total_case_duration"])


def test_calculate_features_for_inference(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # static duration is missing, but finding the static_duration_group should default to scheduled duration
    data_for_prediction_no_static_forecast = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="surgeonA",
        first_primary_procedure="procA",
        procedure_count=1,
        scheduled_duration=100,
        static_forecasted_duration=None,
        obs_time=datetime(2024, 12, 2, 10, 30),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 10),
        second_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 20),
        num_times_patient_draped_in_case=2,
        first_patient_xfer_to_bed_datetime_local=datetime(2024, 12, 2, 10, 1),
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=1,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_or_table_datetime_local=None,
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
    )

    df = default_dynamic_case_end_model.calculate_features_for_inference(
        data_for_prediction_no_static_forecast
    )

    assert np.isnan(list(df["static_minus_scheduled_duration"])[0])
    assert list(df["static_forecasted_duration_is_naive"])[0] == 0
    assert list(df["static_duration_group"])[0] == "61-180"


def test_apply_predicted_phase_duration_to_all_phases(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df_with_predictions = (
        default_dynamic_case_end_model.apply_predicted_phase_duration_to_all_phases(
            df_with_aggs_no_heuristics_needed, default_dynamic_case_end_model.trained_models
        )
    )

    assert "predicted_wheels_in_to_xfer_to_or_table" in df_with_predictions.columns
    assert "predicted_xfer_to_or_table_to_draped" in df_with_predictions.columns
    assert "predicted_draped_to_undraped" in df_with_predictions.columns
    assert "predicted_undraped_to_xfer_to_bed" in df_with_predictions.columns
    assert "predicted_xfer_to_bed_to_wheels_out" in df_with_predictions.columns


def test_generate_phase_prediction_before_phase_starts(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df = pd.DataFrame(
        {
            "obs_time": pd.to_datetime("2024-11-01 09:00:00"),
            "agg_draped_to_undraped": 20.0,
            "first_draped_occurred": 0,
            "first_undraped_occurred": 0,
            "first_draped_to_first_undraped": None,
            "first_patient_draped_datetime_local": pd.NaT,
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "procedure_count": 1,
            "static_forecasted_duration_is_naive": 1,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
        },
        index=[0],
    )

    df["first_patient_draped_datetime_local"] = pd.to_datetime(
        df["first_patient_draped_datetime_local"]
    )

    df_pred = default_dynamic_case_end_model.generate_phase_prediction(
        df=df,
        agg_colname="agg_draped_to_undraped",
        occurred_colname_first_event1="first_draped_occurred",
        occurred_colname_first_event2="first_undraped_occurred",
        first_actual_phase_duration_colname="first_draped_to_first_undraped",
        first_event1_datetime_colname="first_patient_draped_datetime_local",
        trained_models=default_dynamic_case_end_model.trained_models,
    )

    assert (
        list(df_pred)[0] == 10.0
    )  # before the phase starts, predicted phase duration equals the ml estimate


def test_generate_phase_prediction_phase_started_before_ml_estimate(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df = pd.DataFrame(
        {
            "obs_time": pd.to_datetime("2024-11-01 09:00:00"),
            "agg_draped_to_undraped": 20.0,
            "first_draped_occurred": 1,
            "first_undraped_occurred": 0,
            "first_draped_to_first_undraped": None,
            "first_patient_draped_datetime_local": pd.to_datetime(
                "2024-11-01 08:55:00"
            ),  # draped only 5 minutes ago
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "procedure_count": 1,
            "static_forecasted_duration_is_naive": 1,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
        },
        index=[0],
    )

    df["first_patient_draped_datetime_local"] = pd.to_datetime(
        df["first_patient_draped_datetime_local"]
    )

    df_pred = default_dynamic_case_end_model.generate_phase_prediction(
        df=df,
        agg_colname="agg_draped_to_undraped",
        occurred_colname_first_event1="first_draped_occurred",
        occurred_colname_first_event2="first_undraped_occurred",
        first_actual_phase_duration_colname="first_draped_to_first_undraped",
        first_event1_datetime_colname="first_patient_draped_datetime_local",
        trained_models=default_dynamic_case_end_model.trained_models,
    )

    # after the phase starts but before ml estimate is reached, predicted phase duration equals the ml estimate
    assert list(df_pred)[0] == 10.0


def test_generate_phase_prediction_phase_started_after_ml_estimate(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df = pd.DataFrame(
        {
            "obs_time": pd.to_datetime("2024-11-01 09:00:00"),
            "agg_draped_to_undraped": 20.0,
            "first_draped_occurred": 1,
            "first_undraped_occurred": 0,
            "first_draped_to_first_undraped": None,
            "first_patient_draped_datetime_local": pd.to_datetime(
                "2024-11-01 08:35:00"
            ),  # draped 25 minutes ago
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "procedure_count": 1,
            "static_forecasted_duration_is_naive": 1,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
        },
        index=[0],
    )

    df["first_patient_draped_datetime_local"] = pd.to_datetime(
        df["first_patient_draped_datetime_local"]
    )

    df_pred = default_dynamic_case_end_model.generate_phase_prediction(
        df=df,
        agg_colname="agg_draped_to_undraped",
        occurred_colname_first_event1="first_draped_occurred",
        occurred_colname_first_event2="first_undraped_occurred",
        first_actual_phase_duration_colname="first_draped_to_first_undraped",
        first_event1_datetime_colname="first_patient_draped_datetime_local",
        trained_models=default_dynamic_case_end_model.trained_models,
    )

    # after the phase starts and after aggregate value is reached, predicted phase duration equals the running duration
    assert list(df_pred)[0] == 25.0


def test_generate_phase_prediction_phase_started_beyond_limit(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df = pd.DataFrame(
        {
            "obs_time": pd.to_datetime("2024-11-01 09:00:00"),
            "agg_draped_to_undraped": 20.0,
            "first_draped_occurred": 1,
            "first_undraped_occurred": 0,
            "first_draped_to_first_undraped": None,
            "first_patient_draped_datetime_local": pd.to_datetime(
                "2024-11-01 07:30:00"
            ),  # draped 90 minutes ago
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "procedure_count": 1,
            "static_forecasted_duration_is_naive": 1,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
        },
        index=[0],
    )

    df["first_patient_draped_datetime_local"] = pd.to_datetime(
        df["first_patient_draped_datetime_local"]
    )

    df_pred = default_dynamic_case_end_model.generate_phase_prediction(
        df=df,
        agg_colname="agg_draped_to_undraped",
        occurred_colname_first_event1="first_draped_occurred",
        occurred_colname_first_event2="first_undraped_occurred",
        first_actual_phase_duration_colname="first_draped_to_first_undraped",
        first_event1_datetime_colname="first_patient_draped_datetime_local",
        trained_models=default_dynamic_case_end_model.trained_models,
    )

    # after the phase starts and after 3x ml estimate is reached, predicted phase duration equals 3x the ml estimate
    assert list(df_pred)[0] == 30.0


def test_generate_phase_prediction_phase_ended(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df = pd.DataFrame(
        {
            "obs_time": pd.to_datetime("2024-11-01 09:00:00"),
            "agg_draped_to_undraped": 20.0,
            "first_draped_occurred": 1,
            "first_undraped_occurred": 1,
            "first_draped_to_first_undraped": 25.0,  # undraped at 8:55
            "first_patient_draped_datetime_local": pd.to_datetime("2024-11-01 08:30:00"),
            "static_forecasted_duration": 100,
            "static_minus_scheduled_duration": 0,
            "procedure_count": 1,
            "static_forecasted_duration_is_naive": 1,
            "pct_wheels_in_to_first_xfer_to_or_table": 20,
            "pct_first_xfer_to_or_table_to_first_draped": 20,
            "first_xfer_to_or_table_to_first_draped": 20,
            "pct_first_draped_to_first_undraped": 20,
            "pct_first_undraped_to_first_xfer_to_bed": 20,
            "pct_first_xfer_to_bed_to_wheels_out": 20,
            "first_primary_surgeon": "SurgeonA",
            "first_primary_procedure": "ProcedureA",
        },
        index=[0],
    )

    df["first_patient_draped_datetime_local"] = pd.to_datetime(
        df["first_patient_draped_datetime_local"]
    )

    df_pred = default_dynamic_case_end_model.generate_phase_prediction(
        df=df,
        agg_colname="agg_draped_to_undraped",
        occurred_colname_first_event1="first_draped_occurred",
        occurred_colname_first_event2="first_undraped_occurred",
        first_actual_phase_duration_colname="first_draped_to_first_undraped",
        first_event1_datetime_colname="first_patient_draped_datetime_local",
        trained_models=default_dynamic_case_end_model.trained_models,
    )

    # after the phase ends, predicted phase duration is equal to observed value
    assert df_pred[0] == 25.0


def test_make_single_prediction_detected_duplicate_event(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )

    data_for_prediction_with_duplicates_detected = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="surgeonA",
        first_primary_procedure="procA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 30),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 10),
        second_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 20),
        num_times_patient_draped_in_case=2,
        first_patient_xfer_to_bed_datetime_local=datetime(2024, 12, 2, 10, 1),
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=1,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_or_table_datetime_local=None,
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction_with_duplicates_detected, inference_mode=False
    )
    assert result.prediction_tag == "detected_duplicate_event_in_this_case"


def test_make_single_prediction_surgeon_proc_not_in_training(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # even if surgeon-proc not in training, the model should still give a dynamic prediction
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    data_for_prediction_surgeon_proc_not_in_training = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="newsurgeon",
        first_primary_procedure="newproc",
        procedure_count=2,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 30),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 10),
        second_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=1,
        first_patient_xfer_to_bed_datetime_local=None,
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=1,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_or_table_datetime_local=datetime(2024, 12, 2, 10, 1),
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=1,
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction_surgeon_proc_not_in_training, inference_mode=False
    )
    assert result.prediction_tag == "dynamic_end_prediction"
    # 1 (actual) + 9 (actual) + 20 (running duration) + 10 + 10
    assert result.predicted_total_case_duration == (1 + 9 + 20 + 10 + 10)


def test_make_single_prediction_dupes_in_training(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["ProcedureA"]}
    )

    data_for_prediction_dupes_in_training = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        procedure_count=1,
        scheduled_duration=100,
        # this matches the procedure name in procedures_with_high_duplicate_events
        first_primary_procedure="ProcedureA",
        obs_time=datetime(2024, 12, 2, 10, 30),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        first_patient_draped_datetime_local=datetime(2024, 12, 2, 10, 10),
        second_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=1,
        first_patient_xfer_to_bed_datetime_local=datetime(2024, 12, 2, 10, 1),
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=1,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_or_table_datetime_local=None,
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction_dupes_in_training, inference_mode=False
    )
    assert result.prediction_tag == "procedure_historically_had_duplicate_events"


def test_make_single_prediction_suspected_missing_event(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )
    default_dynamic_case_end_model.procedures_with_only_xfer_table_bed = pd.DataFrame(
        {"site_id": ["site1"], "first_primary_procedure": ["Procedure_With_Only_Xfer"]}
    )

    data_for_prediction_suspected_missing_event = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 50),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        # 50 minutes since wheels in and no xfer to or table is 5x the ml estimate for this surgeon-proc-combo-
        # so we would suspect that xfer to or table is missing
        first_patient_xfer_to_or_table_datetime_local=None,
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
        first_patient_draped_datetime_local=None,
        second_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=0,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
        actual_end_datetime_local=None,
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction_suspected_missing_event, inference_mode=False
    )
    assert result.prediction_tag == "suspected_missing_event"


def test_make_single_prediction_inference_before_draped(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.aggregate_phase_data = df_aggregate_phase_data
    default_dynamic_case_end_model.percent_phase_in_total_duration = (
        df_percent_phase_in_total_duration
    )
    default_dynamic_case_end_model.procedures_with_high_duplicate_events = pd.DataFrame(
        {"first_primary_procedure": ["Procedure_With_Dupes"]}
    )

    data_for_prediction_suspected_missing_event = DynamicEndPredictorInputData(
        case_id="case1",
        site_id="site1",
        first_primary_surgeon="SurgeonA",
        first_primary_procedure="ProcedureA",
        procedure_count=1,
        scheduled_duration=100,
        obs_time=datetime(2024, 12, 2, 10, 10),
        actual_start_datetime_local=datetime(2024, 12, 2, 10, 0),
        first_patient_xfer_to_or_table_datetime_local=None,
        second_patient_xfer_to_or_table_datetime_local=None,
        num_times_patient_xfer_to_or_table_in_case=0,
        first_patient_draped_datetime_local=None,
        second_patient_draped_datetime_local=None,
        num_times_patient_draped_in_case=0,
        first_patient_undraped_datetime_local=None,
        second_patient_undraped_datetime_local=None,
        num_times_patient_undraped_in_case=0,
        first_patient_xfer_to_bed_datetime_local=None,
        second_patient_xfer_to_bed_datetime_local=None,
        num_times_patient_xfer_to_bed_in_case=0,
        actual_end_datetime_local=None,
    )

    result = default_dynamic_case_end_model.make_single_prediction(
        data_for_prediction_suspected_missing_event, inference_mode=True
    )
    assert result.prediction_tag == "no_pythia_predictions_before_patient_xfer_to_or_table"


def test_calculate_actual_phase_durations(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df_inference_input = df_train_location_raw.copy()
    # didn't add this column into df_train_location_raw
    # becauase that table doubles as input for training pipeline, and we won't have obs_time for training
    df_inference_input["obs_time"] = pd.to_datetime("2024-11-01 10:00:00")

    df = default_dynamic_case_end_model.calculate_actual_phase_durations(df_inference_input)
    df_sample_case = df[df["case_id"] == "dupe_3"]

    assert list(df_sample_case["wheels_in_to_first_xfer_to_or_table"])[0] == 5.0
    assert list(df_sample_case["first_xfer_to_or_table_to_first_draped"])[0] == 5.0
    assert list(df_sample_case["first_draped_to_first_undraped"])[0] == 20.0
    assert np.isnan(list(df_sample_case["first_undraped_to_first_xfer_to_bed"])[0])
    assert np.isnan(list(df_sample_case["first_xfer_to_bed_to_wheels_out"])[0])

    # xfer to bed already occurred in this case
    # use it to test the approximation that this phase as obs_time minus xfer to bed
    df_sample_case_2 = df[df["case_id"] == "dupe_2"]
    assert list(df_sample_case_2["first_xfer_to_bed_to_wheels_out"])[0] == 5.0


def test_calculate_is_occurred_columns(default_dynamic_case_end_model: DynamicCaseEndModel) -> None:
    df_inference_input = df_train_location_raw.copy()
    # didn't add this column into df_train_location_raw
    # becauase that table doubles as input for training pipeline, and we won't have obs_time for training
    df_inference_input["obs_time"] = pd.to_datetime("2024-11-01 10:00:00")

    df = default_dynamic_case_end_model.calculate_is_occurred_columns(df_inference_input)

    df_sample_case = df[df["case_id"] == "dupe_3"]

    assert len(df) == len(df_inference_input)
    assert list(df_sample_case["wheels_in_occurred"])[0] == 1
    assert list(df_sample_case["first_xfer_to_or_table_occurred"])[0] == 1
    assert list(df_sample_case["second_xfer_to_or_table_occurred"])[0] == 0
    assert list(df_sample_case["first_draped_occurred"])[0] == 1
    assert list(df_sample_case["second_draped_occurred"])[0] == 0
    assert list(df_sample_case["first_undraped_occurred"])[0] == 1
    assert list(df_sample_case["second_undraped_occurred"])[0] == 1
    assert list(df_sample_case["first_xfer_to_bed_occurred"])[0] == 0
    assert list(df_sample_case["second_xfer_to_bed_occurred"])[0] == 0


def test_get_num_times_colname(default_dynamic_case_end_model: DynamicCaseEndModel) -> None:
    assert (
        default_dynamic_case_end_model._get_num_times_colname(
            "second_patient_xfer_to_or_table_datetime_local"
        )
        == "num_times_patient_xfer_to_or_table_in_case"
    )
    assert (
        default_dynamic_case_end_model._get_num_times_colname(
            "second_patient_draped_datetime_local"
        )
        == "num_times_patient_draped_in_case"
    )
    assert (
        default_dynamic_case_end_model._get_num_times_colname(
            "second_patient_undraped_datetime_local"
        )
        == "num_times_patient_undraped_in_case"
    )
    assert (
        default_dynamic_case_end_model._get_num_times_colname(
            "second_patient_xfer_to_bed_datetime_local"
        )
        == "num_times_patient_xfer_to_bed_in_case"
    )


def test_get_event_occurred_colname(default_dynamic_case_end_model: DynamicCaseEndModel) -> None:
    assert (
        default_dynamic_case_end_model._get_event_occurred_colname("actual_start_datetime_local")
        == "wheels_in_occurred"
    )
    assert (
        default_dynamic_case_end_model._get_event_occurred_colname(
            "first_patient_xfer_to_or_table_datetime_local"
        )
        == "first_xfer_to_or_table_occurred"
    )
    assert (
        default_dynamic_case_end_model._get_event_occurred_colname(
            "first_patient_draped_datetime_local"
        )
        == "first_draped_occurred"
    )
    assert (
        default_dynamic_case_end_model._get_event_occurred_colname(
            "second_patient_undraped_datetime_local"
        )
        == "second_undraped_occurred"
    )
    assert (
        default_dynamic_case_end_model._get_event_occurred_colname(
            "second_patient_xfer_to_bed_datetime_local"
        )
        == "second_xfer_to_bed_occurred"
    )


def test_clean_up_training_data(default_dynamic_case_end_model: DynamicCaseEndModel) -> None:
    df_train_cleaned = default_dynamic_case_end_model.clean_up_training_data(df_train_location_raw)

    # only one case has no missing and no duplicate events
    assert len(df_train_cleaned) == 1
    assert list(df_train_cleaned["case_id"])[0] == "regular_3"

    assert list(df_train_cleaned["static_minus_scheduled_duration"])[0] == 0
    assert list(df_train_cleaned["static_forecasted_duration_is_naive"])[0] == 1
    assert list(df_train_cleaned["static_duration_group"])[0] == "61-180"


def test_identify_procedures_with_duplicates(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.identify_procedures_with_duplicates(df_train_location_raw)

    df_duplicate_procs = default_dynamic_case_end_model.procedures_with_high_duplicate_events

    assert len(df_duplicate_procs) == 1
    assert df_duplicate_procs["first_primary_procedure"].iloc[0] == "ProcedureDupe"
    # for ProcedureDupe, the most-duplicated event was patient_xfer_to_or_table, with 2 out of 3 cases with dupes
    assert round(df_duplicate_procs["pct_cases_with_any_event_duplicate"].iloc[0]) == 67


def test_calculate_training_aggregations(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    default_dynamic_case_end_model.calculate_training_aggregations(df_train_location)

    df_agg = default_dynamic_case_end_model.aggregate_phase_data
    df_pcts = default_dynamic_case_end_model.percent_phase_in_total_duration

    assert df_agg["agg_wheels_in_to_xfer_to_or_table"].iloc[0] == 20.0
    assert df_agg["agg_draped_to_undraped"].iloc[0] == 120.0

    assert df_pcts["pct_wheels_in_to_first_xfer_to_or_table"].iloc[0] == 10.0
    assert df_pcts["pct_first_draped_to_first_undraped"].iloc[0] == 60.0


def test_train_model_for_all_phases(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    # if setting as int, it doesn't work... but during the run, this feature IS an int...
    df_train_location["static_forecasted_duration_is_naive"] = df_train_location[
        "static_forecasted_duration_is_naive"
    ].astype(str)

    default_dynamic_case_end_model.train_model_for_all_phases(df_train_location)

    model_names = default_dynamic_case_end_model.trained_models.keys()

    # test that the models are saved under phase names
    assert "wheels_in_to_first_xfer_to_or_table" in model_names
    assert "first_xfer_to_bed_to_wheels_out" in model_names


def test_get_ml_model_pipeline(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df_features = pd.DataFrame(
        [
            {
                "procedure_count": 1,
                "static_forecasted_duration": None,
                "static_minus_scheduled_duration": None,
                "agg_wheels_in_to_xfer_to_or_table": None,
                "pct_wheels_in_to_first_xfer_to_or_table": 20,
                "static_forecasted_duration_is_naive": 0,
                "first_primary_surgeon": "SurgeonA",
                "first_primary_procedure": "ProcedureA",
            }
        ]
    )

    phase_name = "wheels_in_to_first_xfer_to_or_table"

    # adjust the feature names based on the phase
    num_features = [
        feature
        for feature in default_dynamic_case_end_model.config.num_features
        if feature not in ("median_phase_by_surgeon_proc_num_procs", "pct_phase_in_total_duration")
    ]

    if (
        "median_phase_by_surgeon_proc_num_procs"
        in default_dynamic_case_end_model.config.num_features
    ):
        num_features.append("agg_" + phase_name.replace("first_", ""))
    if "pct_phase_in_total_duration" in default_dynamic_case_end_model.config.num_features:
        num_features.append("pct_" + phase_name)

    features = num_features + default_dynamic_case_end_model.config.cat_features

    model_pipeline = default_dynamic_case_end_model.get_ml_model_pipeline(
        cat_features=default_dynamic_case_end_model.config.cat_features, num_features=num_features
    )

    preprocessor = model_pipeline.steps[0][1]

    processed_features = preprocessor.fit_transform(df_features[features])[0]  # unpack nested list

    # ensure no new features were added during transformations
    assert len(processed_features) == len(default_dynamic_case_end_model.config.cat_features) + len(
        default_dynamic_case_end_model.config.num_features
    )
    # if median phase duration by surgeon-proc-combo-num doesn't exist, then it's replaced with -1 filler
    assert processed_features[0] == -1
    # if static_minus_scheduled_duration doesn't exist, then it's replaced with 0
    assert processed_features[1] == 0
    assert processed_features[2] is None


def test_augment_training_data(
    default_dynamic_case_end_model: DynamicCaseEndModel,
) -> None:
    df_augmented = default_dynamic_case_end_model.augment_training_data(df_train_location)

    # there should be equal number of rows with null agg values as the original train data
    assert len(df_augmented[df_augmented["agg_wheels_in_to_xfer_to_or_table"].isnull()]) == len(
        df_train_location
    )
