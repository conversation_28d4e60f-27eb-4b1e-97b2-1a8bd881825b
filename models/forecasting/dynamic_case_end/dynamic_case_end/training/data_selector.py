import logging
from typing import Any, <PERSON><PERSON>

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from google.cloud.bigquery import Client as BQClient
from training_utils.clearml_reporter import ClearMLReporter

from dynamic_case_end.configs.prod_config import DataSelectionConfig

logger = logging.getLogger(__name__)

MINIMUM_LIVE_DATA_MONTHS = 6


class DataSelector:
    def __init__(
        self,
        bq_client: BQClient,
        config: DataSelectionConfig,
        reporter: ClearMLReporter,
    ):
        self.bq_client = bq_client
        self.config = config
        self.reporter = reporter

    def generate_data_for_fit(self) -> Tuple[pd.DataFrame, pd.DataFrame, list[str]]:
        query_string_for_raw_events = self.get_query_string_for_raw_events()
        query_string_for_all_cases = self.get_query_string_for_all_case_features()
        query_string_for_static_forecasts = self.get_query_string_for_static_case_predictions()
        query_string_for_transformer_forecasts = self.get_query_string_for_transformer_predicitons()

        # df_events_long has one row per event
        df_events_long = self.query_for_data(query_string_for_raw_events)
        df_all_cases = self.query_for_data(query_string_for_all_cases)
        df_static_forecasts = self.query_for_data(query_string_for_static_forecasts)
        df_transformer_forecasts = self.query_for_data(query_string_for_transformer_forecasts)
        # df_events_pivoted has one row per case, and contains all events for that case
        # (including potential duplicate events). It also contains ALL cases, and cases
        # that don't have an event will contain null values for the event timestamp
        df_events_pivoted = self.pivot_events(df_events_long, df_all_cases)

        # add in static duration forecasts
        df_events_pivoted_with_static_forecast = df_events_pivoted.merge(
            df_static_forecasts, on="case_id", how="left"
        )

        df_events_pivoted_with_static_forecast["static_forecasted_duration"] = (
            df_events_pivoted_with_static_forecast["static_forecasted_duration"].astype("Int64")
        )

        df_transformer_forecasts["transformer_forecasted_duration"] = df_transformer_forecasts[
            "transformer_forecasted_duration"
        ].astype(float)

        df_events_pivoted_with_static_forecast.to_csv("df_events_pivoted_with_static_forecast.csv")

        # df_events_pivoted_with_static_forecast has only apella live data joined to events
        # df_all_cases has historical + apella live data, intended to generate aggregate features

        # get list of sites with enough training data for fair evaluation
        sites_to_evaluate = self.calculate_sites_to_evaluate(
            df_events_pivoted=df_events_pivoted_with_static_forecast
        )

        # calculate the occurrence of common edge order-of-events by site such as:
        # xfer_to_or_table --> xfer_to_bed (no draping)
        # patient_draped --> patient_undraped (no xfer to table or bed)
        # no events
        # this can help with interpreting low Pythia performance
        self.calculate_edge_case_frequency(
            df_events_pivoted_with_static_forecast, sites_to_evaluate
        )
        return df_events_pivoted_with_static_forecast, df_transformer_forecasts, sites_to_evaluate

    def calculate_edge_case_frequency(
        self, df_events_pivoted: pd.DataFrame, sites_to_evaluate: list[str]
    ) -> None:
        for num_times_colname in [
            "num_times_patient_draped_in_case",
            "num_times_patient_undraped_in_case",
            "num_times_patient_xfer_to_bed_in_case",
            "num_times_patient_xfer_to_or_table_in_case",
        ]:
            df_events_pivoted[num_times_colname] = df_events_pivoted[num_times_colname].fillna(0)

        df_events_pivoted["case_has_only_draped_undraped"] = (
            ~df_events_pivoted["first_patient_draped_datetime_local"].isna()
            & ~df_events_pivoted["first_patient_undraped_datetime_local"].isna()
            & df_events_pivoted["first_patient_xfer_to_or_table_datetime_local"].isna()
            & df_events_pivoted["first_patient_xfer_to_bed_datetime_local"].isna()
        )

        df_events_pivoted["case_has_xfer_to_bed_then_drapings_no_table"] = (
            (
                df_events_pivoted["first_patient_xfer_to_bed_datetime_local"]
                < df_events_pivoted["first_patient_draped_datetime_local"]
            )
            & (
                df_events_pivoted["first_patient_xfer_to_bed_datetime_local"]
                < df_events_pivoted["first_patient_undraped_datetime_local"]
            )
            & ~df_events_pivoted["first_patient_draped_datetime_local"].isna()
            & ~df_events_pivoted["first_patient_undraped_datetime_local"].isna()
            & df_events_pivoted["first_patient_xfer_to_or_table_datetime_local"].isna()
        )

        df_events_pivoted["case_has_only_xfer_bed_table"] = (
            (df_events_pivoted["num_times_patient_xfer_to_or_table_in_case"] >= 1)
            & (df_events_pivoted["num_times_patient_xfer_to_bed_in_case"] >= 1)
            & (df_events_pivoted["num_times_patient_draped_in_case"] == 0)
            & (df_events_pivoted["num_times_patient_undraped_in_case"] == 0)
        )

        df_events_pivoted["case_has_no_events"] = (
            (df_events_pivoted["num_times_patient_xfer_to_or_table_in_case"] == 0)
            & (df_events_pivoted["num_times_patient_draped_in_case"] == 0)
            & (df_events_pivoted["num_times_patient_undraped_in_case"] == 0)
            & (df_events_pivoted["num_times_patient_xfer_to_bed_in_case"] == 0)
        )

        df_edge_case_pcts = (
            df_events_pivoted[df_events_pivoted["site_id"].isin(sites_to_evaluate)]
            .groupby("site_id")[
                [
                    "case_has_only_draped_undraped",
                    "case_has_xfer_to_bed_then_drapings_no_table",
                    "case_has_only_xfer_bed_table",
                    "case_has_no_events",
                ]
            ]
            .mean()
            .reset_index()
        )

        df_edge_case_pcts_to_plot = pd.melt(
            df_edge_case_pcts,
            id_vars=["site_id"],
            value_vars=[
                "case_has_only_draped_undraped",
                "case_has_xfer_to_bed_then_drapings_no_table",
                "case_has_only_xfer_bed_table",
                "case_has_no_events",
            ],
            var_name="edge_case_type",
            value_name="pct_cases",
        )

        df_edge_case_pcts_to_plot["pct_cases"] = 100 * df_edge_case_pcts_to_plot["pct_cases"]

        sns.barplot(
            data=df_edge_case_pcts_to_plot, x="site_id", y="pct_cases", hue="edge_case_type"
        )
        plt.xticks(rotation=90)
        sns.set_theme(rc={"figure.figsize": (20, 10)})
        self.reporter.add_figure_to_report(
            figure_title="Percent of cases with edge-case event progressions",
            figure=plt,
        )
        plt.close()

    def calculate_sites_to_evaluate(self, df_events_pivoted: pd.DataFrame) -> list[str]:
        df_earliest_events_by_site = (
            df_events_pivoted.groupby("site_id")[["date_of_surgery"]].min().reset_index()
        )

        df_earliest_events_by_site = df_earliest_events_by_site[
            pd.to_datetime(df_earliest_events_by_site["date_of_surgery"])
            <= (
                pd.to_datetime(self.config.max_date)
                - pd.Timedelta(days=30 * MINIMUM_LIVE_DATA_MONTHS)
            )
        ]

        sites_to_evaluate = list(df_earliest_events_by_site["site_id"].unique())

        print("sites_to_evaluate", sites_to_evaluate)

        return sites_to_evaluate

    def _get_pivoted_timestamps_for_event_type(
        self, df: pd.DataFrame, event_type_name: str
    ) -> pd.DataFrame:
        """
        inputs:
            df: A dataframe that contains one row per event. If there are duplicates of an event,
                df will have one row per duplicate. If an event has more than two duplicates, only the
                first and second events (ordered by time) are kept. This is because once we see the
                second duplicate, we will stop doing inference on that case, so we do not need to know
                about subsequent duplicates.
            event_type_name: The type of event (patient_draped, patient_xfer_to_bed, etc) to pivot
        output:
            df_pivoted_timestamps: A dataframe that contains one row per case and event_type.
                If there are duplicates of an event, it will have values in two columns in the wide format:
                e.g. first_patient_draped_datetime_local and second_patient_draped_datetime_local
                If there are no duplicates, the value of second_patient_draped_datetime_local will
                equal to first_patient_draped_datetime_local, so rely on counts to identify duplicate events.
                We need the second event timestamp so that at inference, we know when the count of
                events increases from 1 to 2.
        """

        # note that even if there's only one event in the case, there will still be a
        # non-null value for the 'second event', so rely on the counts to identify duplicate events
        df_event = df[df["event_type_id"] == event_type_name]

        df_pivoted_timestamps = (
            df_event.groupby(["case_id", "site_id", "room_id", "org_id"], as_index=False)[
                "event_datetime_local"
            ]
            .agg(["min", "max", "count"])
            .reset_index()
        )
        df_pivoted_timestamps = df_pivoted_timestamps.rename(
            columns={
                "min": "first_" + event_type_name + "_datetime_local",
                "max": "second_" + event_type_name + "_datetime_local",
                "count": "num_times_" + event_type_name + "_in_case",
            }
        )

        if "index" in df_pivoted_timestamps.columns:
            del df_pivoted_timestamps["index"]

        return df_pivoted_timestamps

    def get_first_two_events_per_case_and_event_type(
        self, df_events_long: pd.DataFrame
    ) -> pd.DataFrame:
        # Get the first two events by timestamp, per event type and case_id
        # We only care about the first two because once the second duplicate is detected,
        # we stop predicting from this model
        sorted_events_long = df_events_long.sort_values(
            by=["org_id", "site_id", "case_id", "event_type_id", "event_datetime_local"]
        )
        sorted_events_long["event_timestamp_order"] = (
            sorted_events_long.groupby(["org_id", "site_id", "case_id", "event_type_id"]).cumcount()
            + 1
        )

        first_two_events_per_event_type = sorted_events_long[
            sorted_events_long["event_timestamp_order"].isin([1, 2])
        ]

        return first_two_events_per_event_type

    def calculate_individual_phase_durations(self, df_events_pivoted: pd.DataFrame) -> pd.DataFrame:
        # make a copy to avoid side effects
        df_cases_with_phase_durations = df_events_pivoted.copy()

        df_cases_with_phase_durations["wheels_in_to_first_xfer_to_or_table"] = (
            self._calculate_phase_duration(
                df_cases_with_phase_durations,
                "actual_start_datetime_local",
                "first_patient_xfer_to_or_table_datetime_local",
            )
        )

        df_cases_with_phase_durations["first_xfer_to_or_table_to_first_draped"] = (
            self._calculate_phase_duration(
                df_cases_with_phase_durations,
                "first_patient_xfer_to_or_table_datetime_local",
                "first_patient_draped_datetime_local",
            )
        )

        df_cases_with_phase_durations["first_draped_to_first_undraped"] = (
            self._calculate_phase_duration(
                df_cases_with_phase_durations,
                "first_patient_draped_datetime_local",
                "first_patient_undraped_datetime_local",
            )
        )

        df_cases_with_phase_durations["first_undraped_to_first_xfer_to_bed"] = (
            self._calculate_phase_duration(
                df_cases_with_phase_durations,
                "first_patient_undraped_datetime_local",
                "first_patient_xfer_to_bed_datetime_local",
            )
        )

        df_cases_with_phase_durations["first_xfer_to_bed_to_wheels_out"] = (
            self._calculate_phase_duration(
                df_cases_with_phase_durations,
                "first_patient_xfer_to_bed_datetime_local",
                "actual_end_datetime_local",
            )
        )

        return df_cases_with_phase_durations

    @staticmethod
    def _calculate_phase_duration(
        df: pd.DataFrame, start_event_name: str, end_event_name: str
    ) -> np.ndarray[float, Any]:  # pd.Series[float]:
        return np.array((df[end_event_name] - df[start_event_name]).dt.total_seconds() / 60)

    def pivot_events(
        self, df_events_long: pd.DataFrame, df_all_cases: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Input:
            df_events_long: a dataframe with one row per detected event. If a case has two events of the same type,
                there would be two rows in this dataframe.
            df_all_cases: a dataframe that contains ALL cases that happened within the selected date range,
                regardless of whether there were any events detected during these cases.
                We need this information to figure out which types of procedures tend to have missing events.
        Output:
            df_events_pivoted: a dataframe with one row per case. If a case has duplicate events, those are
                recorded in separate columns, e.g. "first_patient_draped_datetime_local" and "second_patient_draped_datetime_local".
                Third duplicates and beyond are not included.
        """
        # filter to only the first two events per case and event_type
        first_two_events_per_event_type = self.get_first_two_events_per_case_and_event_type(
            df_events_long
        )

        # pivot to the first and second event timestamp per case and event type
        patient_xfer_to_table_events = self._get_pivoted_timestamps_for_event_type(
            df=first_two_events_per_event_type, event_type_name="patient_xfer_to_or_table"
        )

        patient_draped_events = self._get_pivoted_timestamps_for_event_type(
            df=first_two_events_per_event_type, event_type_name="patient_draped"
        )

        patient_undraped_events = self._get_pivoted_timestamps_for_event_type(
            df=first_two_events_per_event_type, event_type_name="patient_undraped"
        )

        patient_xfer_to_bed_events = self._get_pivoted_timestamps_for_event_type(
            df=first_two_events_per_event_type, event_type_name="patient_xfer_to_bed"
        )

        # join all pivoted events to the list of "all cases that existed within the chosen date range"
        df_events_pivoted = (
            df_all_cases.merge(
                patient_draped_events, how="left", on=["case_id", "site_id", "org_id", "room_id"]
            )
            .merge(
                patient_xfer_to_bed_events,
                how="left",
                on=["case_id", "site_id", "org_id", "room_id"],
            )
            .merge(
                patient_undraped_events, how="left", on=["case_id", "site_id", "org_id", "room_id"]
            )
            .merge(
                patient_xfer_to_table_events,
                how="left",
                on=["case_id", "site_id", "org_id", "room_id"],
            )
        )

        df_events_pivoted = self.calculate_individual_phase_durations(df_events_pivoted)

        return df_events_pivoted

    def query_for_data(self, query_string: str) -> pd.DataFrame:
        data_df = self.bq_client.query(query_string).to_dataframe()
        return data_df

    def get_query_string_for_all_case_features(self) -> str:
        """
        Gets data for features related to cases, e.g. first_primary_surgeon and first_primary_procedure.
        Returns one row per case, and contains ALL cases recorded within the chosen date range (not just
        cases that have events).
        """
        return f"""
        select 
            case_id,
            site_id,
            org_id,
            room_id,
            date_of_surgery,
            scheduled_end_datetime_local,
            first_primary_surgeon,
            first_primary_procedure,
            procedure_count,
            actual_start_datetime_local,
            actual_end_datetime_local,
            scheduled_duration,
            actual_duration,
            apella_data,
            -- note, this isn't relative to "today" b/c of the potential filter for max_day
            date_diff("{ self.config.max_date }", date_of_surgery, day) as days_before_max_date
        from `prod-data-platform-027529.gold.forecasting_case_features_combined_latest` as features
        where features.date_of_surgery >= date("{ self.config.min_date }")
            and features.date_of_surgery <= date("{self.config.max_date }")
            and apella_data = 1
        """

    def get_query_string_for_raw_events(self) -> str:
        """
        Gets data for individual events that have been predicted or annotated, with one row per event.
        Case information is joined to each event, but note that a case that doesn't have events will not
        be included.
        """
        return f"""
            with events as (
                select
                    events.site_id,
                    events.org_id,
                    events.room_id,
                    event_type_id,
                    datetime(events.start_time, sites.timezone) as start_time_local,
                    source_type
                from `prod-data-platform-027529.bronze.public_events` as events
                inner join `prod-data-platform-027529.bronze.public_sites` as sites
                    on events.site_id = sites.id
                where
                    events.event_type_id in (
                        'patient_draped', 
                        'patient_undraped', 
                        'patient_xfer_to_bed', 
                        'patient_xfer_to_or_table'
                    )
                    and source_type in ('human_gt', 'prediction')
                    and events.deleted_at is null
                    and extract(date from datetime(events.start_time, sites.timezone)) >= date("{self.config.min_date}")
                    and extract(date from datetime(events.start_time, sites.timezone)) <= date("{self.config.max_date}")
            ),

            cases as (
                select *,
                    date_diff("{self.config.max_date}", date_of_surgery, day) as days_before_max_date
                from `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
                where date_of_surgery >= date("{self.config.min_date}")
                    and date_of_surgery <= date("{self.config.max_date}")
            )

            select
                cases.case_id,
                cases.site_id,
                cases.org_id,
                cases.room_id,
                cases.actual_start_datetime_local,
                events.event_type_id,
                events.start_time_local as event_datetime_local,
                events.source_type,
                cases.actual_end_datetime_local,
                cases.first_primary_surgeon,
                cases.first_primary_procedure,
                cases.actual_duration,
                cases.scheduled_duration,
                cases.date_of_surgery
            from cases
            inner join events
                on
                    cases.actual_start_datetime_local <= events.start_time_local
                    and cases.actual_end_datetime_local >= events.start_time_local
                    and cases.site_id = events.site_id
                    and cases.org_id = events.org_id
                    and cases.room_id = events.room_id
        """

    def get_query_string_for_static_case_predictions(self) -> str:
        """
        Gets the latest prediction of case duration from the Bayesian model, to be used as a feature for dynamic model.
        This assumes that before the case starts, all forecasts are from the static model and that there
        are no changes to the features after the latest run before case start.
        Note that about 12% of cases will change static case duration during the case, and this query will not
        reflect those changes.
        """

        return f"""
        select
            bayesian.case_id,
            round(bayesian.forecast_median) as static_forecasted_duration
        from `prod-data-platform-027529.case_forecasting.bayesian_case_duration_results` as bayesian
        join `prod-data-platform-027529.gold.forecasting_case_features_combined_latest` as cases
            on bayesian.case_id = cases.case_id
        where cases.date_of_surgery >= date("{self.config.min_date}")
            and cases.date_of_surgery <= date("{self.config.max_date}")
            and cases.apella_data = 1
            and bayesian.ds = (select max(ds) from `prod-data-platform-027529.case_forecasting.bayesian_case_duration_results`)
        """

    def get_query_string_for_transformer_predicitons(self) -> str:
        return f"""
        with cases as (
            select 
                site_id,
                room_id,
                case_id, 
                actual_start_datetime_local, 
                actual_end_datetime_local, 
                date_of_surgery
            from `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
            where date_of_surgery >= date("{self.config.min_date}")
                and date_of_surgery <= date("{self.config.max_date}")
        ),
        
        -- includes all forecast variants that come from transformer
        raw_transformer_forecasts as (
            select
                forecasts.forecast_variant,
                forecasts.case_id,
                cases.site_id,
                datetime(forecasts.forecast_run_time, sites.timezone) as forecast_run_time_local,
                datetime_diff(
                    cases.actual_end_datetime_local, 
                    datetime(forecasts.forecast_run_time, sites.timezone), 
                    minute
                ) as obs_time_minutes_before_wheels_out,
                datetime_diff(
                    datetime(forecasts.forecast_end_time, sites.timezone),
                    datetime(forecasts.forecast_start_time, sites.timezone), 
                    minute
                ) as transformer_forecasted_duration,
                
            from `prod-data-platform-027529.case_forecasting.case_forecast_info` as forecasts
            join cases
                on cases.case_id = forecasts.case_id
            join `prod-data-platform-027529.bronze.public_sites` as sites
                on sites.id = cases.site_id
            where 
                -- only want forecasts during the case
                datetime(forecasts.forecast_run_time, sites.timezone) >= cases.actual_start_datetime_local
                and datetime(forecasts.forecast_run_time, sites.timezone) <= cases.actual_end_datetime_local
                -- only want forecasted end times that are after the forecast run time
                and datetime(forecasts.forecast_end_time, sites.timezone) >= datetime(forecasts.forecast_run_time, sites.timezone)
                -- if the forecast is matched correctly to the case and occurs after the start time, then the 
                -- forecasted start time should be equal to the actual start time. within 3 minutes is ok
                and abs(
                    timestamp_diff(
                        datetime(forecasts.forecast_start_time, sites.timezone), 
                        cases.actual_start_datetime_local, 
                        minute
                    )
                ) <= 3
                -- only want transformer forecasts
                and forecasts.transformer_end_time = forecasts.forecast_end_time
                and forecasts.transformer_end_time is not null
        )
        
        -- this takes only one row for each case_id and forecast_run_time (ordered by forecast variant name and forecasted duration)
        -- each forecast run time can have multiple forecasts, transformer predictions at a given recorded "run time"
        -- may be different depending on the forecast variant due to actually having slightly different run times
        -- we don't want to filter to a specific forecast variant because the active forecast variants will change over time.
        -- ordering alphabetically by forecast_variant will at least serve as a tiebreaker
        select 
            case_id, 
            site_id,
            obs_time_minutes_before_wheels_out,
            transformer_forecasted_duration
        from (
            select *,
                row_number() over (partition by case_id, obs_time_minutes_before_wheels_out order by forecast_variant, transformer_forecasted_duration) as row_num
            from raw_transformer_forecasts
        )
        where row_num = 1
        """
