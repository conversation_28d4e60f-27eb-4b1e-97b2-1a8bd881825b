import pandas as pd
from training_utils.clearml_reporter import <PERSON><PERSON><PERSON><PERSON>orter

from dynamic_case_end.configs.prod_config import (
    DynamicModelConfig,
    EvaluationConfig,
    TrainingConfig,
)
from dynamic_case_end.model.dynamic_case_end_model import DynamicCaseEndModel


class ModelTrainer:
    def __init__(
        self,
        training_config: TrainingConfig,
        model_config: DynamicModelConfig,
        evaluation_config: EvaluationConfig,
        dataset: pd.DataFrame,
        reporter: ClearMLReporter,
    ):
        self.training_config = training_config
        self.model_config = model_config
        self.evaluation_config = evaluation_config
        self.dataset = dataset
        self.reporter = reporter
        self.generate_train_test_datasets()

    def train(self) -> dict[str, DynamicCaseEndModel]:
        trained_models_dict = {}

        for location in self.training_config.locations_to_fit:
            # we need the "raw", uncleaned training data to calculate the procedures that have duplicates
            # inside the trainer, we will filter to only clean cases with no duplicates before fitting
            df_train_raw_location = self.df_train_raw[
                self.df_train_raw[self.training_config.location_type_to_fit] == location
            ]
            print(
                f"fitting for {location} with {str(len(df_train_raw_location))} pre-cleaning points"
            )

            dynamic_model = DynamicCaseEndModel(location, self.model_config)
            dynamic_model.train(df_train_raw_location)

            trained_models_dict[location] = dynamic_model
        return trained_models_dict

    def generate_train_test_datasets(self) -> None:
        df_data = self.dataset
        # min_num_total_days is the sum of the training and test sets
        min_num_total_days = (
            self.training_config.baseline_training_days
            + self.training_config.test_on_most_recent_x_days
        )

        df_train_raw = pd.DataFrame()
        df_test_raw = pd.DataFrame()

        # for each site/location to evaluate, check that there is enough data in both the training and test sets
        for location in self.evaluation_config.evaluation_to_fit_mapping.keys():
            df_location = df_data[
                df_data[self.evaluation_config.location_type_to_evaluate] == location
            ]
            num_days_at_location = df_location["days_before_max_date"].max()
            if (not pd.isnull(num_days_at_location)) and (
                num_days_at_location < min_num_total_days
            ):
                test_on_most_recent_x_days_at_location = round(num_days_at_location * 0.25)
                print(
                    f"Warning: {location} does not have enough data for full test set at {num_days_at_location} days; testing on "
                    + f" {str(test_on_most_recent_x_days_at_location)} days and training on "
                    + f" {str(num_days_at_location - test_on_most_recent_x_days_at_location)} days"
                )
            elif pd.isnull(num_days_at_location):
                print(f"Warning: {location} has no data in test set")
            else:
                test_on_most_recent_x_days_at_location = (
                    self.training_config.test_on_most_recent_x_days
                )

            df_train_location = df_location[
                df_location["days_before_max_date"] >= test_on_most_recent_x_days_at_location
            ]
            df_test_location = df_location[
                df_location["days_before_max_date"] < test_on_most_recent_x_days_at_location
            ]

            print(f"training and test sets at {location}:")
            print(
                f"df_train_location: {len(df_train_location)} cases from "
                f"{df_train_location['date_of_surgery'].min()} "
                + f"to {df_train_location['date_of_surgery'].max()}"
            )
            print(
                f"df_test_location: {len(df_test_location)} cases from {df_test_location['date_of_surgery'].min()} "
                + f"to {df_test_location['date_of_surgery'].max()}"
            )

            df_train_raw = pd.concat([df_train_raw, df_train_location])
            df_test_raw = pd.concat([df_test_raw, df_test_location])

        self.df_train_raw = df_train_raw
        self.df_test_raw = df_test_raw

        return
