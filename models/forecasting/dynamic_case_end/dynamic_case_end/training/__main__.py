import argparse
import json
import logging
from datetime import datetime
from importlib import import_module

import dill  # type: ignore
import joblib
from google.cloud.bigquery import Client as BQClient
from google.cloud.secretmanager import SecretManagerServiceClient
from google.cloud.storage import Client as GCSClient
from training_utils.clearml_reporter import (
    ClearMLReporter,
    get_clearml_task,
)
from training_utils.model_storage import ModelStorage
from training_utils.utils import ensure_gcp_auth_is_ready, get_repo_info

from dynamic_case_end.model.dynamic_case_end_model import MODEL_TYPE
from dynamic_case_end.model.evaluator import Evaluator
from dynamic_case_end.training.data_selector import DataSelector
from dynamic_case_end.training.trainer import ModelTrainer

logger = logging.getLogger(__name__)
dill.settings["recurse"] = True

DEFAULT_CONFIG_MODULE = "dynamic_case_end.configs.prod_config"


def run_pipeline(args: argparse.Namespace) -> None:
    # Load utilities
    ensure_gcp_auth_is_ready()
    bq_client = BQClient()
    gcs_client = GCSClient()

    # Load the TrainingConfig
    model_training_config = import_module(args.config_module_name).config

    if args.is_automated_training:
        # set the variables for an automated fit
        fit_time = datetime.now()
        repo = get_repo_info()
        model_identifier = (
            f"automatic_production_training/{fit_time.date()}"
            if not args.evaluate
            else f"automatic_evaluation_training/{fit_time.date()}"
        )
        model_training_config.data_selection_config.max_date = str(fit_time.date())
        model_training_config.clearml_config.project_name = (
            f"{model_training_config.clearml_config.project_name}: Automated Fit"
        )
        model_training_config.clearml_config.task_name = f"{fit_time.date()} Production Fit"
        model_training_config.clearml_config.tags = {
            "date": str(fit_time),
            "branch_name": repo.active_branch.name,
            "sha": repo.head.object.hexsha,
            "short_sha": repo.git.rev_parse(repo.head.object.hexsha, short=7),
            "model_type": MODEL_TYPE,
            "model_identifier": model_identifier,
        }
        model_training_config.model_identifier = model_identifier

    if args.train and not args.evaluate:
        model_training_config.training_config.test_on_most_recent_x_days = 0

    # Load the ClearML credentials
    secret_manager_client = SecretManagerServiceClient()
    clearml_task = get_clearml_task(secret_manager_client, model_training_config.clearml_config)
    reporter = ClearMLReporter(model_training_config.model_dump(), clearml_task)

    # Load the data
    data_selector = DataSelector(bq_client, model_training_config.data_selection_config, reporter)
    data, df_transformer_predictions, sites_to_evaluate = data_selector.generate_data_for_fit()

    # Train our model
    trainer = ModelTrainer(
        model_training_config.training_config,
        model_training_config.dynamic_model_config,
        model_training_config.evaluation_config,
        data,
        reporter,
    )

    model_storage = ModelStorage(
        gcs_client,
        model_type=MODEL_TYPE,
        model_identifier=model_training_config.model_identifier,
    )

    if args.train:
        fit_models = trainer.train()
        for location in model_training_config.training_config.locations_to_fit:
            filename = f"{MODEL_TYPE}_{location}.joblib"
            local_path = model_storage._get_local_data_file_location(filename)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            with open(local_path, "wb") as io:
                joblib.dump(fit_models[location], io)
        # write our config out as well
        config_local_file_path = model_storage._get_local_data_file_location("config_file.json")
        json.dump(model_training_config.model_dump(), open(config_local_file_path, "w"))
        model_storage.upload_to_google_storage()

    if args.evaluate:
        print("Evaluating model")

        # Load the saved model
        model_storage._download_from_google_storage()
        fit_models = model_storage.load_models()

        # Evaluate results
        evaluator = Evaluator(
            fit_models,
            trainer.df_test_raw,
            df_transformer_predictions,
            model_training_config.evaluation_config,
            sites_to_evaluate,
            reporter,
            bq_client,
        )
        df_predictions = evaluator.evaluate_models()
        df_predictions["model_identifier"] = model_training_config.model_identifier
        evaluator.upload_evaluation_data(bq_client, df_predictions)


def main() -> None:
    # Initialize logging
    parser = argparse.ArgumentParser()

    parser.add_argument("--train", help="Run training", action="store_true")
    parser.add_argument("--evaluate", help="Run training", action="store_true")
    parser.add_argument(
        "--config-module-name",
        type=str,
        help="absolute module name, starting from PACKAGE_ROOT and using '.' as delimiter, not '/', i.e., dynamic_case_end.configs.experiment_config",
        default=DEFAULT_CONFIG_MODULE,
    )
    parser.add_argument("--is-automated-training", default=False, action="store_true")

    args = parser.parse_args()
    if args.train:
        logger.info("We will run the training")
    else:
        logger.info("Not doing anything")

    run_pipeline(args)


if __name__ == "__main__":
    main()
