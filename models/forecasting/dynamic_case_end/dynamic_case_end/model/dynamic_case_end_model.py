from datetime import datetime
from typing import Any, <PERSON><PERSON>, <PERSON><PERSON>, Union

import numpy as np
import pandas as pd
import pandera as pa
from catboost import CatBoostRegressor  # type: ignore
from feature_store.feature_store import FeatureStoreDateTime
from pandera.engines.pandas_engine import PydanticModel
from pydantic import BaseModel, validator
from sklearn.compose import ColumnTransformer
from sklearn.impute import SimpleImputer
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import FunctionTransformer

from dynamic_case_end.configs.prod_config import DynamicModelConfig

MODEL_TYPE = "dynamic_case_end_model"


class DynamicEndPredictorInputData(BaseModel, extra="ignore"):
    """
    BaseModel used for defining input for FastAPI
    """

    case_id: str
    site_id: str
    first_primary_surgeon: str
    first_primary_procedure: str
    procedure_count: int
    static_forecasted_duration: Optional[float] = None
    scheduled_duration: int
    actual_start_datetime_local: Optional[datetime] = None
    first_patient_xfer_to_or_table_datetime_local: Optional[datetime] = None
    second_patient_xfer_to_or_table_datetime_local: Optional[datetime] = None
    num_times_patient_xfer_to_or_table_in_case: Optional[int] = 0
    first_patient_draped_datetime_local: Optional[datetime] = None
    second_patient_draped_datetime_local: Optional[datetime] = None
    num_times_patient_draped_in_case: Optional[int] = 0
    first_patient_undraped_datetime_local: Optional[datetime] = None
    second_patient_undraped_datetime_local: Optional[datetime] = None
    num_times_patient_undraped_in_case: Optional[int] = 0
    first_patient_xfer_to_bed_datetime_local: Optional[datetime] = None
    second_patient_xfer_to_bed_datetime_local: Optional[datetime] = None
    num_times_patient_xfer_to_bed_in_case: Optional[int] = 0

    # TODO: for the two below:
    #  we assume that wheels out hasn't happened, and inference code below checks whether event2
    #  (defining the end of the phase) has occurred and how many times it has occurred
    #  in order to calculate running durations. putting this field in here
    #  so the same method can be used to calculate running durations of all phases in the case,
    #  instead of having to calculate the xfer-to-bed-to-wheels-out differently than the other phases
    #  is there a way to do this better?
    actual_end_datetime_local: Optional[datetime] = None
    wheels_out_occurred: Optional[int] = 0
    num_times_wheels_out_in_case: Optional[int] = 0
    obs_time: datetime

    @validator("static_forecasted_duration", pre=True, always=True)
    def handle_nan(cls, value: Union[float, None]) -> Union[float, None]:
        return None if pd.isna(value) else value

    def to_df(self) -> pd.DataFrame:
        return pd.DataFrame([self.model_dump()])


class DynamicEndPredictorOutputData(BaseModel, extra="ignore"):
    """
    BaseModel used for defining output from dynamic end model
    """

    predicted_total_case_duration: Optional[float]
    predicted_wheels_in_to_xfer_to_or_table: Optional[float]
    predicted_xfer_to_or_table_to_draped: Optional[float]
    predicted_draped_to_undraped: Optional[float]
    predicted_undraped_to_xfer_to_bed: Optional[float]
    predicted_xfer_to_bed_to_wheels_out: Optional[float]
    prediction_tag: str


class ModelInputPydanticFeatureSchema(pa.DataFrameModel):
    """Pandera schema using the pydantic model, used to validate dataframes"""

    case_id: pa.typing.Index[str]

    org_id: str | None
    site_id: str | None
    first_primary_surgeon: str | None
    first_primary_procedure: str | None
    procedure_count: float | None
    scheduled_duration: int | None
    actual_start_datetime_local: FeatureStoreDateTime | None
    first_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_patient_xfer_to_or_table_in_case: int | None = pa.Field(nullable=True)
    first_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_draped_in_case: int | None = pa.Field(nullable=True)
    first_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_undraped_in_case: int | None = pa.Field(nullable=True)
    first_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    second_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    num_times_patient_xfer_to_bed_in_case: int | None = pa.Field(nullable=True)

    class Config:
        """Config with dataframe-level data type."""

        coerce = True  # this is required, otherwise a SchemaInitError is raised


class ModelInputPydanticSchema(ModelInputPydanticFeatureSchema):
    obs_time: FeatureStoreDateTime | None = pa.Field(nullable=True)
    actual_end_datetime_local: FeatureStoreDateTime | None = pa.Field(nullable=True)
    wheels_out_occurred: int | None = pa.Field(nullable=True)
    num_times_wheels_out_in_case: int | None = pa.Field(nullable=True)

    class Config:
        """Config with dataframe-level data type."""

        dtype = PydanticModel(DynamicEndPredictorInputData)
        coerce = True  # this is required, otherwise a SchemaInitError is raised


class ModelOutputPydanticSchema(pa.DataFrameModel):
    """Pandera schema using the pydantic model, used to validate dataframes"""

    class Config:
        """Config with dataframe-level data type."""

        dtype = PydanticModel(DynamicEndPredictorOutputData)
        coerce = True  # this is required, otherwise a SchemaInitError is raised


class DynamicCaseEndModel:
    def __init__(self, location: str, config: DynamicModelConfig):
        self.location = location  # usually this will be org_id
        self.config = config
        self.aggregate_phase_data = pd.DataFrame()
        self.percent_phase_in_total_duration = pd.DataFrame()
        self.procedures_with_high_duplicate_events = pd.DataFrame()
        self.procedures_with_only_xfer_table_bed = pd.DataFrame()
        self.trained_models: dict[str, Pipeline] = {}

    def make_single_prediction(
        self,
        data_for_prediction: DynamicEndPredictorInputData,  # BaseModel
        inference_mode: bool,
    ) -> DynamicEndPredictorOutputData:
        """
        Input:
            data_for_prediction: BaseModel with raw data needed for dynamic end model

        Output:
            a dictionary containing the predicted minutes-until-case-end and a "prediction tag", which
            specifies whether the prediction came from the dynamic end model, or a prediction could not be made
            due to one of the "failure modes"

            Failure modes:
            Currently, this will return a null under the following situations:
            1. the procedure had too many duplicate events in the training data
              (does NOT necessarily mean that this predicted case has duplicates)
            2. we expect that there might be a missing event
              (due to running phase duration exceeding some factor of historical medians)
            3. we found a duplicate event in the specific predicted case
              (does NOT necessarily mean that the procedure had lots of duplicates in training data)
            4. events are in an unexpected order
            we can apply the static case duration model in these situations
        """
        aggregate_phase_data_for_site = self.aggregate_phase_data
        phase_pcts_for_site = self.percent_phase_in_total_duration
        procedures_with_duplicate_events = self.procedures_with_high_duplicate_events
        procedures_with_only_xfer_table_bed = self.procedures_with_only_xfer_table_bed
        trained_models = self.trained_models

        # if in inference mode, don't predict until after first patient xfer to or table
        # if inference_mode and data_for_prediction.num_times_patient_draped_in_case == 0:
        if inference_mode and data_for_prediction.num_times_patient_xfer_to_or_table_in_case == 0:
            return DynamicEndPredictorOutputData(
                predicted_total_case_duration=np.NaN,
                prediction_tag="no_pythia_predictions_before_patient_xfer_to_or_table",
                predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                predicted_xfer_to_or_table_to_draped=np.NaN,
                predicted_draped_to_undraped=np.NaN,
                predicted_undraped_to_xfer_to_bed=np.NaN,
                predicted_xfer_to_bed_to_wheels_out=np.NaN,
            )

        # check if there are duplicates. if so, return null
        if (
            (data_for_prediction.num_times_patient_draped_in_case > 1)  # type: ignore
            or (data_for_prediction.num_times_patient_xfer_to_bed_in_case > 1)  # type: ignore
            or (data_for_prediction.num_times_patient_undraped_in_case > 1)  # type: ignore
            or (data_for_prediction.num_times_patient_xfer_to_or_table_in_case > 1)  # type: ignore
        ):
            return DynamicEndPredictorOutputData(
                predicted_total_case_duration=np.NaN,
                prediction_tag="detected_duplicate_event_in_this_case",
                predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                predicted_xfer_to_or_table_to_draped=np.NaN,
                predicted_draped_to_undraped=np.NaN,
                predicted_undraped_to_xfer_to_bed=np.NaN,
                predicted_xfer_to_bed_to_wheels_out=np.NaN,
            )

        # check if this procedure had lots of duplicates in training data. if it does, return null
        # even if the procedure has duplicates in the past, does not mean that this particular case has duplicates
        if (
            data_for_prediction.first_primary_procedure
            in procedures_with_duplicate_events["first_primary_procedure"].unique()
        ):
            return DynamicEndPredictorOutputData(
                predicted_total_case_duration=np.NaN,
                prediction_tag="procedure_historically_had_duplicate_events",
                predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                predicted_xfer_to_or_table_to_draped=np.NaN,
                predicted_draped_to_undraped=np.NaN,
                predicted_undraped_to_xfer_to_bed=np.NaN,
                predicted_xfer_to_bed_to_wheels_out=np.NaN,
            )

        # check if this procedure had lots of cases with only xfer_to_or_table and xfer_to_bed
        # if it does, return null
        # these procedures need to be tied to the site as well
        df_data_for_prediction = pd.DataFrame(
            {
                "site_id": data_for_prediction.site_id,
                "first_primary_procedure": data_for_prediction.first_primary_procedure,
            },
            index=[0],
        )
        if (
            len(
                df_data_for_prediction.merge(
                    procedures_with_only_xfer_table_bed,
                    on=["site_id", "first_primary_procedure"],
                    how="inner",
                )
            )
            > 0
        ):
            return DynamicEndPredictorOutputData(
                predicted_total_case_duration=np.NaN,
                prediction_tag="procedure_historically_only_xfer_table_bed",
                predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                predicted_xfer_to_or_table_to_draped=np.NaN,
                predicted_draped_to_undraped=np.NaN,
                predicted_undraped_to_xfer_to_bed=np.NaN,
                predicted_xfer_to_bed_to_wheels_out=np.NaN,
            )

        # note that we could still end up returning a null predictions if the events aren't in the expected order
        # or if we determine that there might be a missing event, or we see multiple occurrences of the same event type
        # these require calculating running duration and is_occurred columns, so we first calculate some features
        # and then apply the checks

        # calculate is_occurred columns and actual phase durations
        df_features = self.calculate_features_for_inference(data_for_prediction)
        assert len(df_features) == 1  # there should only be features for a single case

        # apply the "trained" aggregate data and calculate
        df_with_aggs = df_features.merge(
            aggregate_phase_data_for_site,
            on=["first_primary_surgeon", "first_primary_procedure", "procedure_count"],
            how="left",
        )
        df_with_aggs = df_with_aggs.merge(
            phase_pcts_for_site, on=["static_duration_group"], how="left"
        )

        # generate predictions for individual phases
        df_predictions = self.apply_predicted_phase_duration_to_all_phases(
            df_with_aggs, trained_models
        )

        # check the predicted durations for suspected missing events and events out of order
        # we check for missing events only after calculating predicted individual phase durations
        # we don't know ahead of time if there's a missing event, so we rely on the running duration being too long
        # predicted phase = the final prediction for the phase;
        #   could be equal to ml estimate or the actual duration, if the phase has ended
        # ml estimate = the phase duration predicted by the ml model;
        #   will be the same regardless of the stage of the case
        for event1, event2, predicted_duration_colname, ml_phase_prediction_colname in [
            (
                "actual_start_datetime_local",
                "patient_xfer_to_or_table",
                "predicted_wheels_in_to_xfer_to_or_table",
                "ml_estimate_wheels_in_to_xfer_to_or_table",
            ),
            (
                "patient_xfer_to_or_table",
                "patient_draped",
                "predicted_xfer_to_or_table_to_draped",
                "ml_estimate_xfer_to_or_table_to_draped",
            ),
            (
                "patient_draped",
                "patient_undraped",
                "predicted_draped_to_undraped",
                "ml_estimate_draped_to_undraped",
            ),
            (
                "patient_undraped",
                "patient_xfer_to_bed",
                "predicted_undraped_to_xfer_to_bed",
                "ml_estimate_undraped_to_xfer_to_bed",
            ),
            (
                "patient_xfer_to_bed",
                "wheels_out",
                "predicted_xfer_to_bed_to_wheels_out",
                "ml_estimate_xfer_to_bed_to_wheels_out",
            ),
        ]:
            if event1 != "actual_start_datetime_local":
                event1_occurred_colname = "first_" + self._get_event_occurred_colname(event1)
            else:
                event1_occurred_colname = self._get_event_occurred_colname(event1)

            if event2 != "wheels_out":
                event2_occurred_colname = "first_" + self._get_event_occurred_colname(event2)
            else:
                event2_occurred_colname = self._get_event_occurred_colname(event2)

            # there should be only one row in df_with_aggs, corresponding to the single case

            suspected_missing_event = max(
                df_predictions[predicted_duration_colname]
                == (
                    df_predictions[ml_phase_prediction_colname]
                    * self.config.max_factor_allowed_for_running_duration
                )
            ) or max(
                (df_predictions[event2_occurred_colname] == 1)
                & (df_predictions[event1_occurred_colname] == 0)
            )

            if suspected_missing_event:
                return DynamicEndPredictorOutputData(
                    predicted_total_case_duration=np.NaN,
                    predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                    predicted_xfer_to_or_table_to_draped=np.NaN,
                    predicted_draped_to_undraped=np.NaN,
                    predicted_undraped_to_xfer_to_bed=np.NaN,
                    predicted_xfer_to_bed_to_wheels_out=np.NaN,
                    prediction_tag="suspected_missing_event",
                )

            events_out_of_order = max(df_predictions[predicted_duration_colname] < 0)

            if events_out_of_order:
                return DynamicEndPredictorOutputData(
                    predicted_total_case_duration=np.NaN,
                    predicted_wheels_in_to_xfer_to_or_table=np.NaN,
                    predicted_xfer_to_or_table_to_draped=np.NaN,
                    predicted_draped_to_undraped=np.NaN,
                    predicted_undraped_to_xfer_to_bed=np.NaN,
                    predicted_xfer_to_bed_to_wheels_out=np.NaN,
                    prediction_tag="events_out_of_order",
                )

        # calculate predicted end time and errors
        df_predictions["predicted_total_case_duration"] = (
            df_predictions["predicted_wheels_in_to_xfer_to_or_table"]
            + df_predictions["predicted_xfer_to_or_table_to_draped"]
            + df_predictions["predicted_draped_to_undraped"]
            + df_predictions["predicted_undraped_to_xfer_to_bed"]
            + df_predictions["predicted_xfer_to_bed_to_wheels_out"]
        )

        # these are for debugging during model iteration
        predicted_wheels_in_to_xfer_to_or_table = list(
            df_predictions["predicted_wheels_in_to_xfer_to_or_table"]
        )[0]
        predicted_xfer_to_or_table_to_draped = list(
            df_predictions["predicted_xfer_to_or_table_to_draped"]
        )[0]
        predicted_draped_to_undraped = list(df_predictions["predicted_draped_to_undraped"])[0]
        predicted_undraped_to_xfer_to_bed = list(
            df_predictions["predicted_undraped_to_xfer_to_bed"]
        )[0]
        predicted_xfer_to_bed_to_wheels_out = list(
            df_predictions["predicted_xfer_to_bed_to_wheels_out"]
        )[0]
        predicted_total_case_duration = list(df_predictions["predicted_total_case_duration"])[0]

        # if xfer to table hasn't been observed and the pythia predicted duration is more than
        # 90 minutes greater than static duration, use static duration. this can help avoid huge and bad jumps
        # when first undraped is seen but there are eventually duplicates in the case
        # we still want to have pythia use actual phase durations if actual duration is much lower than static prediction
        # so only turn this on before xfer to bed has been observed
        if (
            (data_for_prediction.num_times_patient_xfer_to_bed_in_case == 0)
            and (data_for_prediction.static_forecasted_duration is not None)
            and (
                (predicted_total_case_duration + 90)
                < data_for_prediction.static_forecasted_duration
            )
            and (predicted_total_case_duration < 60)
        ):
            return DynamicEndPredictorOutputData(
                predicted_total_case_duration=np.NaN,
                predicted_wheels_in_to_xfer_to_or_table=predicted_wheels_in_to_xfer_to_or_table,
                predicted_xfer_to_or_table_to_draped=predicted_xfer_to_or_table_to_draped,
                predicted_draped_to_undraped=predicted_draped_to_undraped,
                predicted_undraped_to_xfer_to_bed=predicted_undraped_to_xfer_to_bed,
                predicted_xfer_to_bed_to_wheels_out=predicted_xfer_to_bed_to_wheels_out,
                prediction_tag="dynamic_end_prediction_much_lower_than_static",
            )

        return DynamicEndPredictorOutputData(
            predicted_total_case_duration=predicted_total_case_duration,
            predicted_wheels_in_to_xfer_to_or_table=predicted_wheels_in_to_xfer_to_or_table,
            predicted_xfer_to_or_table_to_draped=predicted_xfer_to_or_table_to_draped,
            predicted_draped_to_undraped=predicted_draped_to_undraped,
            predicted_undraped_to_xfer_to_bed=predicted_undraped_to_xfer_to_bed,
            predicted_xfer_to_bed_to_wheels_out=predicted_xfer_to_bed_to_wheels_out,
            prediction_tag="dynamic_end_prediction",
        )

    def run_model_prediction_for_df(
        self, validated_df: pd.DataFrame, inference_mode: bool = True
    ) -> pd.DataFrame:
        """If inference_mode is True, then only make Pythia prediction after patient_draped is observed"""
        prediction_tag_list = []  # holds the "type of prediction": reason for null, if any, etc
        predicted_duration_list = []  # total predicted duration (wheels in to wheels out)
        predicted_wheels_in_to_xfer_to_or_table_list = []
        predicted_xfer_to_or_table_to_draped_list = []
        predicted_draped_to_undraped_list = []
        predicted_undraped_to_xfer_to_bed_list = []
        predicted_xfer_to_bed_to_wheels_out_list = []

        for _, row in validated_df.iterrows():
            prediction = self.make_single_prediction(
                DynamicEndPredictorInputData(**row), inference_mode
            )

            prediction_tag_list.append(prediction.prediction_tag)
            predicted_duration_list.append(prediction.predicted_total_case_duration)
            predicted_wheels_in_to_xfer_to_or_table_list.append(
                prediction.predicted_wheels_in_to_xfer_to_or_table
            )
            predicted_xfer_to_or_table_to_draped_list.append(
                prediction.predicted_xfer_to_or_table_to_draped
            )
            predicted_draped_to_undraped_list.append(prediction.predicted_draped_to_undraped)
            predicted_undraped_to_xfer_to_bed_list.append(
                prediction.predicted_undraped_to_xfer_to_bed
            )
            predicted_xfer_to_bed_to_wheels_out_list.append(
                prediction.predicted_xfer_to_bed_to_wheels_out
            )

        df_predictions = pd.DataFrame(
            {
                "prediction_tag": prediction_tag_list,
                "predicted_total_case_duration": predicted_duration_list,
                "predicted_wheels_in_to_xfer_to_or_table": predicted_wheels_in_to_xfer_to_or_table_list,
                "predicted_xfer_to_or_table_to_draped": predicted_xfer_to_or_table_to_draped_list,
                "predicted_draped_to_undraped": predicted_draped_to_undraped_list,
                "predicted_undraped_to_xfer_to_bed": predicted_undraped_to_xfer_to_bed_list,
                "predicted_xfer_to_bed_to_wheels_out": predicted_xfer_to_bed_to_wheels_out_list,
            }
        )

        return ModelOutputPydanticSchema.validate(df_predictions)

    def calculate_features_for_inference(self, data_for_prediction: BaseModel) -> pd.DataFrame:
        df_raw = pd.DataFrame([data_for_prediction.model_dump()])
        df = df_raw.copy()

        for timestamp_colname in [
            "obs_time",
            "actual_start_datetime_local",
            "first_patient_draped_datetime_local",
            "second_patient_draped_datetime_local",
            "first_patient_xfer_to_bed_datetime_local",
            "second_patient_xfer_to_bed_datetime_local",
            "first_patient_undraped_datetime_local",
            "second_patient_undraped_datetime_local",
            "first_patient_xfer_to_or_table_datetime_local",
            "second_patient_xfer_to_or_table_datetime_local",
            "actual_end_datetime_local",
        ]:
            # this step is to transform any None timestamps into NaT to enable downstream calculations
            df[timestamp_colname] = pd.to_datetime(df[timestamp_colname])

        df = self.calculate_is_occurred_columns(df)
        df = self.calculate_actual_phase_durations(df)

        df["static_minus_scheduled_duration"] = (
            df["static_forecasted_duration"] - df["scheduled_duration"]
        )
        # note, if static forecasted duration is null, it will not be flagged as naive
        df["static_forecasted_duration_is_naive"] = 1 * (
            (~df["static_forecasted_duration"].isnull())
            & (df["static_forecasted_duration"] == df["scheduled_duration"])
        ).astype(int)

        # before determining the bucket for static forecasted duration, replace nulls with scheduled duration
        df["static_forecasted_duration"] = (
            df["static_forecasted_duration"].fillna(df["scheduled_duration"]).astype(int)
        )

        df["static_duration_group"] = pd.cut(
            # in rare cases, we don't have a static forecasted duration (not even from naive)
            # when this happens, use scheduled_duration instead
            df["static_forecasted_duration"],
            bins=self.config.scheduled_duration_bins,
            labels=self.config.scheduled_duration_bin_labels,
            right=False,
        )

        return df

    def apply_predicted_phase_duration_to_all_phases(
        self, df_with_aggs: pd.DataFrame, trained_models: dict[str, Pipeline]
    ) -> pd.DataFrame:
        """
        Applies the method generate_phase_prediction to all subphases within the case
        :param df_with_aggs: The median phase durations of subphases, calculated from training data
        :return: df_with_predictions: a dataframe containing predicted subphase durations
        """
        df_with_predictions = df_with_aggs.copy()

        (
            df_with_predictions["predicted_wheels_in_to_xfer_to_or_table"],
            df_with_predictions["ml_estimate_wheels_in_to_xfer_to_or_table"],
        ) = self.generate_phase_prediction(
            df=df_with_predictions,
            agg_colname="agg_wheels_in_to_xfer_to_or_table",
            occurred_colname_first_event1="wheels_in_occurred",
            occurred_colname_first_event2="first_xfer_to_or_table_occurred",
            first_actual_phase_duration_colname="wheels_in_to_first_xfer_to_or_table",
            first_event1_datetime_colname="actual_start_datetime_local",
            trained_models=trained_models,
        )

        (
            df_with_predictions["predicted_xfer_to_or_table_to_draped"],
            df_with_predictions["ml_estimate_xfer_to_or_table_to_draped"],
        ) = self.generate_phase_prediction(
            df=df_with_predictions,
            agg_colname="agg_xfer_to_or_table_to_draped",
            occurred_colname_first_event1="first_xfer_to_or_table_occurred",
            occurred_colname_first_event2="first_draped_occurred",
            first_actual_phase_duration_colname="first_xfer_to_or_table_to_first_draped",
            first_event1_datetime_colname="first_patient_xfer_to_or_table_datetime_local",
            trained_models=trained_models,
        )

        (
            df_with_predictions["predicted_draped_to_undraped"],
            df_with_predictions["ml_estimate_draped_to_undraped"],
        ) = self.generate_phase_prediction(
            df=df_with_predictions,
            agg_colname="agg_draped_to_undraped",
            occurred_colname_first_event1="first_draped_occurred",
            occurred_colname_first_event2="first_undraped_occurred",
            first_actual_phase_duration_colname="first_draped_to_first_undraped",
            first_event1_datetime_colname="first_patient_draped_datetime_local",
            trained_models=trained_models,
        )

        (
            df_with_predictions["predicted_undraped_to_xfer_to_bed"],
            df_with_predictions["ml_estimate_undraped_to_xfer_to_bed"],
        ) = self.generate_phase_prediction(
            df=df_with_predictions,
            agg_colname="agg_undraped_to_xfer_to_bed",
            occurred_colname_first_event1="first_undraped_occurred",
            occurred_colname_first_event2="first_xfer_to_bed_occurred",
            first_actual_phase_duration_colname="first_undraped_to_first_xfer_to_bed",
            first_event1_datetime_colname="first_patient_undraped_datetime_local",
            trained_models=trained_models,
        )

        (
            df_with_predictions["predicted_xfer_to_bed_to_wheels_out"],
            df_with_predictions["ml_estimate_xfer_to_bed_to_wheels_out"],
        ) = self.generate_phase_prediction(
            df=df_with_predictions,
            agg_colname="agg_xfer_to_bed_to_wheels_out",
            occurred_colname_first_event1="first_xfer_to_bed_occurred",
            occurred_colname_first_event2="wheels_out_occurred",
            first_actual_phase_duration_colname="first_xfer_to_bed_to_wheels_out",
            first_event1_datetime_colname="first_patient_xfer_to_bed_datetime_local",
            trained_models=trained_models,
        )

        return df_with_predictions

    def generate_phase_prediction(
        self,
        df: pd.DataFrame,
        agg_colname: str,
        occurred_colname_first_event1: str,
        occurred_colname_first_event2: str,
        first_actual_phase_duration_colname: str,
        first_event1_datetime_colname: str,
        trained_models: Pipeline,
    ) -> Tuple[np.ndarray[Any, Any], np.ndarray[Any, Any]]:  # np.ndarray[float, Any]:
        """
        To explain this function, we will use patient_draped (event1) and patient_undraped (event2) as an example.

        Inputs:

            df: input features for model (includes timestamps of any events that have happened by the observed time)
            agg_colname: the column name to access the trained median phase duration of
                patient_draped to patient_undraped for this surgeon-proc-combo-num
            occurred_colname_first_event1: here, event1 is patient_draped. first_event1 refers to the first occurrence
                of patient_draped. occurred_colname_first_event1 is 0/1 depending on whether this event has happened
            occurred_colname_first_event2: here, event2 is patient_undraped. first_event2 refers to the first occurrence
                of patient_undraped. occurred_colname_first_event1 is 0/1 depending on whether this event has happened
            first_actual_phase_duration_colname: the column name to access the duration between the first occurrence
                of patient_draped and the first occurrence of patient_undraped
            first_event1_datetime_colname: the column name to access the timestamp of the first occurrence of patient_draped

        What this function does:
            1. calculate "running durations" -- the "live" view of what the duration of each phase is
                  obs_1  |    obs_2   | obs_3
                here, the | are events: event_1 and event_2 that define a phase
                obs_1 time is before both events, so the "running duration" of the phase is null (it hasn't begun yet)
                obs_2 time is between the events, so the "running duration" of the phase is observation time minus event_1
                obs_3 time is after both events, so the "running duration" of the phase is the actual duration (event_2 minus event_1)


            2. create a predicted duration for each phase. Note that "predicted duration" could be the actual duration
                if the phase has ended; a predicted value from trained model ("ml estimate") if the phase has not started;
                max(running_duration, ml estimate) if the phase is in-progress;
                or capped at 3 * historical_aggregate if the running_duration is too long and a missing event is suspected

        real phase:             -------------------- (20 minutes)
        historical median:      -----------          (10 minutes)
        predicted duration:  ↑  (before phase starts, we predict 10 mins)
        predicted duration:        ↑ (during phase, before exceeding the historical median we predict historical median)
        predicted duration:                    ↑    (during phase, if exceed historical median, predict elapsed time)
        predicted duration:                             ↑ (we predict 20 mins)
        """
        # generate predicted_duration for each phase
        # if event_2 hasn't occurred and running_duration <= median/mean for the phase, replace with median/mean for the phase
        # if event_2 hasn't occurred and running_duration > median/mean for the phase, replace with running_duration

        trained_model_for_phase = trained_models[first_actual_phase_duration_colname]

        # adjust the feature names based on the phase
        num_features = [
            feature
            for feature in self.config.num_features
            if feature
            not in ("median_phase_by_surgeon_proc_num_procs", "pct_phase_in_total_duration")
        ]

        if "median_phase_by_surgeon_proc_num_procs" in self.config.num_features:
            num_features.append(agg_colname)
        if "pct_phase_in_total_duration" in self.config.num_features:
            num_features.append("pct_" + first_actual_phase_duration_colname)

        cat_features = self.config.cat_features

        features_for_phase = num_features + cat_features

        df["ml_estimate_" + first_actual_phase_duration_colname] = trained_model_for_phase.predict(
            df[features_for_phase]
        )
        df_pred = df.copy()

        # Helper function to safely compute running_duration
        def calculate_running_duration(row):  # type: ignore
            if row[occurred_colname_first_event1] == 1 and row[occurred_colname_first_event2] == 1:
                return row[first_actual_phase_duration_colname]
            elif (
                row[occurred_colname_first_event1] == 0 and row[occurred_colname_first_event2] == 0
            ):
                return row["ml_estimate_" + first_actual_phase_duration_colname]
            elif (
                row[occurred_colname_first_event1] == 1 and row[occurred_colname_first_event2] == 0
            ):
                if pd.notnull(row["obs_time"]) and pd.notnull(row[first_event1_datetime_colname]):
                    return (
                        row["obs_time"] - row[first_event1_datetime_colname]
                    ).total_seconds() / 60
                else:
                    return pd.NA  # Handle missing timestamps gracefully
            else:
                return pd.NA

        # Apply the running_duration calculation safely
        df_pred["running_duration"] = df_pred.apply(calculate_running_duration, axis=1)

        # Calculate ceiling for predicted duration
        df_pred["predicted_duration_ceiling"] = (
            self.config.max_factor_allowed_for_running_duration
            * df_pred["ml_estimate_" + first_actual_phase_duration_colname]
        )

        # Helper function for predicted phase duration
        def predict_phase_duration(row):  # type: ignore
            if (
                row[occurred_colname_first_event1] == 1
                and row[occurred_colname_first_event2] == 0
                and pd.notnull(row["running_duration"])
                and row["running_duration"] > row["predicted_duration_ceiling"]
            ):
                return row["predicted_duration_ceiling"]

            elif (
                row[occurred_colname_first_event1] == 1
                and row[occurred_colname_first_event2] == 0
                and pd.notnull(row["running_duration"])
                and row["running_duration"]
                < row["ml_estimate_" + first_actual_phase_duration_colname]
            ):
                return row["ml_estimate_" + first_actual_phase_duration_colname]

            else:
                return row["running_duration"]

        # Apply the predicted phase duration calculation safely
        df_pred["predicted_phase_duration"] = df_pred.apply(predict_phase_duration, axis=1)

        return np.array(df_pred["predicted_phase_duration"]), np.array(
            df_pred["ml_estimate_" + first_actual_phase_duration_colname]
        )

    def calculate_actual_phase_durations(self, df: pd.DataFrame) -> pd.DataFrame:
        # Helper function to calculate duration in minutes, handling NaT
        def safe_duration(start, end) -> Any:  # type: ignore
            if pd.notnull(start) and pd.notnull(end):
                return (end - start).total_seconds() / 60
            return np.NaN

        # Apply the safe_duration function for each pair of datetime columns
        df["first_xfer_to_or_table_to_first_draped"] = df.apply(
            lambda row: safe_duration(
                row["first_patient_xfer_to_or_table_datetime_local"],
                row["first_patient_draped_datetime_local"],
            ),
            axis=1,
        )

        df["first_draped_to_first_undraped"] = df.apply(
            lambda row: safe_duration(
                row["first_patient_draped_datetime_local"],
                row["first_patient_undraped_datetime_local"],
            ),
            axis=1,
        )

        df["first_undraped_to_first_xfer_to_bed"] = df.apply(
            lambda row: safe_duration(
                row["first_patient_undraped_datetime_local"],
                row["first_patient_xfer_to_bed_datetime_local"],
            ),
            axis=1,
        )

        df["wheels_in_to_first_xfer_to_or_table"] = df.apply(
            lambda row: safe_duration(
                row["actual_start_datetime_local"],
                row["first_patient_xfer_to_or_table_datetime_local"],
            ),
            axis=1,
        )

        df["first_xfer_to_bed_to_wheels_out"] = df.apply(
            lambda row: safe_duration(
                row["first_patient_xfer_to_bed_datetime_local"], row["actual_end_datetime_local"]
            ),
            axis=1,
        )

        return df

    @staticmethod
    def _get_event_occurred_colname(timestamp_colname: str) -> str:
        if timestamp_colname == "actual_start_datetime_local":
            return "wheels_in_occurred"
        else:
            occurred_colname = (
                timestamp_colname.replace("_datetime_local", "").replace("patient_", "")
                + "_occurred"
            )
            return occurred_colname

    @staticmethod
    def _get_num_times_colname(timestamp_colname: str) -> str:
        num_times_colname = (
            "num_times_"
            + timestamp_colname.replace("_datetime_local", "")
            .replace("first_", "")
            .replace("second_", "")
            + "_in_case"
        )
        return num_times_colname

    def calculate_is_occurred_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        # figure out whether each event has happened
        # by definition (due to range allowed when exploding to different times),
        # actual_start has already happened and actual_end hasn't yet happened
        # calculate them anyway in order to unify calculation of "running durations"
        df_with_occurred = df.copy()

        # TODO: this can be part of a config later, but would require more refactoring of the whole
        # training and evaluation pipeline
        timestamp_colnames_first_events = [
            "actual_start_datetime_local",
            "first_patient_xfer_to_or_table_datetime_local",
            "first_patient_draped_datetime_local",
            "first_patient_undraped_datetime_local",
            "first_patient_xfer_to_bed_datetime_local",
        ]

        timestamp_colnames_second_events = [
            "second_patient_xfer_to_or_table_datetime_local",
            "second_patient_draped_datetime_local",
            "second_patient_undraped_datetime_local",
            "second_patient_xfer_to_bed_datetime_local",
        ]

        for timestamp_colname in timestamp_colnames_first_events:
            occurred_colname = self._get_event_occurred_colname(timestamp_colname)

            df_with_occurred[occurred_colname] = df_with_occurred.apply(
                lambda row: 1
                * (
                    pd.notnull(row[timestamp_colname]) and row["obs_time"] >= row[timestamp_colname]
                ),
                axis=1,
            )

        for timestamp_colname in timestamp_colnames_second_events:
            # if we know there are more than one of the same event_type,
            # then we can calculate whether the second event happened
            # otherwise, put in 0 because the second event never happened
            occurred_colname = self._get_event_occurred_colname(timestamp_colname)
            event_ct_colname = self._get_num_times_colname(timestamp_colname)

            # Apply row-wise to handle NaT gracefully
            df_with_occurred[occurred_colname] = df_with_occurred.apply(
                lambda row: 1
                if (
                    row[event_ct_colname] > 1
                    and pd.notnull(row[timestamp_colname])
                    and row["obs_time"] >= row[timestamp_colname]
                )
                else 0,
                axis=1,
            )

        return df_with_occurred

    def train(self, df_train_raw_location: pd.DataFrame) -> None:
        df_train_location = self.clean_up_training_data(df_train_raw_location)

        # calculate aggregates based on cleaned, non-augmented data
        self.calculate_training_aggregations(df_train_location)
        df_train_location_with_aggs = df_train_location.merge(
            self.aggregate_phase_data,
            on=["first_primary_surgeon", "first_primary_procedure", "procedure_count"],
            how="left",
        )
        df_train_location_with_aggs = df_train_location_with_aggs.merge(
            self.percent_phase_in_total_duration,
            on=["static_duration_group"],
            how="left",
        )

        df_train_location_with_aggs.to_csv("df_train_location_with_aggs.csv")

        # to increase model coverage, we need the model to be trained on instances where there is no aggregate info
        # due to the surgeon-proc-combo-num not appearing in training data
        # but by definition, all training cases HAVE been in the training data. So, augment the dataset by
        # randomly selecting cases and deleting their aggregate info
        df_train_location_augmented = self.augment_training_data(df_train_location_with_aggs)

        # train on cleaned, augmented data
        self.train_model_for_all_phases(df_train_location_augmented)
        # calculate duplicates on uncleaned data (cases have event duplicates)
        self.identify_procedures_with_duplicates(df_train_raw_location)
        # calculate procedures that often have only xfer to table and xfer to bed on uncleaned data
        self.identify_procedures_with_only_xfer_table_bed(df_train_raw_location)

    def train_model_for_all_phases(self, df_train_location_augmented: pd.DataFrame) -> None:
        trained_models_for_phases = {}

        # fit a separate model per phase
        for phase_name in [
            "wheels_in_to_first_xfer_to_or_table",
            "first_xfer_to_or_table_to_first_draped",
            "first_draped_to_first_undraped",
            "first_undraped_to_first_xfer_to_bed",
            "first_xfer_to_bed_to_wheels_out",
        ]:
            # adjust the feature names based on the phase
            num_features = [
                feature
                for feature in self.config.num_features
                if feature
                not in ("median_phase_by_surgeon_proc_num_procs", "pct_phase_in_total_duration")
            ]

            if "median_phase_by_surgeon_proc_num_procs" in self.config.num_features:
                num_features.append("agg_" + phase_name.replace("first_", ""))
            if "pct_phase_in_total_duration" in self.config.num_features:
                num_features.append("pct_" + phase_name)

            cat_features = self.config.cat_features
            features = num_features + cat_features

            ml_model = self.get_ml_model_pipeline(
                cat_features=cat_features, num_features=num_features
            )
            trained_model_for_phase = ml_model.fit(
                X=df_train_location_augmented[features], y=df_train_location_augmented[phase_name]
            )

            trained_models_for_phases[phase_name] = trained_model_for_phase

        self.trained_models = trained_models_for_phases

    def get_ml_model_pipeline(self, cat_features: list[str], num_features: list[str]) -> Pipeline:
        phase_agg_colname = [colname for colname in num_features if "agg" in colname][0]
        case_duration_agg_colnames = [
            colname for colname in num_features if "mean" in colname or "std" in colname
        ]

        # if there is a missing aggregate value, fill it with -1 to indicate no information instead of imputing
        def add_phase_agg_values_filler(df: pd.DataFrame) -> pd.DataFrame:
            df[phase_agg_colname + "_filled"] = np.where(
                pd.isna(df[phase_agg_colname]),
                [-1] * len(df),
                df[phase_agg_colname],
            )
            return df[[phase_agg_colname + "_filled"]]

        def add_static_minus_scheduled_filler(df: pd.DataFrame) -> pd.DataFrame:
            df["static_minus_scheduled_duration_filled"] = np.where(
                pd.isna(df["static_minus_scheduled_duration"]),
                [0] * len(df),
                df["static_minus_scheduled_duration"],
            )
            return df[["static_minus_scheduled_duration_filled"]]

        def feature_names_out_phase_agg_with_filled(
            _: None, input_features: list[str]
        ) -> list[str]:
            return [phase_agg_colname + "_filled"]

        def feature_names_out_static_minus_scheduled_with_filled(
            _: None, input_features: list[str]
        ) -> list[str]:
            return ["static_minus_scheduled_duration_filled"]

        # get numerical features that don't need a filler value
        num_features_cleaned = [
            feature
            for feature in num_features
            if (
                feature != phase_agg_colname
                and feature != "static_minus_scheduled_duration"
                and feature not in case_duration_agg_colnames
            )
        ]

        preprocessor = ColumnTransformer(
            transformers=[
                (
                    "filler_for_agg_values",
                    FunctionTransformer(
                        func=add_phase_agg_values_filler,
                        validate=False,
                        feature_names_out=feature_names_out_phase_agg_with_filled,
                    ),
                    [phase_agg_colname],
                ),
                (
                    "filler_for_static_minus_scheduled_values",
                    FunctionTransformer(
                        func=add_static_minus_scheduled_filler,
                        validate=False,
                        feature_names_out=feature_names_out_static_minus_scheduled_with_filled,
                    ),
                    ["static_minus_scheduled_duration"],
                ),
                (
                    "impute",
                    SimpleImputer(strategy="constant", fill_value=-1),
                    case_duration_agg_colnames,
                ),
                ("num", "passthrough", num_features_cleaned),
                ("cat", "passthrough", cat_features),
            ],
        )

        model_pipeline = Pipeline(
            # hyperparameters found by tuning previous model
            steps=[
                ("preprocessor", preprocessor),
                (
                    "catboost",
                    CatBoostRegressor(
                        # iterations=self.config.iterations,
                        # learning_rate=self.config.learning_rate,
                        # depth=self.config.depth,
                        loss_function="RMSE",
                        logging_level="Silent",
                        # 9 numerical features (idx 0,1,2, 3, 4,) , followed by 3 categorical feature)
                        cat_features=[5, 6, 7],
                    ),
                ),
            ]
        )

        return model_pipeline

    def clean_up_training_data(self, df_train_raw: pd.DataFrame) -> pd.DataFrame:
        # get only the cases with exactly one of each event (no duplicates, no missing)
        # we only want to clean up the training data this way, not the test data
        df_train_live = df_train_raw[df_train_raw["apella_data"] == 1]
        df_train_live = pd.DataFrame(
            df_train_live[
                (df_train_live["num_times_patient_undraped_in_case"] == 1)
                & (df_train_live["num_times_patient_draped_in_case"] == 1)
                & (df_train_live["num_times_patient_xfer_to_or_table_in_case"] == 1)
                & (df_train_live["num_times_patient_xfer_to_bed_in_case"] == 1)
            ]
        )

        df_train_live["static_minus_scheduled_duration"] = (
            df_train_live["static_forecasted_duration"] - df_train_live["scheduled_duration"]
        )
        df_train_live["static_forecasted_duration_is_naive"] = 1 * (
            (~df_train_live["static_forecasted_duration"].isnull())
            & (df_train_live["static_forecasted_duration"] == df_train_live["scheduled_duration"])
        )

        df_train_live = df_train_live[~df_train_live["static_forecasted_duration"].isnull()]

        df_train_live["static_duration_group"] = pd.cut(
            df_train_live["static_forecasted_duration"],
            bins=self.config.scheduled_duration_bins,
            labels=self.config.scheduled_duration_bin_labels,
            right=False,
        )

        return df_train_live

    def augment_training_data(self, df_train_location: pd.DataFrame) -> pd.DataFrame:
        df_train_location_random_selection = df_train_location.copy()

        # artificially replace aggregate values with nulls
        for agg_col in [
            "agg_wheels_in_to_xfer_to_or_table",
            "agg_xfer_to_or_table_to_draped",
            "agg_draped_to_undraped",
            "agg_undraped_to_xfer_to_bed",
            "agg_xfer_to_bed_to_wheels_out",
        ]:
            df_train_location_random_selection[agg_col] = np.NaN

        df_train_location_augmented = pd.concat(
            [df_train_location, df_train_location_random_selection], axis=0
        )

        return df_train_location_augmented

    def identify_procedures_with_only_xfer_table_bed(
        self, df_train_raw_location: pd.DataFrame
    ) -> None:
        df_pct_only_xfer_bed_table = (
            df_train_raw_location.groupby(["site_id", "first_primary_procedure"])[
                ["case_has_only_xfer_bed_table"]
            ]
            .mean(numeric_only=True)
            .reset_index()
            .rename(columns={"case_has_only_xfer_bed_table": "pct_only_xfer_bed_table"})
        )

        df_site_procs_with_only_xfer_table_bed = df_pct_only_xfer_bed_table[
            df_pct_only_xfer_bed_table["pct_only_xfer_bed_table"] >= 0.9
        ]

        self.procedures_with_only_xfer_table_bed = df_site_procs_with_only_xfer_table_bed

    def identify_procedures_with_duplicates(self, df_train_raw_location: pd.DataFrame) -> None:
        # filter to keep only the cases with one or more events
        event_counter_cols = [
            "num_times_patient_xfer_to_or_table_in_case",
            "num_times_patient_draped_in_case",
            "num_times_patient_undraped_in_case",
            "num_times_patient_xfer_to_bed_in_case",
        ]
        df_train_raw_location = df_train_raw_location.copy()
        for event_counter_col in event_counter_cols:
            df_train_raw_location["has_dupe_" + event_counter_col] = df_train_raw_location[
                event_counter_col
            ].apply(lambda x: 0 if x == 1 else (1 if x > 1 else np.NaN))

        df_train_with_dupes = (
            df_train_raw_location.groupby(["first_primary_procedure"])[
                ["has_dupe_" + event_counter_col for event_counter_col in event_counter_cols]
            ]
            # Do np.nanmean instead of mean b/c some cases might have missing events, but we still
            # want to include their duplicate events. Similarly, use size instead of count to include
            # cases with some missing events
            .agg(["mean", "size"])
            .reset_index()
        )

        # filter to more "popular" procedures (ones with at least certain number of cases in a site)
        df_train_with_dupes = df_train_with_dupes[
            df_train_with_dupes[("has_dupe_num_times_patient_xfer_to_or_table_in_case", "size")]
            >= 3
        ]

        # do the filter on the event with the most duplicates within the procedure
        df_train_with_dupes["pct_cases_with_any_event_duplicate"] = 100 * df_train_with_dupes[
            [
                ("has_dupe_num_times_patient_xfer_to_or_table_in_case", "mean"),
                ("has_dupe_num_times_patient_draped_in_case", "mean"),
                ("has_dupe_num_times_patient_undraped_in_case", "mean"),
                ("has_dupe_num_times_patient_xfer_to_bed_in_case", "mean"),
            ]
        ].max(axis=1)

        procedures_with_high_duplicate_rate = df_train_with_dupes[
            df_train_with_dupes["pct_cases_with_any_event_duplicate"]
            > self.config.pct_duplicate_cutoff
        ][["first_primary_procedure", "pct_cases_with_any_event_duplicate"]]

        self.procedures_with_high_duplicate_events = procedures_with_high_duplicate_rate

    def calculate_training_aggregations(self, df_train_location: pd.DataFrame) -> None:
        """
        df_train_location: contains only cases with apella events (live data)
            This is used to generate aggregate features related to subphase durations, which requires
            clean apella live data
        df_train_location_raw: contains cases with apella events AND historical data dumps
            This is used to generate features like mean and stdev of total case duration by site x surgeon
            and by site x procedure, and don't need apella events

        """
        # eliminate phases out of order, these negative phase durations can impact the median
        phase_name_cols = [
            "wheels_in_to_first_xfer_to_or_table",
            "first_xfer_to_or_table_to_first_draped",
            "first_draped_to_first_undraped",
            "first_undraped_to_first_xfer_to_bed",
            "first_xfer_to_bed_to_wheels_out",
        ]

        has_negative_agg_phase = (df_train_location[phase_name_cols] < 0).any(axis=1)
        df_train_location_no_negative_phases = df_train_location[~has_negative_agg_phase].copy()

        df_aggs = (
            df_train_location_no_negative_phases.groupby(
                ["first_primary_surgeon", "first_primary_procedure", "procedure_count"],
                observed=False,
            )
            .median(numeric_only=True)
            .reset_index()[
                [
                    "first_primary_surgeon",
                    "first_primary_procedure",
                    "procedure_count",
                    "wheels_in_to_first_xfer_to_or_table",
                    "first_xfer_to_or_table_to_first_draped",
                    "first_draped_to_first_undraped",
                    "first_undraped_to_first_xfer_to_bed",
                    "first_xfer_to_bed_to_wheels_out",
                ]
            ]
        )

        df_aggs = df_aggs.rename(
            columns={
                "wheels_in_to_first_xfer_to_or_table": "agg_wheels_in_to_xfer_to_or_table",
                "first_xfer_to_or_table_to_first_draped": "agg_xfer_to_or_table_to_draped",
                "first_draped_to_first_undraped": "agg_draped_to_undraped",
                "first_undraped_to_first_xfer_to_bed": "agg_undraped_to_xfer_to_bed",
                "first_xfer_to_bed_to_wheels_out": "agg_xfer_to_bed_to_wheels_out",
            }
        )

        # after left-joining to test data, any nulls in this column will indicate case that we can't predict for
        df_aggs["surgeon_proc_combo_num_appeared_in_training"] = 1

        for phase_name in phase_name_cols:
            df_train_location_no_negative_phases["pct_" + phase_name + "_raw"] = (
                100
                * df_train_location_no_negative_phases[phase_name]
                / df_train_location_no_negative_phases["actual_duration"]
            )

        df_percent_phase_in_total_duration = (
            df_train_location_no_negative_phases.groupby(["static_duration_group"], observed=False)
            .mean(numeric_only=True)
            .reset_index()[
                ["static_duration_group"]
                + ["pct_" + phase_name + "_raw" for phase_name in phase_name_cols]
            ]
        )

        df_percent_phase_in_total_duration["static_duration_group_start"] = (
            df_percent_phase_in_total_duration[
                "static_duration_group"
            ].map({"0-60": 0, "61-180": 60, "181-270": 181, "271-5000": 271})
        )

        df_percent_phase_in_total_duration = df_percent_phase_in_total_duration.sort_values(
            ["static_duration_group_start"]
        )

        # for static_duration_group that don't have any data,
        # find the nearest values within the site and fill it in
        for phase_name in phase_name_cols:
            df_percent_phase_in_total_duration["pct_" + phase_name] = (
                df_percent_phase_in_total_duration["pct_" + phase_name + "_raw"].ffill().bfill()
            )

        self.aggregate_phase_data = df_aggs
        self.percent_phase_in_total_duration = df_percent_phase_in_total_duration
