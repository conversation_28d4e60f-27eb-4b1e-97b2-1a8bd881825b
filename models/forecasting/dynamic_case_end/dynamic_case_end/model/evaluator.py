from datetime import datetime, timezone
from typing import Any, List

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from google.api_core.retry import Retry
from google.cloud.bigquery import Client as BQ<PERSON>lient
from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON>, Table, TimePartitioning, TimePartitioningType
from training_utils.clearml_reporter import Clear<PERSON><PERSON>eporter

from dynamic_case_end.configs.prod_config import EvaluationConfig
from dynamic_case_end.model.dynamic_case_end_model import DynamicCaseEndModel


class Evaluator:
    def __init__(
        self,
        models: dict[str, DynamicCaseEndModel],
        df_test_raw: pd.DataFrame,
        df_transformer_predictions: pd.DataFrame,
        evaluation_config: EvaluationConfig,
        sites_to_evaluate: list[str],
        reporter: ClearMLReporter,
        bq_client: BQClient,
    ):
        self.models = models
        # "raw" just means that it's not cleaned to "exactly one event per case"
        # we will be testing heuristics on the cases that don't have exactly one event per case
        self.df_test = df_test_raw
        self.df_transformer_predictions = df_transformer_predictions
        self.reporter = reporter
        self.bq_client = bq_client
        self.evaluation_config = evaluation_config
        # need to rename case_id column so it can be used in Predictor
        self.df_test = self.df_test.rename(columns={"all_cases_case_id": "case_id"})
        self.sites_to_evaluate = sites_to_evaluate

    def evaluate_models(self) -> pd.DataFrame:
        now_timestamp = datetime.now(tz=timezone.utc).isoformat()  # for writing predictions to DWH
        ds_date = datetime.now(tz=timezone.utc).date()

        # explode the test set to time increments
        df_test_with_time_increments = self.explode_to_time_increments()

        # simulate the timestamps and features available at obs_time
        df_test_with_time_increments = self.simulate_inference_features(
            df_test_with_time_increments
        )

        # model evaluation
        df_predictions_all_sites = pd.DataFrame()  # to calculate coverage and mae at all sites
        for location in self.evaluation_config.evaluation_to_fit_mapping:
            if location in self.sites_to_evaluate:
                df_test_site = df_test_with_time_increments[
                    df_test_with_time_increments[self.evaluation_config.location_type_to_evaluate]
                    == location
                ]
                df_test_site_subset = df_test_site[
                    df_test_site["obs_time_minutes_before_wheels_out"].isin(
                        [1] + list(range(5, 305, 5))
                    )
                ]

                if df_test_site_subset.empty:
                    print(f"Skipping {location} as the test set is empty.")
                    continue

                # set the model we fit.
                fit_model = self.models[self.evaluation_config.evaluation_to_fit_mapping[location]]

                # inference_mode needs to be False in order to correctly evaluate cutover event
                df_predictions = fit_model.run_model_prediction_for_df(
                    df_test_site_subset, inference_mode=False
                )

                # join to one-row-per-case df_test to get actual end time, etc -- needed for evaluation
                df_predictions_with_case_data = pd.concat(
                    [
                        df_predictions.reset_index(drop=True),
                        df_test_site_subset[
                            [
                                "case_id",
                                "site_id",
                                "actual_end_datetime_local",
                                "actual_start_datetime_local",
                                "actual_duration",
                                "scheduled_duration",
                                "static_forecasted_duration",
                                "first_primary_surgeon",
                                "first_primary_procedure",
                                "procedure_count",
                                "obs_time_minutes_before_wheels_out",
                                "obs_time",
                                "first_patient_xfer_to_or_table_datetime_local",
                                "first_patient_draped_datetime_local",
                                "first_patient_undraped_datetime_local",
                                "first_patient_xfer_to_bed_datetime_local",
                            ]
                        ].reset_index(drop=True),
                    ],
                    axis=1,
                )

                assert len(df_predictions_with_case_data) == len(df_predictions)

                print(
                    f"prediction tag counts for {location} before transformer replacement",
                )
                print(
                    df_predictions_with_case_data.groupby("prediction_tag").count(),
                )

                # concatenate this site's predictions to all other sites' predictions
                df_predictions_all_sites = pd.concat(
                    [df_predictions_all_sites, df_predictions_with_case_data]
                )

            if location not in self.sites_to_evaluate:
                print(f"Not evaluating at {location} because not enough training data at this site")

        if df_predictions_all_sites.empty:
            print("No predictions were made for any sites.")
            return pd.DataFrame()

        # TODO: replace null with predicted static case duration when heuristic is needed
        df_predictions_all_sites = self.apply_static_forecasted_duration_heuristic(
            df_predictions_all_sites
        )

        # if transformer is available, replace pythia prediction with transformer prediction
        # this is a better representation of prod behavior
        df_predictions_all_sites = self.replace_pythia_with_transformer(df_predictions_all_sites)
        df_predictions_all_sites = self.calculate_errors(df_predictions_all_sites)

        df_predictions_all_sites["ds"] = ds_date
        df_predictions_all_sites["training_run_at"] = now_timestamp
        cols_to_upload = [
            "case_id",
            "site_id",
            "actual_duration",
            "scheduled_duration",
            "static_forecasted_duration",
            "predicted_total_case_duration",
            "prediction_tag",
            "predicted_wait_minutes",
            "static_predicted_wait_minutes",
            "predicted_end_datetime_local",
            "static_predicted_end_datetime_local",
            "prediction_error",
            "abs_prediction_error",
            "static_prediction_error",
            "static_abs_prediction_error",
            "abs_percent_prediction_error",
            "obs_time_minutes_to_forecasted_wheels_out",
            "obs_time_minutes_to_forecasted_wheels_out_category",
            "obs_time_minutes_before_wheels_out",
            "obs_time",
            "first_patient_xfer_to_or_table_datetime_local",
            "first_patient_draped_datetime_local",
            "first_patient_undraped_datetime_local",
            "first_patient_xfer_to_bed_datetime_local",
            "training_run_at",
            "ds",
        ]

        df_predictions_all_sites.to_csv("df_predictions_all_sites.csv")

        static_to_pythia_cutover_event = self.find_static_to_pythia_cutovers_by_event(
            df_predictions_all_sites
        )
        print("found static_to_pythia_cutover_event", static_to_pythia_cutover_event)
        self.reporter.report_dataframe_as_table(
            pd.DataFrame(
                {"static_to_pythia_cutover_event": static_to_pythia_cutover_event}, index=[0]
            ),
            report_group="Validation",
            table_name="Static to Pythia Cutover by Event Type",
        )

        # filter to only predictions made by dynamic end model to evaluate them separately
        df_predictions_cleanest_all_sites = df_predictions_all_sites[
            df_predictions_all_sites["prediction_tag"] == "dynamic_end_prediction"
        ]

        # calculate 5 min pushback metrics
        self.plot_pushback_metrics(df_predictions_all_sites)

        (
            df_mae_cleanest_relative_to_wheels_out,
            df_mae_cleanest_relative_to_forecasted,
        ) = self.calculate_mae(
            df_predictions_subset=df_predictions_cleanest_all_sites,
            obs_time_vs_other_time_colnames=[
                "obs_time_minutes_before_wheels_out",
                "obs_time_minutes_to_forecasted_wheels_out_category",
            ],
        )

        df_mae_all_relative_to_wheels_out, df_mae_all_relative_to_forecasted = self.calculate_mae(
            df_predictions_subset=df_predictions_all_sites,
            obs_time_vs_other_time_colnames=[
                "obs_time_minutes_before_wheels_out",
                "obs_time_minutes_to_forecasted_wheels_out_category",
            ],
        )

        df_mae_cleanest_relative_to_wheels_out.to_csv("df_mae_cleanest_relative_to_wheels_out.csv")
        df_mae_cleanest_relative_to_forecasted.to_csv("df_mae_cleanest_relative_to_forecasted.csv")
        df_mae_all_relative_to_wheels_out.to_csv("df_mae_all_relative_to_wheels_out.csv")
        df_mae_all_relative_to_forecasted.to_csv("df_mae_all_relative_to_forecasted.csv")

        self.plot_scalars_comparing_pythia_to_static(df_mae_all_relative_to_forecasted)

        self.plot_error_evaluation_metrics(
            df_eval_metrics=df_mae_cleanest_relative_to_wheels_out,
            obs_time_vs_other_time_colname="obs_time_minutes_before_wheels_out",
            max_x_axis=120,
            title_name="predictions made exclusively from statistical model",
        )

        self.plot_error_evaluation_metrics(
            df_eval_metrics=df_mae_cleanest_relative_to_forecasted,
            obs_time_vs_other_time_colname="obs_time_minutes_to_forecasted_wheels_out_category",
            max_x_axis=120,
            title_name="predictions made exclusively from statistical model",
        )

        self.plot_error_evaluation_metrics(
            df_eval_metrics=df_mae_all_relative_to_wheels_out,
            obs_time_vs_other_time_colname="obs_time_minutes_before_wheels_out",
            max_x_axis=120,
            title_name="all predictions, including heuristics",
        )

        self.plot_error_evaluation_metrics(
            df_eval_metrics=df_mae_all_relative_to_forecasted,
            obs_time_vs_other_time_colname="obs_time_minutes_to_forecasted_wheels_out_category",
            max_x_axis=120,
            title_name="all predictions, including heuristics",
        )

        self.plot_coverage_evaluation_metrics(
            df_test_with_predictions=df_predictions_all_sites,
            coverage_at_obs_time_minutes_before_wheels_out=15,
        )

        return df_predictions_all_sites[cols_to_upload]

    def plot_pushback_metrics(self, df_predictions_all_sites: pd.DataFrame) -> None:
        # first, for each case, find fraction of forecasts needing 5min pushback
        df_pct_case_needing_pushback_int = (
            df_predictions_all_sites.groupby(["site_id", "case_id"])[
                ["prediction_needs_5min_pushback", "static_prediction_needs_5min_pushback"]
            ]
            .mean(numeric_only=True)
            .reset_index()
            .rename(
                columns={
                    "prediction_needs_5min_pushback": "pct_case_needing_5min_pushback",
                    "static_prediction_needs_5min_pushback": "pct_case_needing_5min_pushback_static",
                }
            )
        )

        # then avg across sites
        df_pct_case_needing_pushback = (
            df_pct_case_needing_pushback_int.groupby("site_id")[
                ["pct_case_needing_5min_pushback", "pct_case_needing_5min_pushback_static"]
            ]
            .mean(numeric_only=True)
            .reset_index()
        )

        df_pct_case_needing_pushback["pct_case_needing_5min_pushback"] = (
            df_pct_case_needing_pushback["pct_case_needing_5min_pushback"] * 100
        )
        df_pct_case_needing_pushback["pct_case_needing_5min_pushback_static"] = (
            df_pct_case_needing_pushback["pct_case_needing_5min_pushback_static"] * 100
        )

        df_pct_case_needing_pushback["static_needs_more_5min_pushbacks"] = (
            df_pct_case_needing_pushback["pct_case_needing_5min_pushback_static"]
            > df_pct_case_needing_pushback["pct_case_needing_5min_pushback"]
        )

        self.reporter.report_dataframe_as_table(
            df_pct_case_needing_pushback,
            report_group="Validation",
            table_name="Percent of Case Requiring 5min Pushback",
        )

    def replace_pythia_with_transformer(self, df_predictions: pd.DataFrame) -> pd.DataFrame:
        """
        Where transformer prediction is not null, replace pythia with transformer to mirror prod behavior
        """

        # join transformer prediction
        df_predictions = df_predictions.merge(
            self.df_transformer_predictions,
            on=["case_id", "site_id", "obs_time_minutes_before_wheels_out"],
            how="left",
        )

        df_predictions.loc[
            ~df_predictions["transformer_forecasted_duration"].isna(),
            "predicted_total_case_duration",
        ] = df_predictions["transformer_forecasted_duration"]

        df_predictions.loc[
            ~df_predictions["transformer_forecasted_duration"].isna(), "prediction_tag"
        ] = "transformer_replaced_pythia"

        return df_predictions

    def find_static_to_pythia_cutovers_by_event(
        self, df_predictions_all_sites: pd.DataFrame
    ) -> str:
        """
        Identify cutovers based on events that have happened

        First, split predictions based on the "stage" they are in. Stages are defined based on the starting point,
        but the stage continues to the next event (which might not be consecutive if missing/out of order events)
        This is a simulation of what's available at inference: we can see that e.g. draped has happened,
        but we don't know if undraped will ever happen (might be missing or out of order)
        The stages are:
        - 1 = after wheels in
        - 2 = after first xfer to or table
        - 3 = after first draped
        - 4 = after first undraped
        - 5 = after first xfer to bed
        Example: if we are calculating cutover for a single case,
        - If an event is missing, its stage will be skipped
        - If no events happen, it will always be in stage 1 (because the and no pythia predictions will be made for the case
        - If events happen out of order, the event that happens earlier will be used to calculate the stage and
          it will appear to the model that the case has progressed faster than expected
        """

        # these timestamps are null if they haven't happened (due to simulating what is available at inference)
        # fill them in again to make evaluation of cutovers by events easier
        df_predictions_all_sites = df_predictions_all_sites.sort_values(["case_id", "obs_time"])
        for timestamp_colname in [
            "first_patient_xfer_to_or_table_datetime_local",
            "first_patient_draped_datetime_local",
            "first_patient_undraped_datetime_local",
            "first_patient_xfer_to_bed_datetime_local",
        ]:
            df_predictions_all_sites[timestamp_colname] = df_predictions_all_sites.groupby(
                "case_id"
            )[timestamp_colname].bfill()

        event_stage_mapping = {
            1: "actual_start_datetime_local",
            2: "first_patient_xfer_to_or_table_datetime_local",
            3: "first_patient_draped_datetime_local",
            4: "first_patient_undraped_datetime_local",
            5: "first_patient_xfer_to_bed_datetime_local",
        }

        for stage, event_name in event_stage_mapping.items():
            df_predictions_all_sites.loc[
                df_predictions_all_sites["obs_time"] >= df_predictions_all_sites[event_name],
                "stage",
            ] = stage

        df_maes = (
            df_predictions_all_sites.groupby("stage")
            .mean(numeric_only=True)
            .reset_index()[["stage", "static_abs_prediction_error", "abs_prediction_error"]]
            .rename(
                columns={
                    "abs_prediction_error": "pythia_mae",
                    "static_abs_prediction_error": "static_mae",
                }
            )
        )

        df_maes["pythia_better_than_static"] = df_maes["pythia_mae"] < df_maes["static_mae"]

        df_maes = df_maes.sort_values("stage", ascending=False)

        df_maes["row_ct"] = np.array(range(len(df_maes))) + 1
        df_maes["cumsum_pythia_better_than_static"] = df_maes.agg(
            {"pythia_better_than_static": "cumsum"}
        )

        df_cutover = df_maes[df_maes["row_ct"] == df_maes["cumsum_pythia_better_than_static"]]
        earliest_stage_pythia_better_than_static = df_cutover["stage"].min()
        earliest_event_pythia_better_than_static = event_stage_mapping[
            int(earliest_stage_pythia_better_than_static)
        ]

        return str(earliest_event_pythia_better_than_static)

    def apply_static_forecasted_duration_heuristic(
        self, df_predictions: pd.DataFrame
    ) -> pd.DataFrame:
        # need minutes_since_actual_start to calculate wait time when we need to activate heuristic
        df_predictions["minutes_since_actual_start"] = (
            df_predictions["obs_time"] - df_predictions["actual_start_datetime_local"]
        ).dt.total_seconds() / 60

        # for the predictions that required a heuristic, use the scheduled duration for now
        #   note that this heuristic isn't implemented in the inference class because we will probably
        #   be using the static case duration model instead of the scheduled duration in production
        df_predictions["static_forecasted_duration"] = np.where(
            df_predictions["static_forecasted_duration"].isnull(),
            df_predictions["scheduled_duration"],
            df_predictions["static_forecasted_duration"],
        )

        df_predictions["predicted_total_case_duration"] = np.where(
            (df_predictions["prediction_tag"] != "dynamic_end_prediction")
            | (df_predictions["predicted_total_case_duration"].isnull()),
            df_predictions["static_forecasted_duration"],
            df_predictions["predicted_total_case_duration"],
        )

        df_predictions["predicted_wait_minutes"] = (
            df_predictions["predicted_total_case_duration"]
            - df_predictions["minutes_since_actual_start"]
        )
        df_predictions["static_predicted_wait_minutes"] = (
            df_predictions["static_forecasted_duration"]
            - df_predictions["minutes_since_actual_start"]
        )

        # identify where the 5-minute pushback is needed
        df_predictions["prediction_needs_5min_pushback"] = np.where(
            df_predictions["predicted_wait_minutes"] < 0,
            1,
            0,
        )
        df_predictions["static_prediction_needs_5min_pushback"] = np.where(
            df_predictions["static_predicted_wait_minutes"] < 0,
            1,
            0,
        )

        # if the running duration of the case exceeds the scheduled duration, replace predicted wait time with 5 minutes
        df_predictions["predicted_wait_minutes"] = np.where(
            df_predictions["predicted_wait_minutes"] < 0,
            5,
            df_predictions["predicted_wait_minutes"],
        )
        df_predictions["static_predicted_wait_minutes"] = np.where(
            df_predictions["static_predicted_wait_minutes"] < 0,
            5,
            df_predictions["static_predicted_wait_minutes"],
        )

        # calculate predicted end time to include the new heuristic wait time
        df_predictions["predicted_end_datetime_local"] = df_predictions[
            "obs_time"
        ] + pd.to_timedelta(df_predictions["predicted_wait_minutes"], unit="m")
        df_predictions["static_predicted_end_datetime_local"] = df_predictions[
            "obs_time"
        ] + pd.to_timedelta(df_predictions["static_predicted_wait_minutes"], unit="m")

        return df_predictions

    def calculate_mae(
        self, df_predictions_subset: pd.DataFrame, obs_time_vs_other_time_colnames: list[str]
    ) -> List[pd.DataFrame]:
        eval_metrics_list = []

        for colname in obs_time_vs_other_time_colnames:
            # first, get avg errors by case AND predicted wait time/time to wheels out
            # this reduces the weight of cases that have a lot of 20-minute pushbacks
            # while still retaining our ability to see the higher error associated with
            # a case having a lot of pushbacks
            df_eval_metrics_by_case = (
                df_predictions_subset.groupby(["site_id", colname, "case_id"])[
                    ["prediction_error", "abs_prediction_error", "static_abs_prediction_error"]
                ]
                .mean(numeric_only=True)
                .reset_index()
                .rename(
                    columns={
                        "prediction_error": "prediction_error_by_case",
                        "abs_prediction_error": "abs_prediction_error_by_case",
                        "static_abs_prediction_error": "static_abs_prediction_error_by_case",
                    }
                )
            )

            df_eval_metrics = (
                df_eval_metrics_by_case.groupby(["site_id", colname])[
                    [
                        "prediction_error_by_case",
                        "abs_prediction_error_by_case",
                        "static_abs_prediction_error_by_case",
                    ]
                ]
                .mean(numeric_only=True)
                .reset_index()
                .rename(
                    columns={
                        "prediction_error_by_case": "prediction_error",
                        "abs_prediction_error_by_case": "abs_prediction_error",
                        "static_abs_prediction_error_by_case": "static_abs_prediction_error",
                    }
                )
            )
            eval_metrics_list.append(df_eval_metrics)

        return eval_metrics_list

    def calculate_errors(self, df_predictions: pd.DataFrame) -> pd.DataFrame:
        df_predictions_with_errors = df_predictions.copy()

        # pythia prediction error
        df_predictions_with_errors["prediction_error"] = (
            pd.to_datetime(df_predictions_with_errors["predicted_end_datetime_local"])
            - pd.to_datetime(df_predictions_with_errors["actual_end_datetime_local"])
        ).dt.total_seconds() / 60
        df_predictions_with_errors["abs_prediction_error"] = np.abs(
            df_predictions_with_errors["prediction_error"]
        )

        # static prediction error
        df_predictions_with_errors["static_prediction_error"] = (
            pd.to_datetime(df_predictions_with_errors["static_predicted_end_datetime_local"])
            - pd.to_datetime(df_predictions_with_errors["actual_end_datetime_local"])
        ).dt.total_seconds() / 60
        df_predictions_with_errors["static_abs_prediction_error"] = np.abs(
            df_predictions_with_errors["static_prediction_error"]
        )

        df_predictions_with_errors["abs_percent_prediction_error"] = (
            100
            * df_predictions_with_errors["abs_prediction_error"]
            / df_predictions_with_errors["actual_duration"]
        )

        df_predictions_with_errors["obs_time_minutes_to_forecasted_wheels_out"] = np.round(
            (
                pd.to_datetime(df_predictions_with_errors["predicted_end_datetime_local"])
                - pd.to_datetime(df_predictions_with_errors["obs_time"])
            ).dt.total_seconds()
            / 60
        )

        df_predictions_with_errors["obs_time_minutes_to_forecasted_wheels_out_category"] = (
            10
            * np.round(df_predictions_with_errors["obs_time_minutes_to_forecasted_wheels_out"] / 10)
        )

        return df_predictions_with_errors

    def explode_to_time_increments(self) -> pd.DataFrame:
        self.df_test["obs_time"] = self.df_test.apply(self.calculate_time_increments, axis=1)
        df_test_with_time_increments = self.df_test.explode("obs_time")
        df_test_with_time_increments["obs_time_minutes_before_wheels_out"] = (
            (
                pd.to_datetime(df_test_with_time_increments["actual_end_datetime_local"])
                - pd.to_datetime(df_test_with_time_increments["obs_time"])
            ).dt.total_seconds()
            / 60
        ).astype(int)

        # only want to keep rows before wheels-out
        df_test_with_time_increments["wheels_out_occurred"] = 1 * (
            df_test_with_time_increments["obs_time"]
            >= df_test_with_time_increments["actual_end_datetime_local"]
        )
        df_test_with_time_increments = df_test_with_time_increments[
            df_test_with_time_increments["wheels_out_occurred"] == 0
        ]
        df_test_with_time_increments["num_times_wheels_out_in_case"] = 0

        return df_test_with_time_increments

    @staticmethod
    def calculate_time_increments(row: pd.Series) -> Any:  # type: ignore
        return pd.date_range(
            start=row["actual_end_datetime_local"],
            end=row["actual_start_datetime_local"],
            # count backwards from end time
            freq="-1min",
            inclusive="right",
        )

    def simulate_inference_features(
        self, df_test_with_time_increments: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Simulate the information that would have been observed at obs_time
        for example, we don't know in advance if there will be a duplicate and don't know when
        patient xfer to bed at wheels-in time

        input: df_test_with_time_increments
            contains the timestamps of all events, known as of case end
            however, these timestamps are not necessarily known yet at obs_time

        output: df_test_simulated_at_obs_time
            timestamps that haven't happened by obs_time are replaced with null
            counts of "events seen as of obs_time" are calculated
        """

        df_test_simulated_at_obs_time = df_test_with_time_increments.copy()

        # for the first timestamp
        for first_timestamp_colname in [
            "first_patient_draped_datetime_local",
            "first_patient_xfer_to_bed_datetime_local",
            "first_patient_undraped_datetime_local",
            "first_patient_xfer_to_or_table_datetime_local",
        ]:
            # if it's "currently"  before the timestamp, then replace timestamp with null
            df_test_simulated_at_obs_time[first_timestamp_colname] = df_test_simulated_at_obs_time[
                first_timestamp_colname
            ].where(
                df_test_simulated_at_obs_time["obs_time"]
                >= df_test_simulated_at_obs_time[first_timestamp_colname],
                pd.NaT,
            )
            df_test_simulated_at_obs_time[first_timestamp_colname] = pd.to_datetime(
                df_test_simulated_at_obs_time[first_timestamp_colname]
            )

        # for the second timestamp, need to also identify whether a duplicate was detected
        # (i.e. the first timestamp doesn't equal the second timestamp)
        for second_timestamp_colname in [
            "second_patient_draped_datetime_local",
            "second_patient_xfer_to_bed_datetime_local",
            "second_patient_undraped_datetime_local",
            "second_patient_xfer_to_or_table_datetime_local",
        ]:
            first_timestamp_colname = second_timestamp_colname.replace("second", "first")
            df_test_simulated_at_obs_time[second_timestamp_colname] = df_test_simulated_at_obs_time[
                second_timestamp_colname
            ].where(
                (
                    (
                        ~df_test_simulated_at_obs_time[first_timestamp_colname].isnull()
                    )  # the first timestamp needs to have occurred already
                    & (
                        df_test_simulated_at_obs_time["obs_time"]
                        >= df_test_simulated_at_obs_time[second_timestamp_colname]
                    )  # the obs time needs to be after the second timestamp
                    & (
                        df_test_simulated_at_obs_time[first_timestamp_colname]
                        < df_test_simulated_at_obs_time[second_timestamp_colname]
                    )  # the second timestamp must be after the first timestamp
                ),
                pd.NaT,
            )
            df_test_simulated_at_obs_time[second_timestamp_colname] = pd.to_datetime(
                df_test_simulated_at_obs_time[second_timestamp_colname]
            )

        # at the obs_time, need to get the correct count of observed events so far
        df_test_simulated_at_obs_time["num_times_patient_draped_in_case"] = (
            self.simulate_known_event_counts_at_inference(
                df=df_test_simulated_at_obs_time,
                first_timestamp_column="first_patient_draped_datetime_local",
                second_timestamp_column="second_patient_draped_datetime_local",
            )
        )
        df_test_simulated_at_obs_time["num_times_patient_xfer_to_bed_in_case"] = (
            self.simulate_known_event_counts_at_inference(
                df=df_test_simulated_at_obs_time,
                first_timestamp_column="first_patient_xfer_to_bed_datetime_local",
                second_timestamp_column="second_patient_xfer_to_bed_datetime_local",
            )
        )
        df_test_simulated_at_obs_time["num_times_patient_undraped_in_case"] = (
            self.simulate_known_event_counts_at_inference(
                df=df_test_simulated_at_obs_time,
                first_timestamp_column="first_patient_undraped_datetime_local",
                second_timestamp_column="second_patient_undraped_datetime_local",
            )
        )
        df_test_simulated_at_obs_time["num_times_patient_xfer_to_or_table_in_case"] = (
            self.simulate_known_event_counts_at_inference(
                df=df_test_simulated_at_obs_time,
                first_timestamp_column="first_patient_xfer_to_or_table_datetime_local",
                second_timestamp_column="second_patient_xfer_to_or_table_datetime_local",
            )
        )

        return df_test_simulated_at_obs_time

    @staticmethod
    def simulate_known_event_counts_at_inference(
        df: pd.DataFrame, first_timestamp_column: str, second_timestamp_column: str
    ) -> np.ndarray[Any, Any]:
        # for number of events of the same type
        # rely on the fact that above, we have replace timestamps with null if they haven't happened yet
        known_event_counts = np.where(
            df[
                first_timestamp_column
            ].isnull(),  # if the first event hasn't happened, replace with zero
            0,
            np.where(
                (df[second_timestamp_column].isnull() & ~df[first_timestamp_column].isnull()),
                1,  # if the first event has happened but not the second, replace with one
                2,  # if both have happened, replace with 2
            ),
        )
        return known_event_counts

    def calculate_coverage_data(
        self,
        df_test_with_predictions: pd.DataFrame,
        coverage_at_obs_time_minutes_before_wheels_out: int,
    ) -> pd.DataFrame:
        """
        df_test_with_predictions: contains the predictions made at each one-minute increment during the case,
                including cases where we resorted to a heuristic
        coverage_at_obs_time_minutes_before_wheels_out: the number of minutes before wheels out to calculate coverage.
                this needs to be specified because coverage changes over time within a case (it could start out being
                predicted, but then a duplicate detected halfway)
        """

        # assign predictions as cleanest case (i.e. no heuristics were applied) or predicted case
        # (i.e. prediction was attempted, but maybe detected a duplicate event or missing event)
        df_test_with_predictions["is_cleanest_case"] = 1 * (
            df_test_with_predictions["prediction_tag"] == "dynamic_end_prediction"
        )
        # "predicted case" is a case where we attempted to predict at least part of the case
        # before possibly detecting a duplicate or suspected missing event
        df_test_with_predictions["is_predicted_case"] = 1 * (
            ~df_test_with_predictions["prediction_tag"].isin(
                [
                    # when prediction_tag is one of these, we don't attempt to predict for the case at any point
                    "surgeon_proc_combo_num_not_in_training",
                    "procedure_historically_had_duplicate_events",
                ]
            )
        )

        df_coverage_relative_to_actual_wheels_out = (
            df_test_with_predictions[
                df_test_with_predictions["obs_time_minutes_before_wheels_out"]
                == coverage_at_obs_time_minutes_before_wheels_out
            ]
            .groupby(["site_id", "obs_time_minutes_before_wheels_out"])
            .agg(
                {
                    "case_id": "count",
                    "is_cleanest_case": "sum",
                    "is_predicted_case": "sum",
                }
            )
            .reset_index()
        )

        df_coverage_relative_to_actual_wheels_out = (
            df_coverage_relative_to_actual_wheels_out.rename(
                columns={
                    "case_id": "num_all_cases",
                    "is_cleanest_case": "num_cleanest_cases",
                    "is_predicted_case": "num_predicted_cases",
                }
            )
        )

        # NOTE: "dropped bc duplicate" means that there were cases in training data indicating this test case will also have duplicates
        #       they are NOT dropped because we actually observed duplicates in the test case
        df_coverage_relative_to_actual_wheels_out["pct_cleanest_cases"] = (
            100
            * df_coverage_relative_to_actual_wheels_out["num_cleanest_cases"]
            / df_coverage_relative_to_actual_wheels_out["num_all_cases"]
        )
        df_coverage_relative_to_actual_wheels_out["pct_predicted_cases"] = (
            100
            * df_coverage_relative_to_actual_wheels_out["num_predicted_cases"]
            / df_coverage_relative_to_actual_wheels_out["num_all_cases"]
        )

        df_coverage_long = pd.melt(
            df_coverage_relative_to_actual_wheels_out,
            id_vars=["site_id", "obs_time_minutes_before_wheels_out"],
            value_vars=[
                "pct_cleanest_cases",
                "pct_predicted_cases",
            ],
            var_name="coverage_type",
            value_name="percent_coverage",
        )

        return df_coverage_long

    def plot_coverage_evaluation_metrics(
        self,
        df_test_with_predictions: pd.DataFrame,
        coverage_at_obs_time_minutes_before_wheels_out: int,
    ) -> None:
        """
        df_test_with_predictions: contains the predictions made at each one-minute increment during the case,
            including cases where we resorted to a heuristic
        coverage_at_obs_time_minutes_before_wheels_out: the number of minutes before wheels out to calculate coverage.
            this needs to be specified because coverage changes over time within a case (it could start out being
            predicted, but then a duplicate detected halfway)
        """

        df_coverage_long = self.calculate_coverage_data(
            df_test_with_predictions, coverage_at_obs_time_minutes_before_wheels_out
        )

        sns.lineplot(
            # filtered to a single time point here, but should be the same across all time points
            data=df_coverage_long,
            x="site_id",
            y="percent_coverage",
            hue="coverage_type",
        )
        plt.xticks(rotation=90)
        plt.ylim([0, 101])
        self.reporter.add_figure_to_report(
            figure_title="Case Coverage in Test "
            + str(coverage_at_obs_time_minutes_before_wheels_out)
            + " minutes before wheels-out",
            figure=plt,
        )
        plt.close()

        df_prediction_tag_counts = (
            df_test_with_predictions.groupby(["site_id", "prediction_tag"])
            .count()
            .reset_index()[["site_id", "prediction_tag", "case_id"]]
            .rename(columns={"case_id": "num_predictions"})
        )

        df_prediction_tag_counts_pivoted = df_prediction_tag_counts.pivot(
            index="site_id", columns="prediction_tag", values="num_predictions"
        )

        df_prediction_tag_counts_pivoted["total_ct"] = df_prediction_tag_counts_pivoted.sum(axis=1)

        prediction_tags = [
            col for col in df_prediction_tag_counts_pivoted.columns if col != "site_id"
        ]

        for col in prediction_tags:
            df_prediction_tag_counts_pivoted[col + "_pct"] = (
                100
                * df_prediction_tag_counts_pivoted[col]
                / df_prediction_tag_counts_pivoted["total_ct"]
            )

        self.reporter.report_dataframe_as_table(
            df_prediction_tag_counts_pivoted[
                [col + "_pct" for col in prediction_tags if "total" not in col]
            ],
            report_group="Validation",
            table_name="Percent Prediction Types per Site",
        )

        df_prediction_tag_counts_pivoted[
            [col + "_pct" for col in prediction_tags if "total" not in col]
        ].plot(kind="bar", stacked=True)
        plt.legend(bbox_to_anchor=(1.05, 1.0), loc="upper left")
        self.reporter.add_figure_to_report(
            figure_title="Percent Prediction Types per Site",
            figure=plt,
        )
        plt.close()

    def plot_scalars_comparing_pythia_to_static(self, df_maes: pd.DataFrame) -> None:
        df_maes["static_minus_pythia_error"] = (
            df_maes["static_abs_prediction_error"] - df_maes["abs_prediction_error"]
        )

        df_maes_20min_wait = df_maes[
            df_maes["obs_time_minutes_to_forecasted_wheels_out_category"] == 20
        ]
        df_maes_60min_wait = df_maes[
            df_maes["obs_time_minutes_to_forecasted_wheels_out_category"] == 60
        ]

        for location in df_maes_20min_wait[
            self.evaluation_config.location_type_to_evaluate
        ].unique():
            self.reporter.clearml_task.get_logger().report_single_value(
                name=f"ΔMAE at 20 min predicted wait time - {location}",
                value=df_maes_20min_wait.loc[
                    df_maes_20min_wait[self.evaluation_config.location_type_to_evaluate]
                    == location,
                    "static_minus_pythia_error",
                ].values[0],
            )

        for location in df_maes_60min_wait[
            self.evaluation_config.location_type_to_evaluate
        ].unique():
            self.reporter.clearml_task.get_logger().report_single_value(
                name=f"ΔMAE at 60 min predicted wait time - {location}",
                value=df_maes_60min_wait.loc[
                    df_maes_60min_wait[self.evaluation_config.location_type_to_evaluate]
                    == location,
                    "static_minus_pythia_error",
                ].values[0],
            )

    def plot_error_evaluation_metrics(
        self,
        df_eval_metrics: pd.DataFrame,
        obs_time_vs_other_time_colname: str,
        title_name: str,
        max_x_axis: int,
        minute_increments: int = 1,  # TODO: this is hard-coded, explore turning into config
    ) -> None:
        df_eval_metrics = df_eval_metrics[
            df_eval_metrics[obs_time_vs_other_time_colname] < max_x_axis
        ]

        # mean error plot
        sns.lineplot(
            data=df_eval_metrics,
            x=obs_time_vs_other_time_colname,
            y="prediction_error",
            hue="site_id",
        )
        sns.scatterplot(
            data=df_eval_metrics,
            x=obs_time_vs_other_time_colname,
            y="prediction_error",
            hue="site_id",
        )
        plt.legend(bbox_to_anchor=(1.05, 1), loc=2, borderaxespad=0.0)
        plt.axhline(y=0, color="k")
        self.reporter.add_figure_to_report(
            figure_title="Mean Error tested on "
            + str(minute_increments)
            + " minute increments \n"
            + title_name,
            figure=plt,
        )
        plt.close()

        # mean absolute error plot
        sns.lineplot(
            data=df_eval_metrics,
            x=obs_time_vs_other_time_colname,
            y="abs_prediction_error",
            hue="site_id",
        )
        plt.legend(bbox_to_anchor=(1.05, 1), loc=2, borderaxespad=0.0)
        plt.axhline(y=0, color="k")
        self.reporter.add_figure_to_report(
            figure_title="MAE tested on "
            + str(minute_increments)
            + " minute increments \n"
            + title_name,
            figure=plt,
        )
        plt.close()

    def pythia_evaluation_schema(self) -> list[SchemaField]:
        return [
            SchemaField("case_id", "STRING", mode="REQUIRED"),
            SchemaField("site_id", "STRING", mode="REQUIRED"),
            SchemaField("actual_duration", "FLOAT", mode="REQUIRED"),
            SchemaField("scheduled_duration", "FLOAT", mode="REQUIRED"),
            SchemaField("static_forecasted_duration", "FLOAT", mode="REQUIRED"),
            SchemaField("predicted_total_case_duration", "FLOAT", mode="REQUIRED"),
            SchemaField("prediction_tag", "STRING", mode="REQUIRED"),
            SchemaField("predicted_wait_minutes", "FLOAT", mode="REQUIRED"),
            SchemaField("static_predicted_wait_minutes", "FLOAT", mode="REQUIRED"),
            SchemaField("predicted_end_datetime_local", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("static_predicted_end_datetime_local", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("prediction_error", "FLOAT", mode="REQUIRED"),
            SchemaField("abs_prediction_error", "FLOAT", mode="REQUIRED"),
            SchemaField("static_prediction_error", "FLOAT", mode="REQUIRED"),
            SchemaField("static_abs_prediction_error", "FLOAT", mode="REQUIRED"),
            SchemaField("abs_percent_prediction_error", "FLOAT", mode="REQUIRED"),
            SchemaField("obs_time_minutes_to_forecasted_wheels_out", "FLOAT", mode="REQUIRED"),
            SchemaField(
                "obs_time_minutes_to_forecasted_wheels_out_category", "INTEGER", mode="REQUIRED"
            ),
            SchemaField("obs_time_minutes_before_wheels_out", "INTEGER", mode="REQUIRED"),
            SchemaField("obs_time", "TIMESTAMP", mode="REQUIRED"),
            SchemaField(
                "first_patient_xfer_to_or_table_datetime_local", "TIMESTAMP", mode="NULLABLE"
            ),
            SchemaField("first_patient_draped_datetime_local", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("first_patient_undraped_datetime_local", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("first_patient_xfer_to_bed_datetime_local", "TIMESTAMP", mode="NULLABLE"),
            SchemaField("training_run_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("ds", "DATE", mode="REQUIRED"),
            SchemaField("model_identifier", "STRING", mode="REQUIRED"),
        ]

    def upload_evaluation_data(self, bq_client: BQClient, df: pd.DataFrame) -> None:
        table_id = "prod-data-platform-027529.case_forecasting.pythia_training_results"
        table = Table(
            table_id,
            schema=self.pythia_evaluation_schema(),
        )
        table.time_partitioning = TimePartitioning(
            type_=TimePartitioningType.DAY,
            field="ds",
        )
        table = bq_client.create_table(table, exists_ok=True, retry=Retry(deadline=60))
        bq_client.insert_rows_from_dataframe(table, df)
