from typing import Literal

from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class DataSelectionConfig(BaseModel):
    min_date: str = "2022-09-01"
    max_date: str = "2025-05-14"


class DynamicModelConfig(BaseModel):
    # the maximum pct of cases within (first_primary_surgeon, first_primary_procedure, procedure_count) allowed before
    # we save it in a list of high-duplicate procedure types to avoid predicting on
    pct_duplicate_cutoff: int = 25

    # we only allow the running duration of a phase to be e.g. 3x the duration of the
    # median/mean found in the training data
    max_factor_allowed_for_running_duration: int = 3

    # used to calculate a feature for the phase ml model
    # to calculate this feature, split cases by scheduled duration and site, and find
    # the percent of actual total duration taken up by each phase
    scheduled_duration_bins: list[int] = [0, 60, 180, 270, 5000]
    scheduled_duration_bin_labels: list[str] = ["0-60", "61-180", "181-270", "271-5000"]

    # each phase is predicted by an ml model. these are the features used in that ml model
    # note that the phase predicted by the ml model is not necessarily the final prediction;
    # it could be replaced by the actual duration or running duration
    # NOTE: if changing these features, need to also change the catboost model pipeline indicating which
    # features are categorical
    num_features: list[str] = [
        "static_forecasted_duration",
        "static_minus_scheduled_duration",
        "median_phase_by_surgeon_proc_num_procs",  # this is a placeholder for phase-specific name
        "pct_phase_in_total_duration",  # this is a placeholder for a phase-specific name
        "procedure_count",
    ]
    cat_features: list[str] = [
        "static_forecasted_duration_is_naive",
        "first_primary_surgeon",
        "first_primary_procedure",
    ]


class EvaluationConfig(BaseModel):
    location_type_to_evaluate: Literal["site_id", "org_id"] = "site_id"  # site_id or org_id

    # note that if there isn't enough data to fairly evaluate on a new site, it will not be included in evaluation
    evaluation_to_fit_mapping: dict[str, str] = {
        "HMH-OPC18": "houston_methodist",
        "HMH-OPC19": "houston_methodist",
        "HMH-WT03": "houston_methodist",
        "HMH-DUNN03": "houston_methodist",
        "HMH-DUNN06": "houston_methodist",
        "HMH-MAIN03": "houston_methodist",
        "HMH-HMBA": "houston_methodist",
        "HMH-LD06": "houston_methodist",
        "HMH-HMW-LD": "houston_methodist",
        "HMH-HMW-OR": "houston_methodist",
        "HMH-HMBT-ASC": "houston_methodist",
        "HMH-HMBT-LD": "houston_methodist",
        "HMH-HMBT-OR": "houston_methodist",
        "HMH-HMCL-ASC": "houston_methodist",
        "HMH-HMCL-CBC": "houston_methodist",
        "HMH-HMCL-OR": "houston_methodist",
        "HMH-HMCY-CATH": "houston_methodist",
        "HMH-HMCY-ENDO": "houston_methodist",
        "HMH-HMCY-IR": "houston_methodist",
        "HMH-HMCY-LD": "houston_methodist",
        "HMH-HMCY-OR": "houston_methodist",
        "HMH-HMSL-LD": "houston_methodist",
        "HMH-HMSL-OR": "houston_methodist",
        "HMH-HMTW": "houston_methodist",
        "HMH-HMWB-C": "houston_methodist",
        "HMH-HMWB-N": "houston_methodist",
        "HMH-HMWB-S": "houston_methodist",
        "TGH-MAIN02": "tampa_general",
        "TGH-CVTOR03": "tampa_general",
        "HF-VH02": "health_first",
        "nb_medical_center_fairfield": "north_bay",
        "NYU-LI4": "nyu",
        "NYU-KP4": "nyu",
        "NYU-KP5": "nyu",
        "NYU-KP5-CATH": "nyu",
        "LBHS-GOR": "lifebridge",
        # "LBHS-RIAO": "lifebridge"
    }


class TrainingConfig(BaseModel):
    location_type_to_fit: Literal["site_id", "org_id"] = "org_id"  # site_id or org_id

    # Define aggregation type to find phase durations in training data
    # We will aggregate by (first_primary_surgeon, first_primary_procedure).
    aggregation_type: Literal["median", "mean"] = "median"  # 'median' or 'mean'

    locations_to_fit: list[str] = [
        "houston_methodist",
        "tampa_general",
        "north_bay",
        "health_first",
        "nyu",
        "lifebridge",
    ]

    # defines train/test split
    test_on_most_recent_x_days: int = 7
    # baseline_training_days is the minimum number of days in training set before the test set is reduced;
    # for established sites, there will be much more training data
    # if there isn't enough data in the full dataset to achieve the baseline_training_days and the test set,
    # split the data into 75% for training and 25% for testing. So, there still needs to be at least 4 days of data at
    # a new site to achieve a train-test split
    baseline_training_days: int = 20


class ModelTrainingConfig(BaseModel):
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLBaseModel = ClearMLBaseModel(
        project_name="Dynamic Case End Model", task_name="Production Fit", tags={"version": "1"}
    )
    evaluation_config: EvaluationConfig = EvaluationConfig()
    model_identifier: str = "dynamic_case_end_model"

    # See https://docs.pydantic.dev/latest/api/config/#pydantic.config.ConfigDict.protected_namespaces
    dynamic_model_config: DynamicModelConfig = DynamicModelConfig()


config = ModelTrainingConfig()
