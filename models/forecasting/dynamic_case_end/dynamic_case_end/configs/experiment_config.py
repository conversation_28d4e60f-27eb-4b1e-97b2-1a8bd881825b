from dynamic_case_end.configs.prod_config import config

config = config.model_copy()

# Modify config params as needed to experiment
config.training_config.locations_to_fit = [
    "houston_methodist",
    "tampa_general",
    "north_bay",
    "health_first",
    "nyu",
    "lifebridge",
]

# note that if there isn't enough data to fairly evaluate on a new site, it will not be included in evaluation
config.evaluation_config.evaluation_to_fit_mapping = {
    "HMH-OPC18": "houston_methodist",
    # "HMH-OPC19": "houston_methodist",
    # "HMH-WT03": "houston_methodist",
    # "HMH-DUNN03": "houston_methodist",
    # "HMH-DUNN06": "houston_methodist",
    # "HMH-MAIN03": "houston_methodist",
    # "HMH-HMBA": "houston_methodist",
    # "HMH-LD06": "houston_methodist",
    # "HMH-HMW-LD": "houston_methodist",
    # "HMH-HMW-OR": "houston_methodist",
    # "HMH-HMBT-ASC": "houston_methodist",
    # "HMH-HMBT-LD": "houston_methodist",
    # "HMH-HMBT-OR": "houston_methodist",
    # "HMH-HMCL-ASC": "houston_methodist",
    # "HMH-HMCL-CBC": "houston_methodist",
    # "HMH-HMCL-OR": "houston_methodist",
    # "HMH-HMCY-CATH": "houston_methodist",
    # "HMH-HMCY-ENDO": "houston_methodist",
    # "HMH-HMCY-IR": "houston_methodist",
    # "HMH-HMCY-LD": "houston_methodist",
    # "HMH-HMCY-OR": "houston_methodist",
    # "HMH-HMSL-LD": "houston_methodist",
    # "HMH-HMSL-OR": "houston_methodist",
    # "HMH-HMTW": "houston_methodist",
    # "HMH-HMWB-C": "houston_methodist",
    # "HMH-HMWB-N": "houston_methodist",
    # "HMH-HMWB-S": "houston_methodist",
    "TGH-MAIN02": "tampa_general",
    "TGH-CVTOR03": "tampa_general",
    "HF-VH02": "health_first",
    "nb_medical_center_fairfield": "north_bay",
    "NYU-LI4": "nyu",
    "NYU-KP4": "nyu",
    "NYU-KP5": "nyu",
    "NYU-KP5-CATH": "nyu",
    "LBHS-GOR": "lifebridge",
    # "LBHS-RIAO": "lifebridge",
}


config.training_config.test_on_most_recent_x_days = 2
config.training_config.baseline_training_days = 20
config.clearml_config.task_name = "Experiment Training - predict when only xfer table and bed"
config.model_identifier = "dynamic_case_end_model_expt_20250513"
