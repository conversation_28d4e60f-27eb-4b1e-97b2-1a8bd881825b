# Bayesian Case Duration Client Examples

This directory contains examples of how to use the Bayesian Case Duration Client.

## client_usage.py

This example demonstrates how to use the OpenAPI-generated client to interact with the Bayesian Case Duration Service.

To run the example:

1. Make sure the Bayesian Case Duration Service is running locally on port 9980
2. Generate the client:
   ```bash
   make generate-openapi-client
   ```
3. Install the client:
   ```bash
   pip install -e .
   ```
4. Run the example:
   ```bash
   python examples/client_usage.py
   ```

## client_usage_wrapper.py

This example demonstrates how to use the wrapper for the OpenAPI-generated client with shorter method names.

To run the example:

1. Make sure the Bayesian Case Duration Service is running locally on port 9980
2. Generate the client:
   ```bash
   make generate-openapi-client
   ```
3. Install the client:
   ```bash
   pip install -e .
   ```
4. Run the example:
   ```bash
   python examples/client_usage_wrapper.py
   ```

## Client Wrapper

The `client_wrapper.py` module provides a wrapper for the OpenAPI-generated client with shorter method names:

```python
from client_wrapper import BayesianCaseDurationClient

# Create the client
client = BayesianCaseDurationClient(host="http://localhost:9980")

# Check if the service is healthy
client.health_check()

# Get version information
version_info = client.get_version()

# Get model version
model_version = client.get_model_version()

# Predict median case duration
result = client.predict_median(case_id="case-123")

# Sample case duration distribution
result = client.predict_samples(case_id="case-123")

# Predict case duration for a scheduled case
result = client.predict_case(case_id="case-123")

# Predict case duration for multiple scheduled cases
result = client.predict_many_cases(case_ids=["case-123", "case-456"])
```

## Available Endpoints

The client provides access to the following endpoints:

### Health Endpoints
| Original Method | Wrapper Method | Description |
|-----------------|----------------|-------------|
| `livez_livez_get()` | `health_check()` | Check if the service is running |
| `readyz_readyz_get()` | N/A | Check if the service is ready to handle requests |

### Metadata Endpoints
| Original Method | Wrapper Method | Description |
|-----------------|----------------|-------------|
| `version_info_version_info_get()` | `get_version()` | Get version information about the service |
| `model_version_deployed_model_version_deployed_get()` | `get_model_version()` | Get the version of the deployed model |

### Prediction Endpoints
| Original Method | Wrapper Method | Description |
|-----------------|----------------|-------------|
| `predict_v4_standalone_predict_post(request)` | `predict_median(case_id)` | Predict case duration (median) for a standalone case |
| `predict_array_v4_standalone_sample_post(request)` | `predict_samples(case_id)` | Sample case duration distribution for a standalone case |
| `predict_case_v4_schedule_case_sample_post(request)` | `predict_case(case_id)` | Sample case duration for a scheduled case |
| `predict_many_cases_v4_schedule_case_sample_many_post(requests)` | `predict_many_cases(case_ids)` | Sample case duration for multiple scheduled cases |