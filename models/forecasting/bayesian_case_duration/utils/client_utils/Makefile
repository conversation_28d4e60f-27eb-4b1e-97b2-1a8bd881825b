# Makefile for client utilities

.PHONY: test lint

test:
	@echo "Running tests for client utilities"
	python test_client.py

lint:
	@echo "Running linting checks"
	# Check Python syntax in current directory
	python -m py_compile *.py
	# Check Python syntax in forecast_combiner_client directory
	if [ -d "forecast_combiner_client" ]; then \
		find forecast_combiner_client -name "*.py" -exec python -m py_compile {} \; ; \
	fi
	@echo "All linting checks passed!"
