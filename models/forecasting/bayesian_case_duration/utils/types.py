from typing import List

from pydantic import BaseModel, Field, RootModel
from serving_utils.utils import VersionedModel

from bayesian_case_duration.types import APIInputs


class APICaseInputs(BaseModel):
    case_id: str


class ManyCaseInputs(RootModel[list[APICaseInputs]]):
    def to_list(self) -> list[APICaseInputs]:
        return [x for x in self.root]


class APICaseRequest(BaseModel):
    # This API Input is used by the forecast combiner to simplify calls to it, and enable
    # better siloing of responsibilities, this means that the service only requires the case_id
    # rather than requiring all the features for prediction
    inputs: APICaseInputs


class ManyAPICaseRequests(BaseModel):
    inputs: list[APICaseInputs]

    def to_list(self) -> list[APICaseInputs]:
        return self.inputs


class PredictedOutput(BaseModel):
    prediction: float


class PredictedArrayOutput(BaseModel):
    prediction: List[float]


class PredictedCaseOutput(VersionedModel):
    prediction: list[float] | None
    prediction_median: float | None
    case_id: str
    feature_timestamps: dict[str, float] = Field(default={})
    cache_hit: bool = Field(default=False)


class ManyCaseOutputs(RootModel[list[PredictedCaseOutput]]):
    pass


class LegacyAPIRequest(BaseModel):
    case_features: APIInputs
