import logging
import time
from datetime import datetime, timedelta, timezone

import pandas as pd
import schedule
from google.cloud.bigquery import Client as B<PERSON><PERSON><PERSON>
from google.cloud.bigquery import QueryJobConfig, ScalarQueryParameter
from serving_utils import config
from serving_utils.setup_json_logger import setup_json_logger

from bayesian_case_duration.types import APIInputs
from dependencies.dependency_provider import container as AppContainer
from dependencies.services.base_bayesian_case_duration_service import (
    BaseBayesianCaseDurationService,
)


class StandaloneCachePrePopulator:
    model_service: BaseBayesianCaseDurationService
    bq_client: BQClient

    def __init__(self) -> None:
        self.logger = setup_json_logger(logger_name="StandaloneCachePrePopulator")
        self.bq_client = BQClient()

        # Initialize services
        self.model_service = AppContainer.base_bayesian_case_duration_service()

    def get_unique_combinations(self) -> pd.DataFrame:
        """Query BigQuery to get unique combinations of surgeon, procedure, site, and org."""
        four_month_ago = (datetime.now(tz=timezone.utc) - timedelta(days=120)).date()
        query_job_config = QueryJobConfig(
            query_parameters=[ScalarQueryParameter("four_month_ago", "DATE", four_month_ago)]
        )
        query = """
        SELECT DISTINCT
            first_primary_surgeon,
            first_primary_procedure,
            site_id,
            org_id
        FROM `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
        WHERE first_primary_surgeon IS NOT NULL
            AND first_primary_procedure IS NOT NULL
            AND site_id IS NOT NULL
            AND org_id IS NOT NULL
            AND date_of_surgery >= @four_month_ago
        """
        return self.bq_client.query(query, job_config=query_job_config).to_dataframe()

    def pre_populate_cache(
        self,
    ) -> None:
        """Pre-populate the cache with predictions for unique combinations."""
        # Get unique combinations
        unique_combinations = self.get_unique_combinations()
        self.logger.info(f"Found {len(unique_combinations)} unique combinations")

        # Convert to APIInputs format
        api_inputs = [
            APIInputs(
                org_id=row["org_id"],
                site_id=row["site_id"],
                first_primary_surgeon=row["first_primary_surgeon"],
                first_primary_procedure=row["first_primary_procedure"],
            )
            for _, row in unique_combinations.iterrows()
        ]
        self.logger.info(f"Generating predictions for {len(api_inputs)} combinations")

        # Generate predictions using ModelService
        for request in api_inputs:
            self.model_service.generate_predictions(request)

        self.logger.info(
            f"Successfully generated and saved predictions for {len(api_inputs)} combinations"
        )

    def start_process(self) -> None:
        """Start the process of pre-populating the cache."""
        logging.info("Starting StandaloneCachePrePopulator...")
        schedule.every(config.get_pre_populator_schedule_interval()).seconds.do(
            self.pre_populate_cache
        )
        while True:
            schedule.run_pending()
            time.sleep(1)
