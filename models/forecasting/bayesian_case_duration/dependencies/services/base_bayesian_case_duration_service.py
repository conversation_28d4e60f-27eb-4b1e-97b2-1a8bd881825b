from abc import ABC, abstractmethod
from logging import Logger

from serving_utils.utils import get_service_version

from bayesian_case_duration.types import APIInputs
from dependencies.results_store.base_results_store import BaseResultsStore
from utils.types import ManyAPICaseRequests, ManyCaseOutputs


class BaseBayesianCaseDurationService(ABC):
    logger: Logger
    results_store: BaseResultsStore
    model_identifier: str = "Dev"

    def __init__(self, logger: Logger, results_store: BaseResultsStore) -> None:
        self.logger = logger

        self.results_store = results_store

    @abstractmethod
    def readyz(self) -> bool:
        """
        Check if the service is ready to serve requests.
        """
        pass

    @abstractmethod
    def predict_many_cases(
        self, requests: ManyAPICaseRequests, run_id: str | None = None
    ) -> ManyCaseOutputs:
        """
        Predicts the case durations for multiple cases.
        """
        pass

    @abstractmethod
    def generate_predictions(self, inputs: APIInputs) -> tuple[float, list[float]]:
        pass

    def model_version_deployed(self) -> str:
        return self.model_identifier

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": "naive_bayesian_case_duration_model",
        }
