from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER

from bayesian_case_duration.types import APIInputs
from dependencies.services.base_bayesian_case_duration_service import (
    BaseBayesianCaseDurationService,
)
from utils.types import ManyAPICaseRequests, ManyCaseOutputs, PredictedCaseOutput


class DevService(BaseBayesianCaseDurationService):
    def generate_predictions(self, inputs: APIInputs) -> tuple[float, list[float]]:
        return 30, [20, 30, 40]

    def readyz(self) -> bool:
        """
        Check if the service is ready to serve requests.
        """
        return True

    def predict_many_cases(
        self, requests: ManyAPICaseRequests, run_id: str | None = None
    ) -> ManyCaseOutputs:
        requested_case_ids = [input_row.case_id for input_row in requests.inputs]
        self.logger.info(
            "Running in dev stub mode. Not loading models.",
            extra={
                "requested_case_ids": requested_case_ids,
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
            },
        )
        results = [
            PredictedCaseOutput(
                prediction=[27.2],
                prediction_median=None,
                case_id=input_row.case_id,
                **self.version_info(),  # type: ignore
            )
            for input_row in requests.inputs
        ]
        self.results_store.upsert_forecasts(results)
        return ManyCaseOutputs(results)
