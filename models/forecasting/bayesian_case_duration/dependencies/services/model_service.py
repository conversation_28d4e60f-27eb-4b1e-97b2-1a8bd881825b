import os
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from logging import Logger

import numpy as np
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from google.cloud.bigquery import Client as BQClient
from google.cloud.bigtable import Client as BTClient
from google.cloud.storage import Client as GCSClient
from serving_utils import config
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from training_utils.model_protocol import BayesianModelProtocol
from training_utils.model_storage import ModelStorage

from bayesian_case_duration.features import get_features_for_cases
from bayesian_case_duration.persistence_cache import (
    PREDICTION_FIELD,
    PREDICTION_LIST_FIELD,
    SCHEDULE_TTL,
    PersistenceCache,
)
from bayesian_case_duration.types import APIInputs
from dependencies.results_store.base_results_store import BaseResultsStore
from dependencies.services.base_bayesian_case_duration_service import (
    BaseBayesianCaseDurationService,
)
from utils.types import ManyAPICaseRequests, ManyCaseOutputs, PredictedCaseOutput


class ModelService(BaseBayesianCaseDurationService):
    model: BayesianModelProtocol
    model_executor: ThreadPoolExecutor
    feature_store: FeatureStore[CaseSchema]

    def __init__(self, logger: Logger, results_store: BaseResultsStore) -> None:
        super().__init__(logger, results_store)
        self.logger.info("Starting Service")
        # This where it finds the model to download
        gcs_client = GCSClient()
        model_storage = ModelStorage(
            gcs_client,
            model_type="bayesian_case_duration_model",
            model_identifier="automatic_production_training/2025-05-16",
        )

        # Load the cache on startup
        self.bq_client = BQClient(project=config.get_gcp_project_id())

        self.logger.info("Getting Models")
        if model_storage.models_downloaded is False:
            model_storage._download_from_google_storage()
        self.model = model_storage.load_model("cloudpickle")
        self.logger.info("Got Models")
        self.model_executor = ThreadPoolExecutor(
            max_workers=1, thread_name_prefix="prediction_worker"
        )
        self.model_identifier = model_storage.model_identifier
        self.logger.info("Setting up Feature Store")
        bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
        instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])

        self.feature_store = FeatureStore(instance, "cases", CaseSchema)

        self.logger.info("BayesianCaseDurationService initialized")
        self.persistence_cache = PersistenceCache(prefix=f"bayesian/{self.model_identifier}")

    def generate_predictions(self, inputs: APIInputs) -> tuple[float, list[float]]:
        """
        Generates a list of predictions. Each number in the list is a single prediction
        that was sampled from the posterior predictive distribution of the Bayesian model.
        These can be visualized as histogram, summarized as probabilities or in other
        ways downstream.
        """
        cached_prediction = self.persistence_cache.get_standalone_prediction(inputs)
        if cached_prediction is None:
            inference_dict = inputs.model_dump()
            # Add the procedure count to the inputs, this should be calculated from the additional procedures to be consistent with the model
            inference_dict["procedure_count"] = len(inputs.additional_procedures) + 1
            self.logger.info(
                f"Generating prediction for ({inputs.org_id}, {inputs.first_primary_surgeon}, {inputs.first_primary_procedure})",
                extra={
                    "api_inputs": inputs.model_dump(),
                    "cache_hit": False,
                },
            )
            y_hat_list_future = self.model_executor.submit(
                self.model.predict_from_inference_dict,
                inference_dict,
            )
            y_hat_list = y_hat_list_future.result()
            y_hat_median = np.median(np.array(y_hat_list)).item()
            self.persistence_cache.set_standalone_prediction(inputs, y_hat_median, y_hat_list)
        else:
            self.logger.info(
                f"Retrieving cached prediction for ({inputs.org_id}, {inputs.first_primary_surgeon}, {inputs.first_primary_procedure})",
                extra={
                    "api_inputs": inputs.model_dump(),
                    "cache_hit": True,
                },
            )
            y_hat_median, y_hat_list = cached_prediction

        return y_hat_median, y_hat_list

    def predict_many_cases(
        self, requests: ManyAPICaseRequests, run_id: str | None = None
    ) -> ManyCaseOutputs:
        requested_case_ids = [input_row.case_id for input_row in requests.inputs]

        requested_case_ids_set = set(requested_case_ids)
        self.logger.info(
            f"Predicting for {len(requested_case_ids)} cases",
            extra={
                "requested_case_ids": requested_case_ids,
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
            },
        )

        features, bigtable_timestamps = get_features_for_cases(
            self.feature_store, requested_case_ids_set
        )

        if len(features) == 0:
            output_list = []

            for case_id in requested_case_ids_set:
                output_list.append(
                    PredictedCaseOutput(
                        prediction=None,
                        prediction_median=None,
                        case_id=case_id,
                        **self.version_info(),  # type: ignore
                    )
                )
            self.results_store.upsert_forecasts(output_list)

            return ManyCaseOutputs(output_list)

        feature_timestamps = bigtable_timestamps.to_dict(orient="index")
        missing_case_ids = requested_case_ids_set - set(features.index.to_list())

        # Check cache and get predictions for uncached cases
        cached_predictions, case_ids_to_predict = self.persistence_cache.get_many_predictions(
            features
        )
        cache_hit_case_ids = list(cached_predictions.keys())
        if len(cached_predictions) > 0:
            self.logger.info(
                f"Using cached prediction for {len(cached_predictions)} cases",
                extra={
                    "cache_hit_case_ids": cache_hit_case_ids,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )

        # Make predictions for cases not in cache
        if case_ids_to_predict:
            features_to_predict = features.loc[case_ids_to_predict]
            prediction_future = self.model_executor.submit(
                self.model.predict,
                features_to_predict,
            )
            predictions: np.typing.NDArray[np.float64] = prediction_future.result()
            features_to_predict["predictions"] = predictions.tolist()

            if len(predictions) != len(case_ids_to_predict):
                self.logger.error(
                    f"Mismatch between predictions length ({len(predictions)}) and case_ids_to_predict length ({len(case_ids_to_predict)})",
                    extra={
                        "features": features.index.to_list(),
                        "case_ids_to_predict": case_ids_to_predict,
                        X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                    },
                )
                raise ValueError(
                    f"Number of predictions {len(predictions)} does not match number of case ids {len(case_ids_to_predict)}"
                )

            # add some jitter to the cache expiration time so that we don't have
            # all the predictions expire at the same time and cause a cache stampede
            jitter_seconds = np.random.randint(-30, 30) * 60 * 60
            # Store new predictions in cache
            self.persistence_cache.set_many_predictions(
                features_to_predict, ttl=SCHEDULE_TTL + jitter_seconds
            )

            # Add new predictions to cached_predictions
            for case_id, prediction in zip(case_ids_to_predict, predictions):
                cached_predictions[case_id] = {
                    PREDICTION_LIST_FIELD: prediction.tolist(),
                    PREDICTION_FIELD: float(np.median(prediction)),
                }

        # Build output list using cached predictions
        output_list = []
        for case_id in features.index.to_list():
            output_list.append(
                PredictedCaseOutput(
                    prediction=cached_predictions[case_id][PREDICTION_LIST_FIELD],
                    prediction_median=cached_predictions[case_id][PREDICTION_FIELD],
                    case_id=case_id,
                    feature_timestamps=feature_timestamps[case_id],
                    cache_hit=case_id in cache_hit_case_ids,
                    **self.version_info(),
                )
            )

        for case_id in missing_case_ids:
            output_list.append(
                PredictedCaseOutput(
                    prediction=[],
                    prediction_median=None,
                    case_id=case_id,
                    **self.naive_version_info(),  # type: ignore
                )
            )

        self.results_store.upsert_forecasts(output_list)

        return ManyCaseOutputs(output_list)

    def readyz(self) -> bool:
        """Readiness probe that verifies model and device are properly configured."""
        return bool(self.model)
