import os

import pandas as pd
from feature_store import FeatureStore
from google.cloud.bigtable import Client as BTClient

from bayesian_case_duration.features import BayesianDurationResultSchema
from dependencies.results_store.base_results_store import BaseResultsStore
from utils.types import PredictedCaseOutput


class ResultsStore(BaseResultsStore):
    results_store: FeatureStore[BayesianDurationResultSchema]

    def __init__(self) -> None:
        bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
        instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])
        self.results_store = FeatureStore(
            instance, "results", BayesianDurationResultSchema, prefix="ml_forecast"
        )

    def upsert_forecasts(self, results: list[PredictedCaseOutput]) -> None:
        if self.results_store:
            results_df = pd.DataFrame(
                [
                    {
                        "bayesian_duration_forecast": result.prediction,
                        "bayesian_cache_hit": result.cache_hit,
                        "bayesian_duration_median_forecast": result.prediction_median,
                        "bayesian_duration_service_version": result.service_version,
                        "bayesian_duration_model_version": result.model_version,
                        "bayesian_duration_version": result.version,
                    }
                    for result in results
                ],
                index=[res.case_id for res in results],
            )
            self.results_store.store_features(results_df)  # type: ignore
