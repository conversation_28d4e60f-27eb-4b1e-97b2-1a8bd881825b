from dependency_injector import containers, providers
from serving_utils import config
from serving_utils.setup_json_logger import setup_json_logger

from dependencies.results_store.dummy_results_store import DummyResultsStore
from dependencies.results_store.results_store import ResultsStore
from dependencies.services.dev_service import DevService
from dependencies.services.model_service import ModelService

container = containers.DynamicContainer()
container.logger = providers.Singleton(
    setup_json_logger,
    logger_name="BayesianCaseDurationService",
)
if config.get_store_forecast_result():
    container.base_results_store = providers.Singleton(
        ResultsStore,
    )
else:
    container.base_results_store = providers.Singleton(
        DummyResultsStore,
    )

if config.is_dev_stub() or config.is_spec_generation():
    container.base_bayesian_case_duration_service = providers.Singleton(
        DevService,
        container.logger,
        container.base_results_store,
    )
else:
    container.base_bayesian_case_duration_service = providers.Singleton(
        ModelService,
        container.logger,
        container.base_results_store,
    )
