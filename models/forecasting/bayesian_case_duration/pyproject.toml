[build-system]
requires = [ "poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.mypy]
strict = true
explicit_package_bases = true

[[tool.mypy.overrides]]
module = [
    "clearml.*",
    "google.*",
    "joblib",
    "sklearn.*",
    "bayesian_case_duration_client.*",
    "fire.*",
]
ignore_missing_imports = true

[tool.poetry]
name = "bayesian_case_duration"
version = "0.1.0"
description = ""
authors = ["Apella Engineering <<EMAIL>>", "Data Science & Machine Learning <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
pandas = "^2.2.1"
google-cloud-storage = "^2.15.0"
google-cloud-secret-manager = "^2.18.2"
training-utils = {path = "../../../training_utils", develop = true}
pydantic = "^2.6.3"
cloudpickle = "^3.0.0"
dill = "^0.3.7"
pymc = "^5.20.0"
scikit-learn = "^1.6.1"
plotly-express = "^0.4.1"
seaborn = "^0.13.2"
redis = "^5.2.1"
feature-store = { path="../../../feature_store", develop = true }
dependency-injector = "^4.46.0"
fire = "^0.7.0"
schedule = "^1.2.2"

[tool.poetry.group.serving.dependencies]
serving-utils = {path = "../../../serving_utils", develop = true}

[tool.poetry.group.dev.dependencies]
isort = "^5.13.2"
ruff = "^0.4.0"
pytest = "^8.0.2"
pytest-cov = "^4.1.0"
mypy = "^1.8.0"
pandas-stubs = "^2.2.0.240218"

[tool.poetry.group.training.dependencies]
google-cloud-bigquery = "^3.18.0"
db-dtypes = "^1.2.0"
tenacity = "^8.2.3"
torch = "^2.6.0"

[[tool.poetry.source]]
name = "prod-python-registry"
url = "https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/simple/"
priority = "supplemental"

[tool.ruff]
line-length = 100

[tool.ruff.lint]
ignore = [
    # Trust ruff format to get line length right. Without this, there are cases where it won't
    # reflow a line that's too long (e.g. comments) and ruff check complains.
    "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
select = ["E", "F", "I001"]
exclude = [
    # Exclude files in the `bayesian_case_duration_client` directory
    "bayesian_case_duration_client",
]
