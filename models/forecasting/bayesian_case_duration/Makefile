SHELL := /bin/bash
PORT := 9980
PWD := $(shell pwd)


format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run mypy --exclude bayesian_case_duration_client .


test:
	poetry run python -m pytest -vv tests/

test-with-client: generate-openapi-client
	poetry run python -m pytest -vv tests/

test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=. tests | tee pytest-coverage.txt

run-training-locally:
	poetry run python -u -m bayesian_case_duration.training --no-gpu --train --evaluate --config-module-name bayesian_case_duration.configs.experiment_config

run-async-trainer:
	poetry run python -m training_utils.async_trainer --config-filename async_training_config.yml

dev-local:
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-local:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd GCP_PROJECT=prod-data-platform-027529 poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd GCP_PROJECT=prod-data-platform-027529 poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

save-dummy-model:
	echo "🔄 Generating and saving dummy model artifact..."
	mkdir -p tests/artifacts
	poetry run python scripts/save_dummy_model.py
	echo "✅ Successfully saved dummy model artifact to tests/artifacts/dummy_model.pkl"

generate-openapi-spec:
	SPEC_GENERATION=1 poetry run python -m scripts.generate_openapi_spec

generate-openapi-client: generate-openapi-spec
generate-openapi-client:
	docker run \
		-v $(PWD):/local openapitools/openapi-generator-cli generate \
		-i local/openapi-spec.json \
		-g python \
		-o local \
		--additional-properties=disallowAdditionalPropertiesIfNotPresent=false,packageName=bayesian_case_duration_client,useOneOfDiscriminatorLookup=true,usePydanticV2=true

generate-openapi-client-ci: generate-openapi-spec
generate-openapi-client-ci:
	openapi-generator-cli generate \
		-i openapi-spec.json \
		-g python \
		-o . \
		--additional-properties=disallowAdditionalPropertiesIfNotPresent=false,packageName=bayesian_case_duration_client,useOneOfDiscriminatorLookup=true,usePydanticV2=true

generate-client-for-ci: generate-openapi-client-ci
	rm -rf build_artifacts
	mkdir build_artifacts
	mv bayesian_case_duration_client build_artifacts/
	cp utils/client_utils/pyproject.toml build_artifacts/
	cp utils/client_utils/README.md build_artifacts/

pre-populate-standalone-cache:
	FEATURE_STORE_PROJECT=prod-data-platform-027529 FEATURE_STORE_INSTANCE=prod-general-ssd GCP_PROJECT=prod-data-platform-027529 poetry run python -m scripts.pre_populate_standalone_cache pre_populate_standalone_cache

.PHONY: format lint test test-cov run-training-locally run-async-trainer dev-local run-local run-uvicorn save-dummy-model generate-openapi-spec generate-openapi-client generate-mock-openapi-spec generate-openapi-client-ci generate-client-for-ci pre-populate-standalone-cache
