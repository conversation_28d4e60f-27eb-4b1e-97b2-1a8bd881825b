"""
Tests for the OpenAPI-generated Bayesian Case Duration Client.
This module contains unit tests for the OpenAPI-generated Bayesian Case Duration Client.
Note: These tests will be skipped if the client has not been generated yet.
"""

from unittest.mock import MagicMock, patch

import pytest

# Skip tests if the client has not been generated yet
# mypy: disable-error-code="import-not-found"
try:
    import os
    import sys

    sys.path.append(os.path.abspath("."))

    import bayesian_case_duration_client
    from bayesian_case_duration_client import APICaseInputs
    from bayesian_case_duration_client.api.health_api import HealthApi
    from bayesian_case_duration_client.api.metadata_api import MetadataApi

    # Import specific classes to avoid mypy errors
    from bayesian_case_duration_client.api.predict_api import PredictApi
    from bayesian_case_duration_client.api_client import ApiClient
    from bayesian_case_duration_client.configuration import Configuration
    from bayesian_case_duration_client.exceptions import ApiException
    from bayesian_case_duration_client.models.api_case_request import APICaseRequest
    from bayesian_case_duration_client.models.api_inputs import APIInputs
    from bayesian_case_duration_client.models.legacy_api_request import (
        LegacyAPIRequest,
    )

    CLIENT_GENERATED = True
except ImportError as e:
    print(f"Import error: {e}")
    # Define dummy classes for type checking when client is not generated
    Configuration = MagicMock
    ApiClient = MagicMock
    PredictApi = MagicMock
    HealthApi = MagicMock
    MetadataApi = MagicMock
    APIInputs = MagicMock
    APICaseRequest = MagicMock
    LegacyAPIRequest = MagicMock
    ApiException = Exception
    CLIENT_GENERATED = False


@pytest.mark.skipif(not CLIENT_GENERATED, reason="Client not generated yet")
class TestOpenAPIClient:
    """Tests for the OpenAPI-generated Bayesian Case Duration Client."""

    def setup_method(self) -> None:
        """Set up the test environment."""
        self.configuration = MagicMock()
        self.api_client = MagicMock()
        self.health_api = MagicMock()
        self.metadata_api = MagicMock()
        self.predict_api = MagicMock()

        with patch("bayesian_case_duration_client.ApiClient", return_value=self.api_client):
            with patch("bayesian_case_duration_client.HealthApi", return_value=self.health_api):
                with patch(
                    "bayesian_case_duration_client.MetadataApi", return_value=self.metadata_api
                ):
                    with patch(
                        "bayesian_case_duration_client.PredictApi",
                        return_value=self.predict_api,
                    ):
                        self.configuration = bayesian_case_duration_client.Configuration()
                        self.configuration.host = "http://localhost:9980"
                        self.api_client = bayesian_case_duration_client.ApiClient(
                            self.configuration
                        )
                        self.health_api = bayesian_case_duration_client.HealthApi(self.api_client)
                        self.metadata_api = bayesian_case_duration_client.MetadataApi(
                            self.api_client
                        )
                        self.predict_api = bayesian_case_duration_client.PredictApi(self.api_client)

    def test_health_check(self) -> None:
        """Test health check endpoint."""
        mock_response = MagicMock()
        self.health_api.livez_livez_get.return_value = mock_response

        response = self.health_api.livez_livez_get()

        self.health_api.livez_livez_get.assert_called_once()
        assert response == mock_response

    def test_get_version_info(self) -> None:
        """Test getting version info."""
        mock_response = MagicMock()
        self.metadata_api.version_info_version_info_get.return_value = mock_response

        response = self.metadata_api.version_info_version_info_get()

        self.metadata_api.version_info_version_info_get.assert_called_once()
        assert response == mock_response

    def test_predict_standalone(self) -> None:
        """Test predicting for a standalone case."""
        mock_response = MagicMock()
        self.predict_api.predict_v4_standalone_predict_post.return_value = mock_response

        api_inputs = APIInputs(
            first_primary_surgeon="test-case",
            first_primary_procedure="test-case",
            org_id="test-case",
        )
        api_request = LegacyAPIRequest(case_features=api_inputs)

        response = self.predict_api.predict_v4_standalone_predict_post(api_request)

        self.predict_api.predict_v4_standalone_predict_post.assert_called_once_with(api_request)
        assert response == mock_response

    def test_predict_case(self) -> None:
        """Test predicting for a scheduled case."""
        mock_response = MagicMock()
        self.predict_api.predict_case_v4_schedule_case_sample_post.return_value = mock_response

        api_inputs = APICaseInputs(case_id="test-case")
        api_request = APICaseRequest(inputs=api_inputs)

        response = self.predict_api.predict_case_v4_schedule_case_sample_post(api_request)

        self.predict_api.predict_case_v4_schedule_case_sample_post.assert_called_once_with(
            api_request
        )
        assert response == mock_response

    def test_api_exception(self) -> None:
        """Test handling API exceptions."""
        self.health_api.livez_livez_get.side_effect = ApiException(
            status=500, reason="Internal Server Error"
        )

        with pytest.raises(ApiException) as excinfo:
            self.health_api.livez_livez_get()

        assert excinfo.value.status == 500
        assert excinfo.value.reason == "Internal Server Error"
