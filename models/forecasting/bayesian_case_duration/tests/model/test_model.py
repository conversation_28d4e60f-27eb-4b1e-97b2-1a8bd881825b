from pathlib import Path
from unittest.mock import MagicMock

import numpy as np
import pandas as pd
import pytest
from clearml import Task
from training_utils.clearml_reporter import ClearMLReporter
from training_utils.model_storage import ModelStorage

from bayesian_case_duration.configs.prod_config import ModelTrainingConfig
from bayesian_case_duration.model.dummy_model import DummyBayesianCaseDurationModel
from bayesian_case_duration.training.utils import FitModel

MODEL_TYPE = "some_type_of_model"


def _generate_fake_data(N: int = 100) -> pd.DataFrame:
    """Generates fake data for the dummy model."""
    data = {
        "first_primary_procedure": [f"PROC_{i % 5}" for i in range(N)],
        "first_primary_surgeon": [f"SURGEON_{i % 3}" for i in range(N)],
        "actual_duration_log": np.random.normal(loc=np.log(60), scale=0.3, size=N),
    }
    return pd.DataFrame(data)


@pytest.fixture
def mocked_reporter() -> ClearMLReporter:
    Task.set_offline(True)
    clearml_task = Task.init(
        project_name="test_project",
        task_name="test_task",
        tags=["test_tag"],
    )
    reporter = ClearMLReporter({}, clearml_task)
    return reporter


@pytest.fixture
def dummy_model_fixture(mocked_reporter: ClearMLReporter) -> DummyBayesianCaseDurationModel:
    artifact_path = Path(__file__).parent.parent / "artifacts" / "dummy_model.pkl"

    dummy_model = DummyBayesianCaseDurationModel(
        df=_generate_fake_data(N=10), config=ModelTrainingConfig(), artifact_path=str(artifact_path)
    )

    if not dummy_model.idata:
        model_fitter = FitModel(dummy_model.model, mocked_reporter)
        idata = model_fitter.fit()
        dummy_model.idata = idata  # type: ignore[assignment]

    return dummy_model


def test_bayesian_model(
    dummy_model_fixture: DummyBayesianCaseDurationModel,
) -> None:
    test_df = _generate_fake_data(N=10)
    y_hat_test_1 = dummy_model_fixture.predict(test_df)
    y_hat_test_2 = dummy_model_fixture.predict(test_df)

    # this proves that model prediction is idempotent
    np.testing.assert_almost_equal(y_hat_test_1, y_hat_test_2)

    # now we should be able to save the model and load it and make sure predictions are the same
    gcs_client = MagicMock()
    serialization_package_name = "cloudpickle"
    model_storage = ModelStorage(gcs_client, MODEL_TYPE, "test_identifier")
    model_storage.rm_local_folder()

    model_storage.upload_to_google_storage(
        dummy_model_fixture,  # type: ignore[arg-type]
        serialization_package_name=serialization_package_name,
    )
    loaded_model = model_storage.load_model(serialization_package_name)

    y_hat_test_3 = loaded_model.predict(test_df)
    np.testing.assert_almost_equal(y_hat_test_1, y_hat_test_3)


def test_postprocess_prediction(dummy_model_fixture: DummyBayesianCaseDurationModel) -> None:
    """Tests the postprocess_prediction logic."""
    model = dummy_model_fixture
    n_samples = 500  # Standard number of samples in prediction output
    implicit_turnover = 30
    min_diff = 120
    min_sched_dur = 120

    # --- Test Case 1: No big disagreement ---
    y_hat_no_disagree = np.full((1, n_samples), 150.0)
    df_no_disagree = pd.DataFrame({"scheduled_duration": [150.0 + implicit_turnover]})
    result_no_disagree = model.postprocess_prediction(y_hat_no_disagree.copy(), df_no_disagree)
    np.testing.assert_array_equal(result_no_disagree, y_hat_no_disagree)

    # --- Test Case 2: Big disagreement, replace prediction ---
    y_hat_disagree = np.full((1, n_samples), 100.0)  # Median prediction = 100
    scheduled_duration_disagree = 300.0
    adjusted_scheduled_duration = scheduled_duration_disagree - implicit_turnover  # 270
    df_disagree = pd.DataFrame({"scheduled_duration": [scheduled_duration_disagree]})
    expected_disagree = np.full((1, n_samples), adjusted_scheduled_duration)
    result_disagree = model.postprocess_prediction(y_hat_disagree.copy(), df_disagree)
    np.testing.assert_array_equal(result_disagree, expected_disagree)

    # --- Test Case 3: Missing scheduled duration (should not disagree) ---
    y_hat_missing = np.full((1, n_samples), 180.0)  # Median = 180
    df_missing = pd.DataFrame({"scheduled_duration": [None]})
    # Expected: scheduled_duration becomes 180 + 30 = 210
    # adjusted_scheduled becomes 210 - 30 = 180. Difference is 0.
    result_missing = model.postprocess_prediction(y_hat_missing.copy(), df_missing)
    np.testing.assert_array_equal(result_missing, y_hat_missing)

    # --- Test Case 4: Difference just below threshold (no replacement) ---
    y_hat_edge_diff = np.full((1, n_samples), 100.0)  # Median = 100
    scheduled_duration_edge_diff = (
        100.0 + implicit_turnover + min_diff - 1
    )  # 100 + 30 + 120 - 1 = 249
    df_edge_diff = pd.DataFrame({"scheduled_duration": [scheduled_duration_edge_diff]})
    # adjusted_scheduled = 249 - 30 = 219. Difference = 119.
    result_edge_diff = model.postprocess_prediction(y_hat_edge_diff.copy(), df_edge_diff)
    np.testing.assert_array_equal(result_edge_diff, y_hat_edge_diff)

    # --- Test Case 5: Scheduled duration just below threshold (no replacement) ---
    y_hat_edge_dur = np.full((1, n_samples), 100.0)  # Median = 100
    scheduled_duration_edge_dur = min_sched_dur - 1  # 119
    df_edge_dur = pd.DataFrame({"scheduled_duration": [scheduled_duration_edge_dur]})
    # adjusted_scheduled = 119 - 30 = 89. Difference = 11.
    # Even if difference was > min_diff, scheduled_duration < min_sched_dur prevents replacement
    result_edge_dur = model.postprocess_prediction(y_hat_edge_dur.copy(), df_edge_dur)
    np.testing.assert_array_equal(result_edge_dur, y_hat_edge_dur)

    # --- Test Case 6: Multiple rows (mix of cases) ---
    y_hat_multi = np.array(
        [
            np.full(n_samples, 150.0),  # Case 1: No disagree (median=150, sched=180, adj=150)
            np.full(
                n_samples, 100.0
            ),  # Case 2: Disagree (median=100, sched=300, adj=270) -> replace with 270
            np.full(n_samples, 180.0),  # Case 3: Missing sched (median=180)
        ]
    )
    df_multi = pd.DataFrame(
        {"scheduled_duration": [150.0 + implicit_turnover, scheduled_duration_disagree, None]}
    )
    expected_multi = np.array(
        [
            np.full(n_samples, 150.0),
            np.full(n_samples, adjusted_scheduled_duration),  # Replaced
            np.full(n_samples, 180.0),
        ]
    )
    result_multi = model.postprocess_prediction(y_hat_multi.copy(), df_multi)
    np.testing.assert_array_equal(result_multi, expected_multi)
