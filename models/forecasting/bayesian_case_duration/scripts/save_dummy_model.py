import sys
from pathlib import Path

import arviz as az
import numpy as np
import pandas as pd

from bayesian_case_duration.configs.prod_config import ModelTrainingConfig
from bayesian_case_duration.model.dummy_model import DummyBayesianCaseDurationModel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def create_and_save_dummy_model() -> None:
    """Creates and saves a dummy model artifact for testing"""
    # Sample training data matching model expectations
    train_df = pd.DataFrame(
        {
            "first_primary_procedure": ["PROC_A", "PROC_B", "PROC_A"],
            "first_primary_surgeon": ["SURGEON_X", "SURGEON_Y", "SURGEON_X"],
            "actual_duration_log": np.log([45, 90, 50]),  # Log of durations in minutes
        }
    )

    artifact_path = project_root / "tests" / "artifacts" / "dummy_model.pkl"
    print(f"Preparing dummy model artifact for: {artifact_path}")

    # Instantiate the dummy model - it might try to load the artifact, which is fine
    # We'll overwrite its components before saving anyway.
    model = DummyBayesianCaseDurationModel(
        df=train_df,  # df is technically not used by __init__ anymore but kept for context
        config=ModelTrainingConfig(model_identifier="ci_test_dummy_model"),
        artifact_path=str(artifact_path),
    )

    # Create fake InferenceData - it doesn't need real data for the dummy model purpose
    fake_posterior = {
        "y_hat_log": np.random.randn(1, 10)  # Example shape: 1 chain, 10 draws
    }
    fake_idata = az.from_dict(posterior=fake_posterior)
    model.idata = fake_idata

    # Ensure coords and priors are populated (either from load or defaults)
    # The dummy __init__ handles this, but we could explicitly set if needed:
    # model.coords = {'site_id': ['APELLA_UNKNOWN']}
    # model.priors = {'num_scheduled_cases_priors': {'APELLA_UNKNOWN': 5}}

    print(f"Saving dummy model to {artifact_path}")
    model.save_to_local_file(str(artifact_path))


if __name__ == "__main__":
    create_and_save_dummy_model()
