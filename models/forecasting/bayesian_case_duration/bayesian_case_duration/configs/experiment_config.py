from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class TrainingConfig(BaseModel):
    test_on_most_recent_x_days: int = 15


class DataSelectionConfig(BaseModel):
    min_date_in_dataset: str = "2025-01-01"
    max_date_in_dataset: str = "2025-05-06"


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "Bayesian Case Duration Model"
    task_name: str = "Experiment Training - Refactor training code"
    tags: dict[str, str] = {"refactor": "True"}


class ModelTrainingConfig(BaseModel):
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "experiments/refactor_training_code"
    model_file_name: str = "model.pkl"
