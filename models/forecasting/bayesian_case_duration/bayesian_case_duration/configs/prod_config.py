from pydantic import BaseModel
from training_utils.clearml_reporter import ClearMLBaseModel


class TrainingConfig(BaseModel):
    test_on_most_recent_x_days: int = 30


class DataSelectionConfig(BaseModel):
    min_date_in_dataset: str = "2020-01-01"
    max_date_in_dataset: str = "2025-01-15"


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "Bayesian Case Duration Model"
    task_name: str = "Production Training"
    tags: dict[str, str] = {"label": "label"}


class ModelTrainingConfig(BaseModel):
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "20250208_bayesian_case_duration_model"
    model_file_name: str = "model.pkl"
