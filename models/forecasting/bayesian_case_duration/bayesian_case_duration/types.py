from typing import List

from pydantic import BaseModel, Field


class APIInputs(BaseModel):
    org_id: str = Field(
        description="Organization ID for the case",
    )
    first_primary_surgeon: str = Field(
        description="Primary surgeon for the case",
    )
    first_primary_procedure: str = Field(
        description="Primary procedure for the case",
    )
    site_id: str | None = Field(
        description="Site ID where the case is scheduled",
        default=None,
    )
    day_of_week: str | None = Field(
        description="Day of the week the case is scheduled for",
        default=None,
    )
    scheduled_starting_hour: str | None = Field(
        description="Hour of the day the case is scheduled to start",
        default=None,
    )
    outpatient: str | None = Field(
        description="Whether the case is outpatient or not",
        default=None,
    )
    last_case: str | None = Field(
        description="Whether the case is the last case of the day",
        default=None,
    )
    surgeon_count: int = Field(description="Number of surgeons in the case", default=1)
    add_on: str | None = Field(
        description="Whether the case is an add-on or not",
        default=None,
    )
    is_flip_room: str | None = Field(
        description="Whether the case is in a flip room or not",
        default=None,
    )
    case_type_short: str | None = Field(
        description="Shortened name of the case type",
        default=None,
    )
    num_scheduled_cases: int | None = Field(
        description="Number of scheduled cases for the day", default=None
    )
    to_follow_case: str | None = Field(
        description="Whether the case is a follow case or not",
        default=None,
    )
    additional_procedures: List[str] = Field(
        default_factory=list,
        description="List of additional procedures, this shouldn't include the first primary procedure",
    )
    scheduled_duration: int | None = Field(
        description="The scheduled duration. Used as fallback in cases of big disagreement between prediction and schedule. Not used as a feature in the model, but as a post-processing step",
        default=None,
    )
