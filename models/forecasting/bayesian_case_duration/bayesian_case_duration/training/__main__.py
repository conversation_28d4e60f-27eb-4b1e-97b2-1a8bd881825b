import argparse
import logging
from datetime import datetime, timezone
from importlib import import_module
from typing import Any

import numpy as np
import pandas as pd
from google.cloud.bigquery import Client as BQClient
from google.cloud.secretmanager import SecretManagerServiceClient
from google.cloud.storage import Client as GCSClient
from training_utils.clearml_reporter import (
    ClearMLReporter,
    get_clearml_task,
)
from training_utils.model_protocol import BayesianModelProtocol
from training_utils.model_storage import ModelStorage
from training_utils.utils import configure_gpu, ensure_gcp_auth_is_ready, get_repo_info

from bayesian_case_duration.configs.prod_config import ModelTrainingConfig
from bayesian_case_duration.model.dummy_model import DummyBayesianCaseDurationModel
from bayesian_case_duration.model.evaluator import (
    evaluate,
    report_evaluation,
    report_evaluation_standalone,
    upload_evaluation_data,
)
from bayesian_case_duration.model.model import BayesianCaseDurationModel

# Import and configure GPU settings before any PyTensor imports
from bayesian_case_duration.training.data_manager import DataManager

# Check if GPU is available
use_gpu = False
try:
    import torch

    use_gpu = torch.cuda.is_available()
    if use_gpu:
        print("GPU detected and will be used for training")
        # Configure PyTensor to use GPU
        configure_gpu(use_gpu=True)
    else:
        print("No GPU detected, using CPU for training")
        configure_gpu(use_gpu=False)
except ImportError:
    print("PyTorch not available, using CPU for training")
    configure_gpu(use_gpu=False)


# Import FitModel after GPU configuration to ensure proper PyTensor setup
from bayesian_case_duration.training.utils import FitModel  # noqa: E402

logger = logging.getLogger(__name__)

DEFAULT_CONFIG_MODULE = "bayesian_case_duration.configs.experiment_config"
PROD_CONFIG_MODULE = "bayesian_case_duration.configs.prod_config"
MODEL_TYPE = "bayesian_case_duration_model"
SERIALIZATION_PACKAGE_NAME = "cloudpickle"


def get_automated_training_config(evaluate: bool) -> ModelTrainingConfig:
    model_training_config: ModelTrainingConfig = import_module(
        PROD_CONFIG_MODULE
    ).ModelTrainingConfig()

    # set the variables for an automated fit
    fit_time = datetime.now()
    repo = get_repo_info()
    model_identifier = (
        f"automatic_production_training/{fit_time.date()}"
        if not evaluate
        else f"automatic_evaluation_training/{fit_time.date()}"
    )
    model_training_config.data_selection_config.max_date_in_dataset = str(fit_time.date())
    model_training_config.clearml_config.project_name = (
        f"{model_training_config.clearml_config.project_name}: Automated Fit"
    )
    model_training_config.clearml_config.task_name = f"{fit_time.date()} Production Fit"
    model_training_config.clearml_config.tags = {
        "date": str(fit_time),
        "branch_name": repo.active_branch.name,
        "sha": repo.head.object.hexsha,
        "short_sha": repo.git.rev_parse(repo.head.object.hexsha, short=7),
        "model_type": MODEL_TYPE,
        "model_identifier": model_identifier,
    }
    model_training_config.model_identifier = model_identifier

    return model_training_config


def prepare_data(
    model_training_config: ModelTrainingConfig,
    bq_client: BQClient,
    reporter: ClearMLReporter,
) -> tuple[DataManager, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    # Load the data
    data_manager = DataManager(
        bq_client,
        model_training_config.data_selection_config,
        model_training_config.training_config,
        reporter,
    )
    data = data_manager.generate_training_data()
    print("Data index", data.index, "Data loading complete:", len(data), "rows loaded.")

    # Split the data into training and testing
    [train_data, test_data] = data_manager.train_test_split(data, "scheduled_start_datetime_local")
    print(
        len(train_data),
        "Training rows from",
        train_data["scheduled_start_datetime_local"].min(),
        "to",
        train_data["scheduled_start_datetime_local"].max(),
    )
    if model_training_config.training_config.test_on_most_recent_x_days:
        print(
            len(test_data),
            "Testing rows from",
            test_data["scheduled_start_datetime_local"].min(),
            "to",
            test_data["scheduled_start_datetime_local"].max(),
        )

    return data_manager, data, train_data, test_data


def train_model(
    BayesianModel: Any,
    model_training_config: ModelTrainingConfig,
    reporter: ClearMLReporter,
    model_storage: ModelStorage,
    train_data: pd.DataFrame,
) -> None:
    # Initialize the model. Training data is needed in order to initialize the model
    # because categories seen in training data (i.e. unique surgeons, procedures, etc.)
    # inform the structure of the model (i.e. coordinates and number of dimensions)
    print("Initializing all-org model.")
    model = BayesianModel(train_data, model_training_config)

    # Print coordinate counts
    for dim in model.coords:
        print(len(model.coords[dim]), "known", dim)
        if len(model.coords[dim]) < 20:
            print(model.coords[dim])

    # Fit the model
    print("Fitting all-org model.")
    model_fitter = FitModel(model.model, reporter, use_gpu=use_gpu)
    idata = model_fitter.fit()
    model.idata = idata

    # Upload saved tmp files to google cloud storage
    print("Uploading all-org model to Google cloud storage.")
    model_storage.upload_to_google_storage(
        model=model,
        serialization_package_name=SERIALIZATION_PACKAGE_NAME,
        filename=model_training_config.model_file_name,
    )


def evaluate_static_cases(
    model_training_config: ModelTrainingConfig,
    bq_client: BQClient,
    reporter: ClearMLReporter,
    loaded_model: BayesianModelProtocol,
    test_data: pd.DataFrame,
    training_run_datetime: datetime,
) -> None:
    print("Evaluating all-org model on testing data (static use case).")
    # Make predictions on testing data (static use case with all features)
    y_hat_test = loaded_model.predict(test_data)

    # Evaluate testing predictions against actuals
    evaluate_df = evaluate(y_hat_test, test_data)

    # Report evaluation metrics to ClearML
    static_evaluation_df = report_evaluation(reporter, evaluate_df, loaded_model)

    # Upload static evaluation metrics to BigQuery
    print("Saving static model evaluation data to BigQuery.")
    static_evaluation_df["model_type"] = "static"
    static_evaluation_df["model_identifier"] = model_training_config.model_identifier
    static_evaluation_df["ds"] = training_run_datetime.date()
    static_evaluation_df["training_run_time"] = training_run_datetime.isoformat()
    upload_evaluation_data(bq_client, static_evaluation_df)


def evaluate_standalone_cases(
    model_training_config: ModelTrainingConfig,
    bq_client: BQClient,
    reporter: ClearMLReporter,
    loaded_model: BayesianModelProtocol,
    test_data: pd.DataFrame,
    training_run_datetime: datetime,
) -> None:
    print("Evaluating model on testing data (standalone use case).")
    # Make predictions on testing data (standalone use case with only some features)
    # Censor categoical features that standalone model does not have access to
    test_data_standalone = test_data.copy()
    for col in test_data_standalone.columns:
        if col not in [
            "org_id",
            "site_id",
            "first_primary_surgeon",
            "first_primary_procedure",
            "case_procedure_list",
            "procedure_count",
            "scheduled_duration",
        ]:
            if pd.api.types.is_numeric_dtype(test_data_standalone[col]):
                test_data_standalone[col] = np.nan
            else:
                test_data_standalone[col] = "__masked__"
    # Default values for numerical features
    test_data_standalone["surgeon_count"] = 1
    test_data_standalone["num_scheduled_cases"] = None
    test_data_standalone["scheduled_duration"] = None
    y_hat_test_standalone = loaded_model.predict(test_data_standalone)

    # Evaluate testing predictions against actuals
    evaluate_df_standalone = evaluate(y_hat_test_standalone, test_data)

    # Report evaluation metrics to ClearML
    standalone_evaluation_df = report_evaluation_standalone(
        reporter, evaluate_df_standalone, loaded_model
    )

    # Upload standalone evaluation metrics to BigQuery
    print("Saving standalone model evaluation data to BigQuery.")
    standalone_evaluation_df["model_type"] = "standalone"
    standalone_evaluation_df["model_identifier"] = model_training_config.model_identifier
    standalone_evaluation_df["ds"] = training_run_datetime.date()
    standalone_evaluation_df["training_run_time"] = training_run_datetime.isoformat()
    upload_evaluation_data(bq_client, standalone_evaluation_df)


def evaluate_model(
    model_training_config: ModelTrainingConfig,
    bq_client: BQClient,
    reporter: ClearMLReporter,
    loaded_model: BayesianModelProtocol,
    test_data: pd.DataFrame,
) -> None:
    training_run_datetime = datetime.now(tz=timezone.utc)
    evaluate_static_cases(
        model_training_config, bq_client, reporter, loaded_model, test_data, training_run_datetime
    )
    evaluate_standalone_cases(
        model_training_config, bq_client, reporter, loaded_model, test_data, training_run_datetime
    )


def upsert_forecasts(
    model_training_config: ModelTrainingConfig,
    data_manager: DataManager,
    loaded_model: BayesianModelProtocol,
    data: pd.DataFrame,
) -> None:
    # Make predictions on all data
    print(f"Making predictions on all data using {model_training_config.model_identifier}.")
    y_hat_all = loaded_model.predict(data)
    data["forecast_result"] = y_hat_all.tolist()
    data["forecast_median"] = data["forecast_result"].apply(np.median)
    data["ds"] = datetime.now(tz=timezone.utc).date()
    data["model_identifier"] = model_training_config.model_identifier
    # Save predictions to BigQuery
    print(f"Saving predictions to BigQuery for {model_training_config.model_identifier}.")
    reindexed_data = data[
        [
            "case_id",
            "forecast_median",
            "ds",
            "model_identifier",
        ]
    ]
    data_manager.upload_data(reindexed_data)


def evaluation_step(
    model_training_config: ModelTrainingConfig,
    data_manager: DataManager,
    data: pd.DataFrame,
    test_data: pd.DataFrame,
    bq_client: BQClient,
    reporter: ClearMLReporter,
    model_storage: ModelStorage,
    evaluate: bool = False,
    upsert: bool = False,
) -> None:
    print("Downloading model from Google cloud storage.")
    loaded_model = model_storage.load_model(SERIALIZATION_PACKAGE_NAME)

    if evaluate:
        assert loaded_model is not None, "Model must be loaded to evaluate"
        evaluate_model(
            model_training_config,
            bq_client,
            reporter,
            loaded_model,
            test_data,
        )

    if upsert:
        assert loaded_model is not None, "Model must be loaded to evaluate"
        upsert_forecasts(
            model_training_config,
            data_manager,
            loaded_model,
            data,
        )


def main() -> None:
    # Initialize logging
    parser = argparse.ArgumentParser()
    parser.add_argument("--train", help="Run training", action="store_true")
    parser.add_argument("--evaluate", help="Evaluate prior run", action="store_true")
    parser.add_argument(
        "--config-module-name",
        type=str,
        help="absolute module name, starting from PACKAGE_ROOT and using '.' as delimiter, not '/'",
        default=DEFAULT_CONFIG_MODULE,
    )
    parser.add_argument("--is-automated-training", default=False, action="store_true")
    parser.add_argument("--dev", help="Only use to develop an iterate quickly", action="store_true")
    parser.add_argument("--no-gpu", help="Disable GPU acceleration", action="store_true")
    parser.add_argument(
        "--upsert-forecasts",
        help="Whether to use the model to forecast existing cases and upload the results",
        action="store_true",
    )

    args = parser.parse_args()

    # Override GPU usage if --no-gpu flag is provided
    global use_gpu
    if args.no_gpu and use_gpu:
        use_gpu = False
        print("GPU disabled via command line argument --no-gpu")
        configure_gpu(use_gpu=False)

    BayesianModel: Any = None
    if args.dev:
        print(
            "Dev Mode: Using DummyBayesianCaseDurationModel with bayesian_case_duration.configs.dev_config"
        )
        BayesianModel = DummyBayesianCaseDurationModel
        args.config_module_name = "bayesian_case_duration.configs.dev_config"
    else:
        BayesianModel = BayesianCaseDurationModel

    # Load utilities
    ensure_gcp_auth_is_ready()
    bq_client = BQClient()
    gcs_client = GCSClient()

    # Load the TrainingConfig
    model_training_config = import_module(args.config_module_name).ModelTrainingConfig()

    if args.is_automated_training:
        model_training_config = get_automated_training_config(bool(args.evaluate))

    if args.train and not args.evaluate:
        model_training_config.training_config.test_on_most_recent_x_days = 0

    # Load the ClearML credentials
    secret_manager_client = SecretManagerServiceClient()
    clearml_task = get_clearml_task(secret_manager_client, model_training_config.clearml_config)
    reporter = ClearMLReporter(model_training_config.model_dump(), clearml_task)

    # Data Manager and Data
    data_manager, data, train_data, test_data = prepare_data(
        model_training_config, bq_client, reporter
    )

    # Set up the model storage
    model_storage = ModelStorage(
        gcs_client,
        model_type=MODEL_TYPE,
        model_identifier=model_training_config.model_identifier,
    )
    model_storage.rm_local_folder()

    # Train the model
    if args.train:
        train_model(
            BayesianModel,
            model_training_config,
            reporter,
            model_storage,
            train_data,
        )
    else:
        print("Skipping training")

    if args.evaluate or args.upsert_forecasts:
        evaluation_step(
            model_training_config,
            data_manager,
            data,
            test_data,
            bq_client,
            reporter,
            model_storage,
            evaluate=bool(args.evaluate),
            upsert=bool(args.upsert_forecasts),
        )


if __name__ == "__main__":
    main()
