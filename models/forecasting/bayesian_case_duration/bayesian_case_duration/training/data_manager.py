import logging
import re

import pandas as pd
from google.cloud.bigquery import Client as B<PERSON><PERSON>lient
from google.cloud.bigquery import Schem<PERSON><PERSON><PERSON>, Table, TimePartitioning, TimePartitioningType
from training_utils.clearml_reporter import ClearMLReporter

from bayesian_case_duration.configs.prod_config import DataSelectionConfig, TrainingConfig

logger = logging.getLogger(__name__)


class DataManager:
    def __init__(
        self,
        bq_client: BQClient,
        data_config: DataSelectionConfig,
        training_config: TrainingConfig,
        reporter: ClearMLReporter,
    ):
        self.bq_client = bq_client
        self.data_config = data_config
        self.training_config = training_config
        self.reporter = reporter

    def generate_training_data(self) -> pd.DataFrame:
        df = self.query_for_data()
        return df

    def query_for_data(self) -> pd.DataFrame:
        query_string = self.bq_query_string()
        df = self.bq_client.query(query_string).to_dataframe()
        df = self.clean_multi_procedure_data(df)
        return df

    def bayesian_results_schema(self) -> list[SchemaField]:
        return [
            SchemaField("case_id", "STRING", mode="REQUIRED"),
            SchemaField("model_identifier", "STRING", mode="REQUIRED"),
            SchemaField("forecast_median", "FLOAT", mode="REQUIRED"),
            SchemaField("ds", "DATE", mode="REQUIRED"),
        ]

    def train_test_split(self, df_data: pd.DataFrame, datetime_colname: str) -> list[pd.DataFrame]:
        df_data["days_before_most_recent"] = -(
            pd.to_datetime(df_data[datetime_colname])
            - pd.to_datetime(df_data[datetime_colname]).max()
        ).dt.days
        df_train = df_data.query(
            f"days_before_most_recent > {self.training_config.test_on_most_recent_x_days}"
        )
        df_test = df_data.query(
            f"days_before_most_recent <= {self.training_config.test_on_most_recent_x_days}"
        )
        # Some of the historical cases contain incomplete data. For example, a case might have
        # procedure_count = 3 but there is only 1 procedure in case_procedure_list. This causes
        # problems because it is an inaccurate represtation of reality. Below we drop these cases
        # from df_train. This does not impact df_test: we still predict on 100% of cases in df_test.
        # TODO: this should probably be handled upstream somehow
        df_train["num_procedures_check"] = df_train["case_procedure_list"].apply(lambda x: len(x))
        print(
            df_train[df_train["num_procedures_check"] != df_train["procedure_count"]].shape[0],
            "rows dropped from df_train due to inconsistent number of procedures in procedure_count vs. case_procedure_list",
        )
        # Drop training rows where procedure_count != the number of cases in case_procedure_list
        df_train = df_train[df_train["num_procedures_check"] == df_train["procedure_count"]].copy()
        del df_train["num_procedures_check"]

        return [df_train, df_test]

    def clean_multi_procedure_data(self, df: pd.DataFrame) -> pd.DataFrame:
        df = df.copy()
        # Strip multiple whitespaces and capitalize procedure names
        # TODO: procedure name cleaning should happen upstream
        df["case_procedure_list"] = df.apply(
            lambda row: [re.sub(" +", " ", str(i).upper()) for i in row["case_procedure_list"]],
            axis=1,
        )
        return df

    def bq_query_string(self) -> str:
        return f"""
        with features as (
            select 
                case_id,
                scheduled_start_datetime_local,
                org_id,
                site_id,
                first_primary_surgeon,
                first_primary_procedure,
                actual_duration,
                LOG(actual_duration + 1) as actual_duration_log,

                -- Experiment with scheduled duration
                CAST(scheduled_duration AS FLOAT64) as scheduled_duration,
                LOG(scheduled_duration + 1) as scheduled_duration_log,

                -- Additional features
                case_procedure_list,
                procedure_count,
                day_of_week,
                scheduled_starting_hour,
                outpatient,
                number_of_or_day_different_procedures,
                last_case,
                running_during_lunch,
                case_type_short,
                is_flip_room,
                add_on,
                patient_class,
                surgeon_count,
                num_scheduled_cases,
                to_follow_case
            from `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
            where actual_duration is not null
                and date(scheduled_start_datetime_local) <= "{self.data_config.max_date_in_dataset}"
                and date(scheduled_start_datetime_local) >= "{self.data_config.min_date_in_dataset}"     
        )
        select *
        from features
        """

    def upload_data(self, df: pd.DataFrame) -> None:
        table_id = "prod-data-platform-027529.case_forecasting.bayesian_case_duration_results"
        table = Table(
            table_id,
            schema=self.bayesian_results_schema(),
        )
        table.time_partitioning = TimePartitioning(
            type_=TimePartitioningType.DAY,
            field="ds",
        )
        table = self.bq_client.create_table(table, exists_ok=True)
        self.bq_client.insert_rows_from_dataframe(table, df)
