import json
from collections.abc import Set

import pandas as pd
import pandera as pa
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from serving_utils.setup_json_logger import setup_json_logger

logger = setup_json_logger(logger_name="bayesian_case_duration.features")


class BayesianDurationSchema(pa.DataFrameModel):
    """This is the common set of features between the ones we get from the store and the
    ones that end up in the model.
    There are other features that we query from the store that don't end up in the model (those
    are not here).
    There are features we add to these that will end up in the model (those are also not here)
    """

    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    case_type_short: str | None
    patient_class: str | None

    add_on: int | None
    case_procedure_list: list[str] | None
    day_of_week: str | None
    first_primary_procedure: str | None
    first_primary_surgeon: str | None
    is_flip_room: bool | None
    last_case: int | None
    num_scheduled_cases: int | None
    procedure_count: float | None
    org_id: str | None
    outpatient: int | None
    running_during_lunch: int | None
    scheduled_starting_hour: int | None
    surgeon_count: float | None
    to_follow_case: int | None
    site_id: str | None

    scheduled_duration: int | None


class BayesianDurationResultSchema(pa.DataFrameModel):
    """This is the common set of features between the ones we get from the store and the
    ones that end up in the model.
    There are other features that we query from the store that don't end up in the model (those
    are not here).
    There are features we add to these that will end up in the model (those are also not here)
    """

    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    bayesian_cache_hit: bool | None
    bayesian_duration_median_forecast: float | None
    bayesian_duration_forecast: list[float] | None
    bayesian_duration_version: str
    bayesian_duration_service_version: str
    bayesian_duration_model_version: str
    # TODO: https://linear.app/apella/issue/FORC-81/add-trace-ids-to-forecast-results-schema


def get_features_for_cases(
    feature_store: FeatureStore[CaseSchema], case_ids: Set[str]
) -> tuple[pa.typing.DataFrame[BayesianDurationSchema], pd.DataFrame]:
    features_result = feature_store.load_features(case_ids, BayesianDurationSchema)
    if len(features_result.entities_with_missing_features) > 0:
        logger.warning(
            f"Missing features for {len(features_result.entities_with_missing_features)} cases: {features_result.entities_with_missing_features}\n"
            f"Missing features with the following counts {json.dumps(features_result.missing_features_count)}\n"
        )
    return features_result.entities, features_result.bigtable_timestamps
