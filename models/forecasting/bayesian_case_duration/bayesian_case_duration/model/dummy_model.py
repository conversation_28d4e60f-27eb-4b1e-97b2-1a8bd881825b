import os
from typing import List, Union, cast

import cloudpickle  # type: ignore
import numpy as np
import pandas as pd
from pydantic import BaseModel

from .base_model import AbstractCaseDurationModel


class DummyBayesianCaseDurationModel(AbstractCaseDurationModel):
    def __init__(
        self, df: pd.DataFrame, config: BaseModel, artifact_path: str | None = None
    ) -> None:
        """Initializes a dummy model, loading pre-saved artifacts if available."""
        self.config = config.model_dump()
        self.artifact_path = artifact_path
        self.idata = None
        # Add coords and priors attributes expected by tests/downstream code if necessary
        self.coords: dict[str, list[str]] = {}  # Initialize appropriately if needed
        self.priors: dict[str, Union[List[float], dict[str, int]]] = {}
        self._load_artifact()

    def _load_artifact(self) -> None:
        """Loads a pre-saved model artifact (e.g., idata)."""
        try:
            if not self.artifact_path:
                raise ValueError("Artifact path is not set.")

            with open(self.artifact_path, "rb") as f:
                model_file = cloudpickle.load(f)
                self.idata = model_file.get("idata")
                # Load other components if needed, e.g., coords, priors
                self.coords = model_file.get("coords", {})
                self.priors = model_file.get("priors", {})
        except FileNotFoundError:
            print(f"Artifact file not found at {self.artifact_path}. Model needs fitting.")
            # Initialize default coords/priors if artifact not found
            self._initialize_defaults()
        except Exception as e:
            print(f"Error loading artifact: {e}")
            self._initialize_defaults()

    def _initialize_defaults(self) -> None:
        """Initialize default coords and priors if loading fails."""
        # Add minimal coords/priors needed for basic functionality or tests
        self.coords = {"site_id": ["APELLA_UNKNOWN"]}
        self.priors = {"num_scheduled_cases_priors": {"APELLA_UNKNOWN": 5}}
        print("Initialized default coords and priors for dummy model.")

    @property
    def model(self) -> None:
        # Dummy model doesn't have a real PyMC model, return None or mock
        return None

    def predict(self, df: pd.DataFrame) -> np.typing.NDArray[np.float64]:
        """Generates dummy predictions. Returns a fixed value for simplicity."""
        n_predictions = len(df)
        n_samples = 500  # Match the expected output shape
        return np.full((n_predictions, n_samples), 60.0)  # Example: always predict 60 minutes

    def predict_from_inference_dict(self, inference_dict: dict[str, str]) -> list[float]:
        """Dummy implementation for inference dictionary predictions."""
        inference_df = pd.DataFrame([inference_dict])
        # Ensure required columns exist for predict and postprocess
        if "scheduled_duration" not in inference_df.columns:
            inference_df["scheduled_duration"] = None  # Add default if missing

        y_hat = self.predict(inference_df)
        y_hat_processed = self.postprocess_prediction(y_hat, inference_df)

        y_hat_rounded = np.round(y_hat_processed.astype(np.float64), 1)
        y_hat_list = y_hat_rounded.flatten().tolist()
        return cast(List[float], y_hat_list)

    def save_to_local_file(self, output_path: str) -> None:
        """Serialize and save the entire model instance using cloudpickle.

        Args:
            output_path: Full path to save the serialized model artifact
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            with open(output_path, "wb") as f:
                cloudpickle.dump(
                    {
                        "model": self.model,
                        "idata": self.idata,
                        "coords": self.coords,
                        "priors": self.priors,
                        "config": self.config,
                    },
                    f,
                )
            print(f"Successfully saved model artifact to {output_path}")
        except Exception as e:
            print(f"Error saving model artifact: {e}")
            raise
