import re
import warnings
from itertools import combinations
from math import comb
from typing import List, Union, cast

import cloudpickle  # type: ignore
import numpy as np
import pandas as pd
import pymc as pm  # type: ignore
import pytensor.tensor as pt
from arviz import InferenceData
from pydantic import BaseModel

from .base_model import AbstractCaseDurationModel


class BayesianCaseDurationModel(AbstractCaseDurationModel):
    MAX_PROCEDURES = 20
    MIN_DURATION_MINUTES = 1
    MAX_DURATION_MINUTES = 3000

    def __init__(self, df: pd.DataFrame, config: BaseModel) -> None:
        self.coords: dict[str, List[str]]
        self.priors: dict[str, Union[List[float], dict[str, int]]] = {}
        self.max_procedures_per_case: int = self.MAX_PROCEDURES
        self.padded_procedure_list_indices: np.typing.NDArray[np.int64]
        self.padded_procedure_pair_indices: np.typing.NDArray[np.int64]
        self.orgs_to_sites: dict[str, List[str]]
        self.model: pm.model.core.Model
        self.idata: InferenceData
        self.config: dict[str, str]
        self.generate_coords(df)
        self.create_median_num_scheduled_cases(df)
        df = self.preprocess_data(df)
        self.create_priors(df)
        self.build_model(df)
        self.config = config.model_dump()

    @property
    def max_pairs_per_case(self) -> int:
        return comb(self.MAX_PROCEDURES, 2)

    def create_priors(self, df: pd.DataFrame) -> None:
        """
        This function creates any priors in the model that are more complex and need access to the training data. Right now it only creates priors for procedure -> mu. But may include more priors in the future.
        """
        self.create_procedure_mu_priors(df)
        self.create_orgs_to_sites(df)

    def create_orgs_to_sites(self, df: pd.DataFrame) -> None:
        # Create a mapping from org_id to its corresponding site_ids
        # Example: self.org_to_sites = {"org1": ["site1", "site2"], "org2": ["site3"], ...}
        orgs_to_sites: dict[str, list[str]] = {}
        df_grouped = df.groupby(["org_id", "site_id"]).count().reset_index()[["org_id", "site_id"]]
        unique_orgs = list(sorted(df_grouped["org_id"].unique()))
        for i in unique_orgs:
            orgs_to_sites[i] = []
        for i, row in df_grouped.iterrows():
            orgs_to_sites[row["org_id"]].append(row["site_id"])
        self.orgs_to_sites = orgs_to_sites

    def create_procedure_mu_priors(self, df: pd.DataFrame) -> None:
        """
        This function uses training data to construct reasonable priors for each first primary procedure type based on the median duration in the training data.
        Now that we are exclicitly accounting for multi-procedures, use only single procedure cases to form priors here.
        """
        # Create a dictionary mapping procedure type -> median log-duration for that procedure.
        # Limit to single procedure cases only because we use this to build priors for the impact
        # of each standalone procedure on case duration.
        single_procedure_df = df.query("procedure_count == 1").copy()

        # Strip white spaces from procedure names and make sure all caps
        single_procedure_df["first_primary_procedure"] = single_procedure_df[
            "first_primary_procedure"
        ].apply(lambda x: re.sub(" +", " ", x).upper())

        # Ensure uniqueness after white spaces have been stripped
        single_procedure_df = (
            single_procedure_df.groupby("first_primary_procedure").first().reset_index()
        )

        # Create a dictionary with the median log-duration for each procedure
        single_procedure_median_duration_dict = (
            single_procedure_df.groupby(["first_primary_procedure"])["actual_duration_log"]
            .median()
            .to_dict()
        )

        # Set the prior for an APELLA_UNKNOWN procedure type equal to the median of all procedures
        APELLA_UNKNOWN_median = single_procedure_df["actual_duration_log"].median()

        # List comprehension to create a list of all median durations that exactly matches
        # the order of procedures in coords["procedure"]. If procedure does not exist in the dict
        # use APELLA_UNKNOWN_median
        procedure_mu_priors = [
            single_procedure_median_duration_dict.get(i, APELLA_UNKNOWN_median)
            for i in self.coords["procedure"]
        ]
        self.priors["procedure_mu_priors"] = procedure_mu_priors

    def create_median_num_scheduled_cases(self, df: pd.DataFrame) -> None:
        """
        This function uses training data to construct default assumptions for num_scheduled_cases based on median num_scheduled_cases grouped by site_id.
        """
        # Create a dictionary mapping site_id -> median num_scheduled_cases.
        median_num_scheduled_cases_dict = (
            df.groupby(["site_id"])["num_scheduled_cases"].median().astype(int).to_dict()
        )

        # Handle Unknown site ID by setting num_scheduled_cases equal to the median of all sites
        APELLA_UNKNOWN_median = np.median(list(median_num_scheduled_cases_dict.values())).astype(
            int
        )
        median_num_scheduled_cases_dict["APELLA_UNKNOWN"] = APELLA_UNKNOWN_median

        self.priors["num_scheduled_cases_priors"] = median_num_scheduled_cases_dict

    def preprocess_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        This function cleans the data before generating coords, before training the model, and before making predictions. Primarily, it is used to handle cases with surgeon or procedure or some other feature have never been seen before and are thus set to "APELLA_UNKNOWN".
        """
        df = df.copy()
        df.loc[:, "first_primary_procedure_clean"] = df.apply(
            lambda x: self.clean_procedure(x["first_primary_procedure"], self.coords["procedure"]),
            axis=1,
        )
        df.loc[:, "first_primary_surgeon_clean"] = df.apply(
            lambda x: self.clean_surgeon(x["first_primary_surgeon"], self.coords["surgeon"]), axis=1
        )
        df.loc[:, "first_primary_surgeon_procedure_clean"] = df.apply(
            lambda x: self.clean_surgeon_procedure(
                x["first_primary_surgeon_clean"],
                x["first_primary_procedure_clean"],
                self.coords["surgeon_procedure"],
            ),
            axis=1,
        )
        df.loc[:, "site_id_clean"] = df.apply(
            lambda x: self.clean_site_id(x["site_id"], self.coords["site_id"]), axis=1
        )
        df.loc[:, "day_of_week_clean"] = df.apply(
            lambda x: self.clean_day_of_week(x["day_of_week"], self.coords["day_of_week"]),
            axis=1,
        )
        df.loc[:, "scheduled_starting_hour_clean"] = df.apply(
            lambda x: self.clean_scheduled_starting_hour(
                x["scheduled_starting_hour"], self.coords["scheduled_starting_hour"]
            ),
            axis=1,
        )
        df.loc[:, "outpatient_clean"] = df.apply(
            lambda x: self.clean_outpatient(x["outpatient"]), axis=1
        )
        df.loc[:, "last_case_clean"] = df.apply(
            lambda x: self.clean_last_case(x["last_case"]), axis=1
        )
        df.loc[:, "procedure_count_clean"] = df.apply(
            lambda x: self.clean_procedure_count(x["procedure_count"]), axis=1
        )
        df.loc[:, "surgeon_count_clean"] = df.apply(
            lambda x: self.clean_surgeon_count(x["surgeon_count"]), axis=1
        )
        df.loc[:, "add_on_clean"] = df.apply(lambda x: self.clean_add_on(x["add_on"]), axis=1)

        df.loc[:, "is_flip_room_clean"] = df.apply(
            lambda x: self.clean_is_flip_room(x["is_flip_room"]), axis=1
        )
        df.loc[:, "case_type_short_clean"] = df.apply(
            lambda x: self.clean_case_type_short(
                x["case_type_short"], self.coords["case_type_short"]
            ),
            axis=1,
        )
        df.loc[:, "to_follow_case_clean"] = df.apply(
            lambda x: self.clean_to_follow_case(x["to_follow_case"]), axis=1
        )
        df.loc[:, "num_scheduled_cases_clean"] = df.apply(
            lambda x: self.clean_num_scheduled_cases(x["num_scheduled_cases"], x["site_id"]), axis=1
        )
        return df

    def clean_procedure(self, procedure: str, known_procedures: list[str]) -> str:
        procedure = re.sub(" +", " ", procedure).upper()  # removes one or more whitespaces
        if procedure in known_procedures:
            return procedure
        else:
            return "APELLA_UNKNOWN"

    def clean_surgeon(self, surgeon: str, known_surgeons: list[str]) -> str:
        surgeon = re.sub(" +", " ", surgeon).upper()  # removes one or more whitespaces
        if surgeon in known_surgeons:
            return surgeon
        else:
            return "APELLA_UNKNOWN"

    def clean_surgeon_procedure(
        self, surgeon: str, procedure: str, known_surgeon_procedures: list[str]
    ) -> str:
        procedure = re.sub(" +", " ", procedure)  # removes one or more whitespaces
        surgeon = re.sub(" +", " ", surgeon)  # removes one or more whitespaces
        interaction = surgeon + " : " + procedure
        if interaction in known_surgeon_procedures:
            return interaction
        else:
            return "APELLA_UNKNOWN"

    def clean_org_id(self, org_id: str, known_org_ids: list[str]) -> str:
        if org_id in known_org_ids:
            return org_id
        else:
            return "APELLA_UNKNOWN"

    def clean_site_id(self, site_id: str, known_site_ids: list[str]) -> str:
        if site_id in known_site_ids:
            return site_id
        else:
            return "APELLA_UNKNOWN"

    def clean_day_of_week(self, day_of_week: str, known_day_of_weeks: list[str]) -> str:
        day_of_week = str(day_of_week)  # ensure it is string
        if day_of_week in known_day_of_weeks:
            return day_of_week
        else:
            return "Tuesday"  # default to Tuesday if day of week is unspecified

    def clean_scheduled_starting_hour(
        self, scheduled_starting_hour: int, known_scheduled_starting_hours: list[str]
    ) -> str:
        scheduled_starting_hour_str = str(scheduled_starting_hour)  # convert to string
        if scheduled_starting_hour_str in known_scheduled_starting_hours:
            return scheduled_starting_hour_str
        else:
            return "7"  # default to 7am if starting hour is unspecificed

    def clean_outpatient(self, outpatient: int) -> str:
        outpatient_str = str(outpatient)  # convert to string
        if outpatient_str == "0":
            return outpatient_str
        elif outpatient_str == "1":
            return outpatient_str
        else:
            return "1"  # default to is outpatient if unspecified

    def clean_last_case(self, last_case: int) -> str:
        last_case_str = str(last_case)  # convert to string
        if last_case_str == "0":
            return last_case_str
        elif last_case_str == "1":
            return last_case_str
        else:
            return "0"  # default to not last_case if unspecified

    def clean_procedure_count(self, procedure_count: int) -> str:
        if procedure_count == 1:
            return "one_procedure"
        elif procedure_count == 2:
            return "two_procedures"
        elif procedure_count >= 3:
            return "three_plus_procedures"
        else:
            return "one_procedure"  # default is one_procedure if procedure_count is unspecified

    def clean_surgeon_count(self, surgeon_count: int) -> str:
        if surgeon_count == 1:
            return "one_surgeon"
        elif surgeon_count == 2:
            return "two_surgeons"
        elif surgeon_count >= 3:
            return "three_plus_surgeons"
        else:
            return "one_surgeon"  # default is one_surgeon if surgeon_count is unspecified

    def clean_add_on(self, add_on: int) -> str:
        add_on_str = str(add_on)  # convert to string
        if add_on_str == "0":
            return add_on_str
        elif add_on_str == "1":
            return add_on_str
        else:
            return "0"  # default to not add_on if unspecified

    def clean_to_follow_case(self, to_follow_case: int) -> str:
        to_follow_case_str = str(to_follow_case)  # convert to string
        if to_follow_case_str == "0":
            return to_follow_case_str
        elif to_follow_case_str == "1":
            return to_follow_case_str
        else:
            return "0"  # default to not to_follow_case if unspecified

    def clean_is_flip_room(self, is_flip_room: str) -> str:
        is_flip_room_str = str(is_flip_room)  # convert to string
        if is_flip_room_str == "False":
            return is_flip_room_str
        elif is_flip_room_str == "True":
            return is_flip_room_str
        else:
            return "False"  # default to not is_flip_room if unspecified

    def clean_case_type_short(self, case_type_short: str, known_case_type_shorts: list[str]) -> str:
        if case_type_short in known_case_type_shorts:
            return case_type_short
        else:
            return "APELLA_UNKNOWN"

    def clean_num_scheduled_cases(self, num_scheduled_cases: int | None, site_id: str) -> int:
        site_id = self.clean_site_id(site_id, self.coords["site_id"])
        median_num_scheduled_cases_dict = self.priors["num_scheduled_cases_priors"]
        # If num_scheduled_cases is provided, use it
        if num_scheduled_cases is not None:
            return int(num_scheduled_cases)
        # If num_scheduled_cases is not provided, use the median for that site
        else:
            return int(median_num_scheduled_cases_dict[site_id])  # type: ignore

    def generate_coords(self, df: pd.DataFrame) -> None:
        """
        This function constructs the coords dictionary which is used to keep track of the names of all known categorical variables in the model.
        """
        df = df.copy()

        # Strip whitespaces from each name in case_procedure_list and ensure all caps
        df["case_procedure_list"] = df["case_procedure_list"].apply(
            lambda lst: [re.sub(" +", " ", x).upper() for x in lst]
        )

        # Known procedures list is now generated from the full exploded case_procedure_list rather than
        # just the first_primary_procedure because we are now accounting for multi-procedures
        known_procedures = sorted(list(df["case_procedure_list"].explode().unique()))
        if "APELLA_UNKNOWN" not in known_procedures:
            known_procedures.append("APELLA_UNKNOWN")

        # Here we build a list of all unique procedure pairs that have co-existed in a case together
        # This is later used to define the procedure pair synergies. They are sorted alphabetically
        # so that A_B and B_A are coalesced to A_B (i.e. so that order does not matter).
        procedure_pairs: set[tuple[str, str]] = set()
        for procedures in df["case_procedure_list"]:
            if len(procedures) >= 2:
                # Generate all unique pairs for this case
                pairs = combinations(sorted(procedures), 2)  # Sort to ensure consistent ordering
                procedure_pairs.update(pairs)
        # Convert each tuple pair to a sorted string (e.g., ("A", "B") -> "A_B")
        known_procedure_pairs = list(sorted([f"{pair[0]}_{pair[1]}" for pair in procedure_pairs]))
        # An UKNOWN is appended to serve as a placeholder for unseen procedure pairs
        known_procedure_pairs.append("APELLA_UNKNOWN")

        # Remove extra white spaces and capitalize
        # TODO: procedure name cleaning should happen upstream
        df["first_primary_procedure"] = df["first_primary_procedure"].apply(
            lambda x: re.sub(" +", " ", x).upper()
        )
        df["first_primary_surgeon"] = df["first_primary_surgeon"].apply(
            lambda x: re.sub(" +", " ", x).upper()
        )

        known_surgeons = list(sorted(df["first_primary_surgeon"].unique()))
        if "APELLA_UNKNOWN" not in known_surgeons:
            known_surgeons.append("APELLA_UNKNOWN")

        df.loc[:, "first_primary_surgeon_procedure"] = (
            df["first_primary_surgeon"] + " : " + df["first_primary_procedure"]
        )
        known_surgeon_procedures = list(sorted(df["first_primary_surgeon_procedure"].unique()))
        if "APELLA_UNKNOWN" not in known_surgeon_procedures:
            known_surgeon_procedures.append("APELLA_UNKNOWN")

        known_org_ids = list(sorted(df["org_id"].unique()))
        if "APELLA_UNKNOWN" not in known_org_ids:
            known_org_ids.append("APELLA_UNKNOWN")

        known_site_ids = list(sorted(df["site_id"].unique()))
        if "APELLA_UNKNOWN" not in known_site_ids:
            known_site_ids.append("APELLA_UNKNOWN")

        known_case_type_shorts = list(sorted(df["case_type_short"].unique()))
        if "APELLA_UNKNOWN" not in known_case_type_shorts:
            known_case_type_shorts.append("APELLA_UNKNOWN")

        known_outpatients = ["0", "1"]

        known_last_cases = ["0", "1"]

        known_add_ons = ["0", "1"]

        known_to_follow_cases = ["0", "1"]

        known_is_flip_rooms = ["False", "True"]

        known_procedure_counts = ["one_procedure", "two_procedures", "three_plus_procedures"]

        known_surgeon_counts = ["one_surgeon", "two_surgeons", "three_plus_surgeons"]

        known_day_of_weeks = [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
            "Sunday",
        ]

        known_scheduled_starting_hours = [
            "0",
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
            "20",
            "21",
            "22",
            "23",
        ]

        coords = {
            "procedure": known_procedures,
            "procedure_pair": known_procedure_pairs,
            "surgeon": known_surgeons,
            "surgeon_procedure": known_surgeon_procedures,
            "org_id": known_org_ids,
            "site_id": known_site_ids,
            "outpatient": known_outpatients,
            "day_of_week": known_day_of_weeks,
            "scheduled_starting_hour": known_scheduled_starting_hours,
            "last_case": known_last_cases,
            "procedure_count": known_procedure_counts,
            "surgeon_count": known_surgeon_counts,
            "add_on": known_add_ons,
            "is_flip_room": known_is_flip_rooms,
            "case_type_short": known_case_type_shorts,
            "to_follow_case": known_to_follow_cases,
        }
        self.coords = coords

        # In addition to the coords, we generate padded array representations of both
        # multiprocedure cases and also potentially synergistic procedure pairs
        self.padded_procedure_list_indices = self.create_padded_procedure_list_indices(df)
        self.padded_procedure_pair_indices = self.create_padded_procedure_pair_indices(df)

    def create_padded_procedure_list_indices(self, df: pd.DataFrame) -> np.typing.NDArray[np.int64]:
        """
        This function returns a matrix to represent multiprocedure cases. Every row is a case, and every column is a potential procedure contained within the case. It has default fixed width of {self.MAX_PROCEDURES}, meaning that we allow up to a maximum of {self.MAX_PROCEDURES} procedures within a single case.

        For example, a single case containing three procedures might look like this:

        [40, 362, 2008, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]

        Where the first three numbers (40, 362, 2008) represent the indices of the three procedures contained within the case. Procedure #40 might correspond to "MYRINGOTOMY", and procedure #362 might correspond to "TONSILLECTOMY", etc. The index to procedure name mappings are contained in coords['procedure']. The -1 values mean that there is NOT a procedure in position 4 through {self.MAX_PROCEDURES}. The -1 values are used as a mask in the model to indicate that there is no procedure. This approach enables us to use a matrix of fixed width {self.MAX_PROCEDURES} that is more dense than a sparse one-hot encoded matrix (which would be several thousand columns wide and cause model fitting to be extremely slow) while still flexibly modeling cases that may contain differing numbers of procedures.

        We call this matrix padded_procedure_list_indices and store it as an attribute of BayesianCaseDurationModel to be used later.
        """
        df = df.copy()

        # Strip whitespaces from each name in case_procedure_list and ensure all caps
        df["case_procedure_list"] = df["case_procedure_list"].apply(
            lambda lst: [re.sub(" +", " ", x).upper() for x in lst]
        )

        # Created padded array containing to represent multiple procedures in a case
        procedure_indices = [
            [
                self.coords["procedure"].index(proc)
                if proc in self.coords["procedure"]
                else self.coords["procedure"].index("APELLA_UNKNOWN")
                for proc in procs
            ]
            for procs in df["case_procedure_list"]
        ]
        padded_procedure_list_indices = np.full(
            (len(procedure_indices), self.max_procedures_per_case),
            fill_value=-1,
            dtype=int,
        )
        for i, indices in enumerate(procedure_indices):
            padded_procedure_list_indices[i, : len(indices)] = indices
        return padded_procedure_list_indices

    def create_padded_procedure_pair_indices(self, df: pd.DataFrame) -> np.typing.NDArray[np.int64]:
        """
        This function returns a matrix to represent observed procedure pairs, so that we can model synergies. For example, consider a three procedure case ['Procedure B', 'Procedure A', 'Procedure C']. First, the procedures are sorted alphabetically to ensure consistent ordering. So they become: ['Procedure A', 'Procedure B', 'Procedure C'].
        Next, all combinations of pairs are generated.

        In this example, there would be three tuples:
        ('Procedure A', 'Procedure B')
        ('Procedure A', 'Procedure C')
        ('Procedure B', 'Procedure C')

        These tuples then get converted into strings separated by an underscore:
        'Procedure A_Procedure B'
        'Procedure A_Procedure C'
        'Procedure B_Procedure C'

        And finally we look up the index of each pair from coords["procedure_pair"]. So each string is associated with a number (there are currently around 25,000 pairs of procedures that have co-occurred in a case).
        'Procedure A_Procedure B' --> 234
        'Procedure A_Procedure C' --> 595
        'Procedure B_Procedure C' --> 800

        So our example case would have it's three procedure pairs encoded as:
        [234, 595, 800]

        Since we allow for a maximum of 20 procedures in a case, there are a maximum of 190 procedure pairs in a case (think N choose K: 20 choose 2 = 190 possible combinations). So, we use a padded array with fixed width of 190 to encode all possible procedure pairs up to a maximum of 20 procedures in one case, and 190 procedure pair combinations in one case.
        Again, we encode missing values with -1 so we can mask them out later.

        So [234, 595, 800] becomes an array with fixed width of 190:
        [234, 595, 800, -1, -1, -1, -1, -1, -1, ..., -1, -1, -1, -1, -1, -1, -1, -1, -1, -1]

        We call this matrix padded_procedure_pair_indices and store it as an attribute of BayesianCaseDurationModel to be used later.
        """
        df = df.copy()

        # Strip whitespaces from each name in case_procedure_list and ensure all caps
        df["case_procedure_list"] = df["case_procedure_list"].apply(
            lambda lst: [re.sub(" +", " ", x).upper() for x in lst]
        )

        # Created padded array containing indices to represent procedure pairs in a case
        procedure_pair_indices = []
        for procedures in df["case_procedure_list"]:
            if len(procedures) >= 2:
                pairs = list(combinations(sorted(procedures), 2))
                pair_strings = [f"{pair[0]}_{pair[1]}" for pair in pairs]
                pair_indices = [
                    self.coords["procedure_pair"].index(pair_str)
                    if pair_str in self.coords["procedure_pair"]
                    else self.coords["procedure_pair"].index("APELLA_UNKNOWN")
                    for pair_str in pair_strings
                ]
                procedure_pair_indices.append(pair_indices)
            else:
                procedure_pair_indices.append([])  # No pairs for cases with < 2 procedures
        padded_procedure_pair_indices = np.full(
            (len(procedure_pair_indices), self.max_pairs_per_case),
            fill_value=-1,
            dtype=int,
        )
        for i, pair_indices in enumerate(procedure_pair_indices):
            padded_procedure_pair_indices[i, : len(pair_indices)] = pair_indices
        return padded_procedure_pair_indices

    def build_model(self, df: pd.DataFrame) -> None:
        """
        This function constructs the PyMC model.
        Mutable pm.Data containers are used to store feature indices and target variable so that
        pm.set_data() can later be used when making predictions on new data.
        """
        with pm.Model(coords=self.coords) as model:
            ########################################################################################
            #               DATA CONTAINERS
            ########################################################################################
            procedure_idx = pm.Data(
                "procedure_idx",
                pd.Categorical(
                    df["first_primary_procedure_clean"], categories=self.coords["procedure"]
                ).codes,
            )
            surgeon_idx = pm.Data(
                "surgeon_idx",
                pd.Categorical(
                    df["first_primary_surgeon_clean"], categories=self.coords["surgeon"]
                ).codes,
            )
            surgeon_procedure_idx = pm.Data(
                "surgeon_procedure_idx",
                pd.Categorical(
                    df["first_primary_surgeon_procedure_clean"],
                    categories=self.coords["surgeon_procedure"],
                ).codes,
            )
            site_id_idx = pm.Data(
                "site_id_idx",
                pd.Categorical(df["site_id_clean"], categories=self.coords["site_id"]).codes,
            )
            day_of_week_idx = pm.Data(
                "day_of_week_idx",
                pd.Categorical(
                    df["day_of_week_clean"], categories=self.coords["day_of_week"]
                ).codes,
            )
            scheduled_starting_hour_idx = pm.Data(
                "scheduled_starting_hour_idx",
                pd.Categorical(
                    df["scheduled_starting_hour_clean"],
                    categories=self.coords["scheduled_starting_hour"],
                ).codes,
            )
            outpatient_idx = pm.Data(
                "outpatient_idx",
                pd.Categorical(df["outpatient_clean"], categories=self.coords["outpatient"]).codes,
            )
            last_case_idx = pm.Data(
                "last_case_idx",
                pd.Categorical(df["last_case_clean"], categories=self.coords["last_case"]).codes,
            )
            procedure_count_idx = pm.Data(
                "procedure_count_idx",
                pd.Categorical(
                    df["procedure_count_clean"], categories=self.coords["procedure_count"]
                ).codes,
            )
            surgeon_count_idx = pm.Data(
                "surgeon_count_idx",
                pd.Categorical(
                    df["surgeon_count_clean"], categories=self.coords["surgeon_count"]
                ).codes,
            )
            add_on_idx = pm.Data(
                "add_on_idx",
                pd.Categorical(df["add_on_clean"], categories=self.coords["add_on"]).codes,
            )
            is_flip_room_idx = pm.Data(
                "is_flip_room_idx",
                pd.Categorical(
                    df["is_flip_room_clean"], categories=self.coords["is_flip_room"]
                ).codes,
            )
            case_type_short_idx = pm.Data(
                "case_type_short_idx",
                pd.Categorical(
                    df["case_type_short_clean"], categories=self.coords["case_type_short"]
                ).codes,
            )
            to_follow_case_idx = pm.Data(
                "to_follow_case_idx",
                pd.Categorical(
                    df["to_follow_case_clean"], categories=self.coords["to_follow_case"]
                ).codes,
            )
            num_scheduled_cases = pm.Data(
                "num_scheduled_cases", df["num_scheduled_cases_clean"].values
            )
            padded_procedure_list_idx = pm.Data(
                "padded_procedure_list_idx", self.padded_procedure_list_indices
            )
            procedure_pairs_idx = pm.Data("procedure_pairs_idx", self.padded_procedure_pair_indices)
            y_obs_log = pm.Data("y_obs_log", df["actual_duration_log"].values)

            ########################################################################################
            #               SUM OF MULTI PROCEDURE DURATIONS
            ########################################################################################
            # Prior for the standard deviation of each procedure category
            sigma_procedure = pm.HalfNormal("sigma_procedure", sigma=1, dims="procedure")

            # Prior for the duration of each procedure category
            procedure_durations = pm.Normal(
                "procedure_durations",
                mu=np.array(self.priors["procedure_mu_priors"]),
                sigma=sigma_procedure,
                dims="procedure",
            )

            # Using a mask for valid procedure indices (non -1 values)
            valid_procedure_mask = pt.cast(pt.neq(padded_procedure_list_idx, -1), "float32")
            valid_procedure_durations = (
                procedure_durations[padded_procedure_list_idx] * valid_procedure_mask
            )

            # Prevent numerical under/overflow in the logsumexp calculation using max-trick
            # When working with sums of exponentials as we do below, the numbers involved can grow
            # very large, leading to numerical overflow, or become extremely small, leading to
            # underflow. This can result in NaN values or inf values when performing further
            # calculations. The trick to avoid these issues is to subtract the maximum value from
            # the terms inside the exponential. After summing the exponentials, you take the
            # logarithm of the result and add back the max_log value to return to the correct scale.
            max_log = pm.math.max(procedure_durations, keepdims=True)

            # TODO: Consider a putting lower threshold of 1 second within the exp() to address underflow.
            # Sum the individual procedure durations. Since we are working in log space
            # we need to exponentiate before taking the sum and then transform back to log space.
            multiprocedure_duration_sum = max_log + pm.math.log(
                pm.math.sum(
                    pm.math.exp(valid_procedure_durations - max_log),
                    axis=1,  # Sum across all procedures for each case
                )
            )

            ########################################################################################
            #               SYNERGY EFFECTS
            ########################################################################################
            # TODO: Are synergies among pairs in 3+ procedure cases being accurately calculated?
            # TODO: Probably should be a log sum exp logic here as well
            # Suggestion: Create tests with simulated data? Will look into simulation.
            # Synergy effects modeled as normal with a small sigma (can be negative or positive)
            synergy_effects = pm.Normal(
                "synergy_effects",
                mu=0,
                sigma=0.1,
                dims="procedure_pair",
            )

            # Mask for valid procedure pair indices (non -1 values)
            valid_pair_mask = pt.cast(pt.neq(procedure_pairs_idx, -1), "float32")
            valid_synergy_effects = synergy_effects[procedure_pairs_idx] * valid_pair_mask

            # Sum all procedure pair synergy effecs across a single case
            synergy_effect_sum = pm.math.sum(valid_synergy_effects, axis=1)

            ########################################################################################
            #               SURGEON -> MU
            ########################################################################################
            beta_surgeon = pm.Normal("beta_surgeon", mu=0, sigma=0.1, dims="surgeon")

            ########################################################################################
            #               SURGEON x PROCEDURE -> MU
            ########################################################################################
            beta_surgeon_procedure = pm.Normal(
                "beta_surgeon_procedure", mu=0, sigma=1.0, dims="surgeon_procedure"
            )

            ########################################################################################
            #               OUTPATIENT x SITE_ID -> MU
            ########################################################################################
            beta_outpatient = pm.Normal(
                "beta_outpatient", mu=0, sigma=0.1, dims=("outpatient", "site_id")
            )

            ########################################################################################
            #               LAST CASE x SITE_ID -> MU
            ########################################################################################
            beta_last_case = pm.Normal(
                "beta_last_case", mu=0, sigma=0.1, dims=("last_case", "site_id")
            )

            ########################################################################################
            #               PROCEDURE COUNT -> MU
            ########################################################################################
            beta_procedure_count = pm.Normal(
                "beta_procedure_count", mu=0, sigma=0.1, dims=("procedure_count", "procedure")
            )

            ########################################################################################
            #               SURGEON COUNT -> MU
            ########################################################################################
            beta_surgeon_count = pm.Normal(
                "beta_surgeon_count", mu=0, sigma=0.1, dims=("surgeon_count", "procedure")
            )

            ########################################################################################
            #               ADD ON -> MU
            ########################################################################################
            beta_add_on = pm.Normal("beta_add_on", mu=0, sigma=0.05, dims=("add_on", "procedure"))

            ########################################################################################
            #               FLIP ROOM -> MU
            ########################################################################################
            beta_is_flip_room = pm.Normal(
                "beta_is_flip_room", mu=0, sigma=0.05, dims=("is_flip_room", "procedure")
            )

            ########################################################################################
            #               CASE TYPE -> MU
            ########################################################################################
            beta_case_type_short = pm.Normal(
                "beta_case_type_short", mu=0, sigma=0.05, dims=("case_type_short", "procedure")
            )

            ########################################################################################
            #               TO FOLLOW CASE -> MU
            ########################################################################################
            beta_to_follow_case = pm.Normal(
                "beta_to_follow_case", mu=0, sigma=0.05, dims=("to_follow_case", "procedure")
            )

            ########################################################################################
            #               NUM SCHEDULED CASES -> MU
            ########################################################################################
            beta_num_scheduled_cases = pm.Normal(
                "beta_num_scheduled_cases", mu=0, sigma=0.1, dims=("site_id")
            )

            ########################################################################################
            #               DAY_OF_WEEK x SCHEDULED_STARTING_HOUR x SITE_ID -> MU
            ########################################################################################
            # This approach models the impacts of both day_of_week and scheduled_starting_hour on case duration, while ensuring that these impacts are ZeroSumNormal within each site_id. If day of week or scheduled_starting_hour are unspecified, default to Tuesday at 7am.

            # Initialize tensor for the interaction between day_of_week and scheduled_starting_hour across site_id
            beta_day_of_week_scheduled_starting_hour = pt.zeros(
                (
                    len(self.coords["day_of_week"]),
                    len(self.coords["scheduled_starting_hour"]),
                    len(self.coords["site_id"]),
                )
            )  # type: ignore

            # Apply ZeroSumNormal by site_id
            for site_idx in range(len(self.coords["site_id"])):
                # Zero-sum normal for day_of_week and scheduled_starting_hour effects within each site_id
                beta_day_of_week_scheduled_starting_hour_site = pm.ZeroSumNormal(
                    f"beta_day_of_week_scheduled_starting_hour_site_{self.coords['site_id'][site_idx]}",
                    sigma=0.1,
                    shape=(
                        len(self.coords["day_of_week"]),
                        len(self.coords["scheduled_starting_hour"]),
                    ),  # One effect per pair of day_of_week and scheduled_starting_hour
                )

                # Assign the site-specific day_of_week and scheduled_starting_hour effects
                beta_day_of_week_scheduled_starting_hour = pt.set_subtensor(
                    beta_day_of_week_scheduled_starting_hour[:, :, site_idx],
                    beta_day_of_week_scheduled_starting_hour_site,
                )  # type: ignore

            # Add a deterministic variable to combine all effects
            beta_day_of_week_scheduled_starting_hour = pm.Deterministic(
                "beta_day_of_week_scheduled_starting_hour",
                beta_day_of_week_scheduled_starting_hour,
                dims=("day_of_week", "scheduled_starting_hour", "site_id"),
            )

            ########################################################################################
            #               TOTAL CASE VARIANCE IS THE SUM OF INDIVIDUAL PROCEDURE VARIANCES
            ########################################################################################
            # Variance of case duration is modeled as the sum of the variances of each procedure
            # in a case. Since we are working in log space, need to handle variance summation
            # carefully. The first-order Taylor expansion is used to approximate the variance of the
            # log-transformed procedure durations as if they were normally distributed in the log
            # space. It gives an approximation for variance when working in log-transformed space.

            # Index sigma_procedure using the valid procedure indices
            # Masking out invalid (-1) procedures
            valid_sigma_procedure = (
                sigma_procedure[padded_procedure_list_idx] * valid_procedure_mask
            )

            # Calculate variance using the first-order Taylor expansion
            var_x = pm.math.exp(2 * valid_procedure_durations) * valid_sigma_procedure**2

            # Compute variance using Law of Total Variance
            multiprocedure_variance = pm.Deterministic(
                "multiprocedure_variance",
                pm.math.log(
                    pm.math.sum(
                        var_x,
                        axis=1,
                    )
                )
                - 2 * multiprocedure_duration_sum,
            )

            # Compute sigma as square root of variance
            multiprocedure_sigma = pm.math.sqrt(pm.math.exp(multiprocedure_variance))

            ########################################################################################
            #               SURGEON -> VARIANCE
            ########################################################################################
            # The surgeon is allowed to have a +/- impact on variance
            sigma_offset_surgeon = pm.Normal(
                "sigma_offset_surgeon", mu=0, sigma=0.1, dims="surgeon"
            )

            ########################################################################################
            #               SURGEON x PROCEDURE -> VARIANCE
            ########################################################################################
            # The surgeon x procedure pair is allowed to have a +/- impact on variance
            sigma_offset_surgeon_procedure = pm.Normal(
                "sigma_offset_surgeon_procedure",
                mu=0,
                sigma=0.1,
                dims="surgeon_procedure",
            )

            ########################################################################################
            #               LIKELIHOOD PARAMETERS
            ########################################################################################
            # Expected value (mu) of log-duration is the sum of each procedure log-duration
            # +/- synergy effects, +/- all other regressors
            mu = pm.math.log1pexp(
                multiprocedure_duration_sum
                + synergy_effect_sum
                + beta_surgeon[surgeon_idx]
                + beta_surgeon_procedure[surgeon_procedure_idx]
                + beta_outpatient[outpatient_idx, site_id_idx]
                + beta_day_of_week_scheduled_starting_hour[
                    day_of_week_idx, scheduled_starting_hour_idx, site_id_idx
                ]
                + beta_last_case[last_case_idx, site_id_idx]
                + beta_procedure_count[procedure_count_idx, procedure_idx]
                + beta_surgeon_count[surgeon_count_idx, procedure_idx]
                + beta_add_on[add_on_idx, procedure_idx]
                + beta_is_flip_room[is_flip_room_idx, procedure_idx]
                + beta_case_type_short[case_type_short_idx, procedure_idx]
                + beta_to_follow_case[to_follow_case_idx, procedure_idx]
                + beta_num_scheduled_cases[site_id_idx] * num_scheduled_cases
            )

            # Standard deviation (sigma) of log-duration is the square root of total variance of
            # each procedure, +/- surgeon offset, +/- surgeon : procedure offset.
            # Sigma must be greater than zero, so we need to be thoughtful about how we incorporate
            # the offset terms. If we simply add them, negative offset terms could cause sigma to
            # become less than zero, which yields an error. So instead, we allow sigma to shrink or
            # grow by a maximum of 50% using a scaled hyperbolic tangent transformation.
            sigma = (
                multiprocedure_sigma
                * (1 + 0.5 * pm.math.tanh(sigma_offset_surgeon[surgeon_idx]))
                * (1 + 0.5 * pm.math.tanh(sigma_offset_surgeon_procedure[surgeon_procedure_idx]))
            )

            ########################################################################################
            #               LIKELIHOOD
            ########################################################################################
            # This is the final piece of the model. Log-duration is distributed normally with
            # Expected value = mu and standard deviation = sigma as defined above.
            # Use truncated normal to limit extremely small or large predictions
            _y_hat_log = pm.TruncatedNormal(
                "y_hat_log",
                mu=mu,
                sigma=sigma,
                lower=np.log(self.MIN_DURATION_MINUTES),
                upper=np.log(self.MAX_DURATION_MINUTES),
                observed=y_obs_log,
            )

        self.model = model

    def predict(self, df: pd.DataFrame) -> np.typing.NDArray[np.float64]:
        """
        This function: 1) processes an input dataframe, 2) uses set_data() to condition the model
        on the inputted dataframe, 3) uses sample_posterior_predictive() to generate predictions.
        A random_seed is set in sample_posterior_predictive() to ensure reproducibility. If a user
        inputs Surgeon A x Procedure B multiple times, we want to ensure that they receive the same
        exact prediction each time. Setting the random_seed here accomplishes that. Otherwise,
        the same inputs could yield a slightly different prediction each time.
        """
        # First, we preprocess the incoming dataframe
        df = self.preprocess_data(df)

        # Next, we generate the required padded matrices that represent multiprocedures
        # and the potential synergies between procedure pairs in a multiprocedure case
        padded_procedure_list_indices = self.create_padded_procedure_list_indices(df)
        padded_procedure_pair_indices = self.create_padded_procedure_pair_indices(df)

        # Next, we use set_data() to condition the model on the incoming, preprocessed dataframe
        with self.model:
            pm.set_data(
                {
                    "padded_procedure_list_idx": padded_procedure_list_indices,
                    "procedure_pairs_idx": padded_procedure_pair_indices,
                    "procedure_idx": pd.Categorical(
                        df["first_primary_procedure_clean"], categories=self.coords["procedure"]
                    ).codes,
                    "surgeon_idx": pd.Categorical(
                        df["first_primary_surgeon_clean"], categories=self.coords["surgeon"]
                    ).codes,
                    "surgeon_procedure_idx": pd.Categorical(
                        df["first_primary_surgeon_procedure_clean"],
                        categories=self.coords["surgeon_procedure"],
                    ).codes,
                    "site_id_idx": pd.Categorical(
                        df["site_id_clean"], categories=self.coords["site_id"]
                    ).codes,
                    "day_of_week_idx": pd.Categorical(
                        df["day_of_week_clean"], categories=self.coords["day_of_week"]
                    ).codes,
                    "scheduled_starting_hour_idx": pd.Categorical(
                        df["scheduled_starting_hour_clean"],
                        categories=self.coords["scheduled_starting_hour"],
                    ).codes,
                    "outpatient_idx": pd.Categorical(
                        df["outpatient_clean"], categories=self.coords["outpatient"]
                    ).codes,
                    "last_case_idx": pd.Categorical(
                        df["last_case_clean"], categories=self.coords["last_case"]
                    ).codes,
                    "procedure_count_idx": pd.Categorical(
                        df["procedure_count_clean"],
                        categories=self.coords["procedure_count"],
                    ).codes,
                    "surgeon_count_idx": pd.Categorical(
                        df["surgeon_count_clean"],
                        categories=self.coords["surgeon_count"],
                    ).codes,
                    "add_on_idx": pd.Categorical(
                        df["add_on_clean"],
                        categories=self.coords["add_on"],
                    ).codes,
                    "is_flip_room_idx": pd.Categorical(
                        df["is_flip_room_clean"],
                        categories=self.coords["is_flip_room"],
                    ).codes,
                    "case_type_short_idx": pd.Categorical(
                        df["case_type_short_clean"],
                        categories=self.coords["case_type_short"],
                    ).codes,
                    "to_follow_case_idx": pd.Categorical(
                        df["to_follow_case_clean"],
                        categories=self.coords["to_follow_case"],
                    ).codes,
                    "num_scheduled_cases": df["num_scheduled_cases_clean"].values,
                    "y_obs_log": np.zeros(
                        len(df), dtype=int
                    ),  # Dummy values, not used for prediction
                }
            )
            # This is where the predictions are generated by sampling the model's posterior
            # predictive distribution, conditioned on the incoming data
            predictions = pm.sample_posterior_predictive(
                self.idata, random_seed=123, sample_dims=["chain", "draw"], progressbar=False
            )

            # Here we stack all of the sampled predictions into a tall array
            y_hat_log = predictions.posterior_predictive["y_hat_log"].stack(
                sample=("chain", "draw")
            )

            # Transform predictions into the original scale (minutes)
            # by reversing the log+1 transformation
            y_hat = np.exp(y_hat_log.values) - 1
            # Check for NaN values in y_hat. If so, yield a warning and replace them with 0
            if np.isnan(y_hat).any():
                warnings.warn("Warning: NaN values found in the prediction array!", UserWarning)
                y_hat = np.nan_to_num(y_hat, nan=0)

            # Perform postprocessing step
            y_hat = self.postprocess_prediction(y_hat, df)

            return y_hat  # type: ignore

    def predict_from_inference_dict(self, inference_dict: dict[str, str]) -> list[float]:
        """
        This function generates predictions at time of inference. It expects a dictionary
        of the user-selected inputs. It returns a flattened list of predictions rounded to 1
        decimal place.
        """
        inference_df = pd.DataFrame.from_dict([inference_dict])  # type: ignore

        # Build the procedures list
        case_procedure_list = [inference_dict["first_primary_procedure"]]
        if inference_dict["additional_procedures"]:
            case_procedure_list += inference_dict["additional_procedures"]
        inference_df["case_procedure_list"] = [case_procedure_list]

        # Generate the predictions
        y_hat = self.predict(inference_df)

        # Round to 1 decimal place
        y_hat_rounded = np.round(y_hat.astype(np.float64), 1)

        # Flatten array and convert to a list
        y_hat_list = y_hat_rounded.flatten().tolist()

        return cast(List[float], y_hat_list)

    def save_to_local_file(self) -> None:
        """
        Save individual model components to local file for exploration.
        """
        model_file = {}
        model_file["model"] = self.model
        model_file["idata"] = self.idata
        model_file_pickle = cloudpickle.dumps(model_file)
        out_file = open("model_file.pkl", "wb")
        out_file.write(model_file_pickle)
        out_file.close()
