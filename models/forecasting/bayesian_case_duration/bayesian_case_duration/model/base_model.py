from abc import ABC, abstractmethod

import numpy as np
import pandas as pd


class AbstractCaseDurationModel(ABC):
    @abstractmethod
    def predict(self, df: pd.DataFrame) -> np.typing.NDArray[np.float64]:
        """Generates raw predictions based on input features."""
        pass

    @abstractmethod
    def predict_from_inference_dict(self, inference_dict: dict[str, str]) -> list[float]:
        """Generates final predictions from an inference dictionary."""
        pass

    def postprocess_prediction(
        self, y_hat: np.typing.NDArray[np.float64], df: pd.DataFrame
    ) -> np.typing.NDArray[np.float64]:
        """Applies postprocessing logic to raw predictions.

        Checks for large disagreements between median prediction and scheduled duration.
        If a significant difference exists (>= 120 mins and scheduled >= 120 mins),
        it replaces the prediction samples with the adjusted scheduled duration.
        Handles missing scheduled durations by comparing against the median prediction.
        Ensures adjusted scheduled duration is at least 1 minute.
        """
        comparison_df = df.copy().reset_index(drop=True)
        comparison_df["y_hat_median"] = np.median(y_hat, axis=1)

        # Assume implicit turnover, could be configured if needed
        comparison_df["implicit_turnover"] = 30

        # Fill missing scheduled_duration based on prediction median for comparison
        comparison_df["scheduled_duration"] = comparison_df["scheduled_duration"].fillna(
            comparison_df["y_hat_median"] + comparison_df["implicit_turnover"]
        )

        # Calculate adjusted scheduled duration, ensuring it's at least 1 minute
        comparison_df["adjusted_scheduled_duration"] = (
            comparison_df["scheduled_duration"] - comparison_df["implicit_turnover"]
        )

        comparison_df["absolute_difference"] = np.abs(
            comparison_df["y_hat_median"] - comparison_df["adjusted_scheduled_duration"]
        )

        # Is there a big disagreement between Apella prediciton and adj. scheduled duration?
        min_minutes_different = 120

        # Only make adjustments on cases that are scheduled for at least 120 minutes long
        # Short cases are risky, because ambiguous implicit turnover issue becomes more important
        # And adjusted predictions could become negative after subtracting implicit turnover time
        min_scheduled_duration = 120

        # Set big_disagreement = True if thresholds are met
        comparison_df["big_disagreement"] = (
            comparison_df["absolute_difference"] >= min_minutes_different
        ) & (comparison_df["scheduled_duration"] >= min_scheduled_duration)

        big_disagreement_indices = comparison_df[comparison_df["big_disagreement"]].index
        if len(big_disagreement_indices) > 0:
            # IMPORTANT: Copy y_hat to avoid modifying the original array in place
            y_hat_processed = y_hat.copy()
            for i in big_disagreement_indices:
                # Replace all samples for that prediction row
                y_hat_processed[i, :] = comparison_df.iloc[i]["adjusted_scheduled_duration"]
            return y_hat_processed
        else:
            # Return the original y_hat if no disagreements triggered replacement
            return y_hat

    # Add other methods common to both models if needed, e.g.:
    # @abstractmethod
    # def save_to_local_file(self) -> None:
    #     pass
