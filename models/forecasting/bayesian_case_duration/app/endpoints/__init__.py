"""
Defines the API endpoints for all operations
© Apella Inc 2025
"""

import logging

from fastapi import FastAPI
from opentelemetry import trace
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import Tracer<PERSON>rovider
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from serving_utils.timeout_middleware import timeout_middleware

from app.endpoints.predict.predict_api import bayesian_duration_router
from dependencies.dependency_provider import container as AppContainer

# Configure the OTLP exporter
otlp_exporter = OTLPSpanExporter()

# Define the service resource
resource = Resource(attributes={SERVICE_NAME: "bayesian_case_duration"})

# Create a TracerProvider with the resource and processor
tracer_provider = TracerProvider(resource=resource)
span_processor = BatchSpanProcessor(otlp_exporter)
tracer_provider.add_span_processor(span_processor)

# Set the global TracerProvider
trace.set_tracer_provider(tracer_provider)

# --- BEGIN WORKAROUND for uvicorn.access logging level ---
# Get the uvicorn access logger
access_logger = logging.getLogger("uvicorn.access")
# Set its level programmatically, overriding any defaults or potentially failed YAML config
access_logger.setLevel(logging.WARNING)
# --- END WORKAROUND ---

VERSION = "v4"


def create_api() -> FastAPI:
    """Creates the FastAPI application with all routes, instrumentation, and DI."""

    api: FastAPI = FastAPI(
        title="bayesian_case_duration",
        description="Bayesian case duration model service for predicting case duration",
    )

    FastAPIInstrumentor.instrument_app(api)

    api.middleware("http")(timeout_middleware)

    api.include_router(
        bayesian_duration_router,
    )
    api.container = AppContainer  # type: ignore[attr-defined]
    api.container.wire(packages=[__name__])  # type: ignore[attr-defined]
    api.container.base_bayesian_case_duration_service()  # type: ignore[attr-defined]
    return api


__all__ = [
    "create_api",
]
