from http import HTTPStatus

from dependency_injector.wiring import Provide, inject
from fastapi import API<PERSON><PERSON><PERSON>, Depends, Header, Response
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER

from bayesian_case_duration.types import APIInputs
from dependencies.dependency_provider import container as AppContainer
from dependencies.services.base_bayesian_case_duration_service import (
    BaseBayesianCaseDurationService,
)
from utils.types import (
    APICaseRequest,
    LegacyAPIRequest,
    ManyAPICaseRequests,
    ManyCaseOutputs,
    PredictedArrayOutput,
    PredictedCaseOutput,
    PredictedOutput,
)

# This API Input is used by the forecast combiner to simplify calls to it, and enable
# better siloing of responsibilities, this means that the service only requires the case_id
# rather than requiring all the features for prediction


# This is used by the forecast combiner to properly match the outputs to the case
# as well as adding the versioning information to the output
bayesian_duration_router = APIRouter()


@bayesian_duration_router.get(
    "/livez",
    tags=["health"],
    summary="Liveness Check",
    description="Health check endpoint that verifies the service is running.",
)
@inject
def livez() -> Response:
    """Health check endpoint that verifies the service is running."""
    # A liveness probe should simply check if the service is running
    # and not depend on actual prediction traffic
    return Response(status_code=HTTPStatus.OK, content="Service is running")


@bayesian_duration_router.get(
    "/readyz",
    tags=["health"],
    summary="Readiness Check",
    description="Readiness probe that verifies model and device are properly configured.",
)
@inject
async def readyz(
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> Response:
    """Readiness probe that verifies model and device are properly configured."""
    if service.readyz():
        return Response(status_code=HTTPStatus.OK, content="OK")
    else:
        return Response(status_code=HTTPStatus.INTERNAL_SERVER_ERROR, content="Model not loaded")


@bayesian_duration_router.post(
    "/v4/standalone/predict",
    tags=["v4", "predict", "standalone"],
    summary="Predict Case Duration (Median)",
    description="Predicts the median case duration for a standalone case.",
)
@bayesian_duration_router.post(
    "/v3/standalone/predict",
    tags=["legacy"],
    summary="Predict Case Duration (Legacy)",
    description="Legacy endpoint for predicting the median case duration.",
    deprecated=True,
)
@inject
def predict(
    request_input: LegacyAPIRequest,
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> PredictedOutput:
    """Serves up the median prediction. Output is just a single number."""
    y_hat_median, _ = service.generate_predictions(request_input.case_features)
    return PredictedOutput(prediction=y_hat_median)


@bayesian_duration_router.post(
    "/v4/standalone/sample",
    tags=["v4", "sample", "standalone"],
    summary="Sample Case Duration Distribution",
    description="Returns samples from the posterior distribution of case duration for a standalone case.",
)
@bayesian_duration_router.post(
    "/v3/standalone/sample",
    tags=["legacy"],
    summary="Sample Case Duration Distribution (Legacy)",
    description="Legacy endpoint for sampling from the posterior distribution of case duration.",
    deprecated=True,
)
@inject
def predict_array(
    request_input: LegacyAPIRequest,
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> PredictedArrayOutput:
    """
    Serves up the entire list of predictions. Each number in the list is a single
    prediction that was sampled from the posterior predictive distribution of the
    Bayesian model. These can be visualized as histogram, summarized as a range, or
    in other ways downstream.
    """
    _, y_hat_list = service.generate_predictions(request_input.case_features)

    return PredictedArrayOutput(prediction=y_hat_list)


@bayesian_duration_router.post(
    "/predict_case",
    tags=["legacy"],
    summary="Predict for Scheduled Case (Legacy)",
    description="Legacy endpoint for predicting case duration for a scheduled case.",
    deprecated=True,
)
@bayesian_duration_router.post(
    "/v4/schedule/case/sample",
    tags=["v4", "sample", "schedule"],
    summary="Sample Case Duration for Scheduled Case",
    description="Returns samples from the posterior distribution of case duration for a scheduled case.",
)
@inject
def predict_case(
    request: APICaseRequest,
    run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> PredictedCaseOutput:
    return service.predict_many_cases(ManyAPICaseRequests(inputs=[request.inputs]), run_id).root[0]


@bayesian_duration_router.post(
    "/predict_many_cases",
    tags=["legacy"],
    summary="Predict for Multiple Scheduled Cases (Legacy)",
    description="Legacy endpoint for predicting case duration for multiple scheduled cases.",
    deprecated=True,
)
@bayesian_duration_router.post(
    "/v4/schedule/case/sample-many",
    tags=["v4", "sample", "schedule"],
    summary="Sample Case Duration for Multiple Scheduled Cases",
    description="Returns samples from the posterior distribution of case duration for multiple scheduled cases.",
)
@inject
def predict_many_cases(
    requests: ManyAPICaseRequests,
    run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> ManyCaseOutputs:
    return service.predict_many_cases(requests, run_id)


@bayesian_duration_router.post(
    "/test_inputs",
    tags=["utility"],
    summary="Test Input Parsing",
    description="Utility endpoint for testing input parsing.",
)
@inject
def test_inputs(api_request: APICaseRequest) -> APIInputs:
    return APIInputs(**api_request.inputs.model_dump())


@bayesian_duration_router.get(
    "/model_version_deployed",
    tags=["metadata"],
    summary="Get Model Version",
    description="Returns the version of the deployed model.",
)
@inject
def model_version_deployed(
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> str:
    return service.model_version_deployed()


@bayesian_duration_router.get(
    "/version_info",
    tags=["metadata"],
    summary="Get Version Info",
    description="Returns version information about the service.",
)
@inject
def version_info(
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> dict[str, str]:
    return service.version_info()


@bayesian_duration_router.get(
    "/naive_version_info",
    tags=["metadata"],
    summary="Get Naive Version Info",
    description="Returns version information for the naive model fallback.",
)
@inject
def naive_version_info(
    service: BaseBayesianCaseDurationService = Depends(
        Provide[AppContainer.base_bayesian_case_duration_service]
    ),
) -> dict[str, str]:
    return service.naive_version_info()
