from fastapi import FastAPI
from prometheus_fastapi_instrumentator import Instrumentator

from app.endpoints import create_api


def create_app() -> FastAPI:
    """
    Creates the FastAPI application with all routes, instrumentation, and DI.
    """
    app = create_api()

    Instrumentator().instrument(app).expose(app)
    return app


app = create_app()
if __name__ == "__main__":
    import os

    import uvicorn

    port = int(os.environ.get("PORT", 3000))
    uvicorn.run(app, host="0.0.0.0", port=port)
