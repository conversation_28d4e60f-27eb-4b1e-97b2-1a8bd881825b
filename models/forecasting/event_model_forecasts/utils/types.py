from datetime import datetime

from pydantic import BaseModel, RootModel
from serving_utils.utils import VersionedModel


class APIInputs(BaseModel):
    case_id: str

    def to_list(self) -> list[str]:
        return list(vars(self).values())


class ManyInputs(RootModel[list[APIInputs]]):
    def to_list(self) -> list[str]:
        vals = [x.to_list() for x in self.root]
        return [item for row in vals for item in row]


class PredictedOutput(VersionedModel):
    prediction: datetime | None
    case_id: str


class ManyOutputs(RootModel[list[PredictedOutput]]):
    pass


class APIRequest(BaseModel):
    inputs: APIInputs


class ManyAPIRequests(BaseModel):
    inputs: list[APIInputs]
