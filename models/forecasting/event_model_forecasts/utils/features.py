import json
from collections.abc import Set

import pandera as pa
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from feature_store.entity_catalog.event_model_forecast import (
    EventModelForecastSchema,
)
from feature_store.feature_store import FeatureStoreDateTime
from serving_utils.setup_json_logger import setup_json_logger

logger = setup_json_logger(logger_name="event_model_forecasts.features")


class PatientWheelsOutSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    # The room_id is the key
    room_id: pa.typing.Index[str]

    inference_timestamp: FeatureStoreDateTime | None
    patient_wheels_out_timestamp: FeatureStoreDateTime | None = pa.Field(nullable=True)


class EventModelForecastResultSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    # The room_id is the key
    room_id: pa.typing.Index[str]

    patient_wheels_out_forecast: FeatureStoreDateTime | None
    patient_wheels_out_version: str
    patient_wheels_out_service_version: str
    patient_wheels_out_model_version: str
    # TODO: https://linear.app/apella/issue/FORC-81/add-trace-ids-to-forecast-results-schema


class SimpleCaseSchema(pa.DataFrameModel):
    """Schema for getting the room_id for a given case"""

    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]
    room: str | None


def get_features_for_cases(
    feature_store: FeatureStore[CaseSchema], case_ids: Set[str]
) -> pa.typing.DataFrame[SimpleCaseSchema]:
    features_result = feature_store.load_features(case_ids, SimpleCaseSchema)
    if len(features_result.entities_with_missing_features) > 0:
        logger.warning(
            f"Missing features for {len(features_result.entities_with_missing_features)} cases: {features_result.entities_with_missing_features}\n"
            f"Missing features with the following counts {json.dumps(features_result.missing_features_count)}\n"
        )
    return features_result.entities


def get_features_for_rooms(
    feature_store: FeatureStore[EventModelForecastSchema], room_ids: Set[str]
) -> pa.typing.DataFrame[PatientWheelsOutSchema]:
    features_result = feature_store.load_features(room_ids, PatientWheelsOutSchema)
    if len(features_result.entities_with_missing_features) > 0:
        logger.warning(
            f"Missing features for {len(features_result.entities_with_missing_features)} rooms: {features_result.entities_with_missing_features}\n"
            f"Missing features with the following counts {json.dumps(features_result.missing_features_count)}\n"
        )
    return features_result.entities
