version: 1
disable_existing_loggers: False
formatters:
  json:
    class: pythonjsonlogger.jsonlogger.JsonFormatter
    format: '%(asctime)s %(name)s %(levelname)s %(message)s %(processName)s %(pathname)s %(funcName)s'
handlers:
  json:
    class: logging.StreamHandler
    formatter: json
    stream: ext://sys.stdout
loggers:
  uvicorn:
    level: INFO
    handlers:
      - json
  uvicorn.error:
    level: INFO
    handlers:
      - json
    propagate: no
  uvicorn.access:
    level: WARNING
    handlers:
      - json
    propagate: no
  EventModelForecastsService:
    level: INFO
    handlers:
      - json
    propagate: no
root:
  level: INFO
  handlers:
    - json
  propagate: no
