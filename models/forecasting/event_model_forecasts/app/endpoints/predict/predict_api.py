import os
from collections.abc import Set
from datetime import datetime, timedelta, timezone
from http import HTTPStatus

import pandas as pd
import pandera as pa
import serving_utils.config as config
from fastapi import APIRouter, Header, Response
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from feature_store.entity_catalog.event_model_forecast import (
    EVENT_MODEL_FORECASTS_ENTITY_TYPE,
    EventModelForecastSchema,
)
from google.cloud.bigtable import Client as BTClient
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import get_service_version

from utils.features import (
    EventModelForecastResultSchema,
    PatientWheelsOutSchema,
    get_features_for_cases,
    get_features_for_rooms,
)
from utils.types import APIInputs, APIRequest, ManyAPIRequests, ManyOutputs, PredictedOutput

logger = setup_json_logger(logger_name="EventModelForecastService")


class EventModelForecastService:
    def __init__(self) -> None:
        self.is_dev_stub = config.get_dev_stub() == "1"
        self.results_store = None

        bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
        instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])

        self.cases_feature_store = FeatureStore(instance, "cases", CaseSchema)
        self.event_model_forecasts_feature_store = FeatureStore(
            instance, EVENT_MODEL_FORECASTS_ENTITY_TYPE, EventModelForecastSchema
        )
        logger.info("EventModelForecastService initialized")

        if config.get_store_forecast_result():
            self.results_store = FeatureStore(
                instance, "results", EventModelForecastResultSchema, prefix="ml_forecast"
            )

        self.version_information = self.version_info()
        self.naive_version_information = self.naive_version_info()

        self.router = APIRouter(tags=["v3"])

        self.router.add_api_route("/livez", self.livez, methods=["GET"], tags=["Health"])
        self.router.add_api_route("/readyz", self.readyz, methods=["GET"], tags=["Health"])

        self.router.add_api_route("/predict", self.predict, methods=["POST"], tags=["Prediction"])
        self.router.add_api_route(
            "/predict_many", self.predict_many, methods=["POST"], tags=["Prediction"]
        )

        self.router.add_api_route(
            "/version_info", self.version_info, methods=["GET"], tags=["Version"]
        )
        self.router.add_api_route(
            "/naive_version_info", self.naive_version_info, methods=["GET"], tags=["Version"]
        )

        self.router.add_api_route(
            "/model_version_deployed", self.model_version_deployed, methods=["GET"]
        )

    def livez(self) -> Response:
        """Health check endpoint that verifies the service is running."""
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    async def readyz(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        return Response(status_code=HTTPStatus.OK, content="OK")

    def predict(
        self,
        request: APIRequest,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> PredictedOutput:
        return self._predict_many([request.inputs], run_id).root[0]

    def _upsert_forecasts(self, results: list[PredictedOutput]) -> None:
        if self.results_store:
            results_df = pd.DataFrame(
                [
                    {
                        "patient_wheels_out_forecast": result.prediction,
                        "patient_wheels_out_version": result.version,
                        "patient_wheels_out_service_version": result.service_version,
                        "patient_wheels_out_model_version": result.model_version,
                    }
                    for result in results
                ],
                index=[res.case_id for res in results],
            )

            self.results_store.store_features(
                results_df,  # type: ignore
            )

    def _get_validated_event_model_forecasts(
        self, room_ids: Set[str], current_time: datetime
    ) -> pa.typing.DataFrame[PatientWheelsOutSchema]:
        """Get the event model forecasts for the given room_ids, filtering out invalid forecasts.
        (room_ids with no valid patient wheeled out will be dropped here)
        """
        # get the predicted patient wheeled out for each room_id
        room_patient_wheels_out_features = get_features_for_rooms(
            self.event_model_forecasts_feature_store, room_ids
        )

        # drop rows where the predicted patient wheeled out is in the past
        # drop rows where the prediction timestamp is more than 5 minutes old
        cond_future_only = (
            room_patient_wheels_out_features["patient_wheels_out_timestamp"] >= current_time
        )
        cond_recent_only = room_patient_wheels_out_features[
            "inference_timestamp"
        ] >= current_time - timedelta(minutes=5)

        filtered_room_patient_wheels_out_features = room_patient_wheels_out_features[
            cond_future_only & cond_recent_only
        ]
        return pa.typing.DataFrame[PatientWheelsOutSchema](
            filtered_room_patient_wheels_out_features
        )

    def predict_many(
        self,
        request: ManyAPIRequests,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> ManyOutputs:
        return self._predict_many(request.inputs, run_id)

    def _predict_many(self, inputs: list[APIInputs], run_id: str | None = None) -> ManyOutputs:
        requested_case_ids = [input.case_id for input in inputs]
        requested_case_ids_set = set(requested_case_ids)
        logger.info(
            f"Predicting for {len(requested_case_ids)} cases",
            extra={
                "requested_case_ids": requested_case_ids,
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
            },
        )

        cases_room_ids = get_features_for_cases(self.cases_feature_store, requested_case_ids_set)
        room_ids = frozenset(
            room_id for room_id in cases_room_ids["room"].to_list() if room_id is not None
        )

        current_time = datetime.now(timezone.utc)
        room_timestamps = self._get_validated_event_model_forecasts(room_ids, current_time)
        # assign the predicted patient wheeled out to each case_id
        # case_ids with no valid patient wheeled out will be dropped here
        cases_wheels_outs = cases_room_ids.merge(
            room_timestamps,
            how="inner",
            left_on="room",
            right_index=True,
        )

        results = [
            PredictedOutput(
                case_id=case_id,
                prediction=prediction,
                **self.version_information,
            )
            for case_id, prediction in zip(
                cases_wheels_outs.index.to_list(),
                cases_wheels_outs["patient_wheels_out_timestamp"],
            )
        ]
        missing_cases = requested_case_ids_set - set(cases_wheels_outs.index.to_list())
        if len(missing_cases) > 0:
            logger.warning(
                f"Missing {len(missing_cases)} cases",
                extra={
                    "missing_cases": list(missing_cases),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
        for case_id in missing_cases:
            results.append(
                PredictedOutput(
                    case_id=case_id,
                    prediction=None,
                    **self.naive_version_information,
                )
            )

        self._upsert_forecasts(results)

        return ManyOutputs(results)

    def model_version_deployed(self) -> str:
        return "None"

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": "naive_event_model_forcasts_model",
        }
