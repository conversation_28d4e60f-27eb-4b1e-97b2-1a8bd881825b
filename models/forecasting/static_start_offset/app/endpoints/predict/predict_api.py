from __future__ import annotations

import json
import os
from collections.abc import Set
from http import HTTPStatus
from typing import List

import pandas as pd
import pandera as pa
import serving_utils.config as config
from fastapi import APIRouter, Header, Response
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from google.cloud.bigtable import Client as BTClient
from google.cloud.storage import Client as GCSClient
from pydantic import BaseModel, Field, RootModel
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import VersionedModel, get_service_version
from training_utils.model_storage import ModelStorage

from static_start_offset.configs.prod_config import ModelTrainingConfig
from static_start_offset.features import (
    StaticStartOffsetFeatureSchema,
    StaticStartOffsetResultSchema,
)
from static_start_offset.model.inference import StaticStartOffsetInputs

logger = setup_json_logger(logger_name="StaticStartOffsetService")


class APIInputs(BaseModel):
    case_id: str


class APIRequest(BaseModel):
    inputs: APIInputs


class ManyAPIRequests(BaseModel):
    inputs: list[APIInputs]

    def to_list(self) -> list[str]:
        return [x.case_id for x in self.inputs]


class PredictedOffset(VersionedModel):
    prediction: float | None
    case_id: str
    feature_timestamps: dict[str, float] = Field(default={})


class ManyOutputs(RootModel[list[PredictedOffset]]):
    pass


class StaticStartOffsetService:
    """Service class for static start offset model inference."""

    def __init__(self) -> None:
        self.is_dev_stub = config.get_dev_stub() == "1"

        gcs_client = GCSClient()
        model_storage = ModelStorage(
            gcs_client,
            model_type="static_start_offset_model",
            model_identifier="automatic_production_training/2025-05-16",
        )

        logger.info("Loading models. This may take a while")

        if self.is_dev_stub:
            logger.warning("Running in dev stub mode. Not loading models.")
            self.models_by_org_id = {}
            self.locations_to_fit = ["nope"]
            self.model_identifier = "None"

        else:
            logger.info("Loading models. This may take a while")
            self.models_by_org_id = model_storage.load_models(serialization_package_name="dill")
            self.model_training_config = ModelTrainingConfig(**model_storage.json_config)
            logger.info("Done loading models")
            self.locations_to_fit = self.model_training_config.training_config.locations_to_fit
            self.model_identifier = self.model_training_config.model_identifier

        bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
        instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])
        self.feature_store = FeatureStore(instance, "cases", CaseSchema)

        self.results_store = None
        if config.get_store_forecast_result():
            self.results_store = FeatureStore(
                instance, "results", StaticStartOffsetResultSchema, prefix="ml_forecast"
            )

        logger.info("StaticStartOffsetService initialized")

        self.version_information = self.version_info()
        self.naive_version_information = self.naive_version_info()

        self.router = APIRouter()
        self.router.add_api_route("/livez", self.livez, methods=["GET"])
        self.router.add_api_route("/readyz", self.readyz, methods=["GET"])

        self.router.add_api_route("/predict", self.predict, methods=["POST"])
        self.router.add_api_route("/predict_many", self.predict_many, methods=["POST"])

        self.router.add_api_route("/test_inputs", self.test_inputs, methods=["POST"])
        self.router.add_api_route("/supported_org_ids", self.supported_org_ids, methods=["GET"])
        self.router.add_api_route(
            "/model_version_deployed", self.model_version_deployed, methods=["GET"]
        )
        self.router.add_api_route("/version_info", self.version_info, methods=["GET"])
        self.router.add_api_route("/naive_version_info", self.naive_version_info, methods=["GET"])

    def livez(self) -> Response:
        """Health check endpoint that verifies the service is running."""
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    async def readyz(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        if not hasattr(self, "models_by_org_id") or self.models_by_org_id is None:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR, content="Model not loaded"
            )

        return Response(status_code=HTTPStatus.OK, content="OK")

    def get_features_for_cases(
        self, case_ids: Set[str]
    ) -> tuple[pa.typing.DataFrame[StaticStartOffsetFeatureSchema], pd.DataFrame]:
        features_result = self.feature_store.load_features(case_ids, StaticStartOffsetFeatureSchema)
        if len(features_result.entities_with_missing_features) > 0:
            logger.warning(
                f"Missing features for {len(features_result.entities_with_missing_features)} cases: {features_result.entities_with_missing_features}\n"
                f"Missing features with the following counts {json.dumps(features_result.missing_features_count)}\n"
            )
        return features_result.entities, features_result.bigtable_timestamps

    def predict(
        self,
        request: APIRequest,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> PredictedOffset:
        return self._predict_many(ManyAPIRequests(inputs=[request.inputs]), run_id).root[0]

    def predict_many(
        self,
        requests: ManyAPIRequests,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> ManyOutputs:
        return self._predict_many(requests, run_id)

    def _upsert_forecasts(self, results: list[PredictedOffset]) -> None:
        if self.results_store:
            results_df = pd.DataFrame(
                [
                    {
                        "static_start_offset_forecast": result.prediction,
                        "static_start_offset_version": result.version,
                        "static_start_offset_service_version": result.service_version,
                        "static_start_offset_model_version": result.model_version,
                    }
                    for result in results
                ],
                index=[res.case_id for res in results],
            )

            self.results_store.store_features(
                results_df,  # type: ignore
            )

    def _predict_many(self, requests: ManyAPIRequests, run_id: str | None = None) -> ManyOutputs:
        requested_case_ids = requests.to_list()

        if self.is_dev_stub:
            logger.info(
                "Running in dev stub mode. Not loading models.",
                extra={
                    "requested_case_ids": requested_case_ids,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            results = [
                PredictedOffset(
                    case_id=case_id,
                    prediction=3.14,
                    **self.version_information,  # type: ignore[arg-type]
                )
                for case_id in requested_case_ids
            ]
            self._upsert_forecasts(results)

            return ManyOutputs(results)

        requested_case_ids_set = frozenset(requested_case_ids)
        features, bigtable_timestamps = self.get_features_for_cases(requested_case_ids_set)

        if len(features) == 0:
            logger.warning(
                "No features found",
                extra={
                    "requested_case_ids": requested_case_ids_set,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            results = [
                PredictedOffset(
                    case_id=case_id,
                    prediction=None,
                    **self.version_information,  # type: ignore[arg-type]
                )
                for case_id in requested_case_ids_set
            ]

            self._upsert_forecasts(results)

            # We have no features for these cases
            return ManyOutputs(results)

        if len(features["org_id"].unique()) > 1:
            raise Exception("Multiple orgs not supported yet")

        org_id = str(features["org_id"].unique()[0])
        if org_id not in self.models_by_org_id:
            logger.warning(
                "No model found",
                extra={
                    "org_id": org_id,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            # We have no model for these cases
            return ManyOutputs(
                [
                    PredictedOffset(
                        case_id=case_id,
                        prediction=None,
                        **self.version_information,  # type: ignore[arg-type]
                    )
                    for case_id in requested_case_ids_set
                ]
            )

        logger.info(
            f"Predicting for {len(requested_case_ids)} cases",
            extra={
                "requested_case_ids": requested_case_ids,
                "org_id": org_id,
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
            },
        )

        model = self.models_by_org_id[org_id]
        predictions = model.predict(features)

        feature_timestamps = bigtable_timestamps.to_dict(orient="index")

        results = [
            PredictedOffset(
                case_id=case_id,
                prediction=pred,
                feature_timestamps=feature_timestamps[case_id],
                **self.version_information,
            )
            for case_id, pred in zip(features.index.to_list(), predictions)
        ]

        missing_cases = requested_case_ids_set - set(features.index.to_list())

        if len(missing_cases) > 0:
            logger.warning(
                f"Missing {len(missing_cases)} cases",
                extra={
                    "missing_cases": missing_cases,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )

        for case_id in missing_cases:
            results.append(
                PredictedOffset(
                    case_id=case_id,
                    prediction=None,
                    **self.naive_version_information,  # type: ignore[arg-type]
                )
            )

        self._upsert_forecasts(results)

        return ManyOutputs(results)

    def test_inputs(self, request: APIRequest) -> StaticStartOffsetInputs:
        return StaticStartOffsetInputs(**request.inputs.model_dump())

    def supported_org_ids(self) -> List[str]:
        return self.locations_to_fit

    def model_version_deployed(self) -> str:
        return self.model_identifier

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": "naive_static_start_offset_model",
        }
