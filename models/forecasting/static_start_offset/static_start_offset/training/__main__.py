import argparse
import json
import logging
from datetime import datetime
from importlib import import_module

import dill  # type: ignore
from google.cloud.bigquery import Client as BQClient
from google.cloud.secretmanager import SecretManagerServiceClient
from google.cloud.storage import Client as GCSClient
from training_utils.clearml_reporter import (
    ClearMLReporter,
    get_clearml_task,
)
from training_utils.model_storage import CONFIG_FILE_NAME, ModelStorage
from training_utils.utils import ensure_gcp_auth_is_ready, get_repo_info

from static_start_offset.model.evaluator import Evaluator
from static_start_offset.model.static_start_offset_model import StaticStartOffsetModel
from static_start_offset.training.data_selector import DataSelector
from static_start_offset.training.trainer import ModelTrainer

logger = logging.getLogger(__name__)
dill.settings["recurse"] = True

DEFAULT_CONFIG_MODULE = "static_start_offset.configs.prod_config"
MODEL_TYPE = "static_start_offset_model"


def main() -> None:
    # Initialize logging
    parser = argparse.ArgumentParser()

    parser.add_argument("--train", help="Run training", action="store_true")
    parser.add_argument("--evaluate", help="Run training", action="store_true")
    parser.add_argument(
        "--config-module-name",
        type=str,
        help="absolute module name, starting from PACKAGE_ROOT and using '.' as delimiter, not '/', i.e., static_start_offset.configs.experiment_config",
        default=DEFAULT_CONFIG_MODULE,
    )
    parser.add_argument("--is-automated-training", default=False, action="store_true")

    args = parser.parse_args()
    if args.train:
        logger.info("We will run the training")
    else:
        logger.info("Not doing anything")

    print("Starting Model training...")

    # Load utilities
    ensure_gcp_auth_is_ready()
    bq_client = BQClient()
    gcs_client = GCSClient()

    # Load the TrainingConfig
    model_training_config = import_module(args.config_module_name).ModelTrainingConfig()
    if args.is_automated_training:
        # set the variables for an automated fit
        fit_time = datetime.now()
        repo = get_repo_info()
        model_identifier = f"automatic_production_training/{fit_time.date()}"
        model_training_config.clearml_config.project_name = (
            f"{model_training_config.clearml_config.project_name}: Automated Fit"
        )
        model_training_config.clearml_config.task_name = f"{fit_time.date()} Production Fit"
        model_training_config.clearml_config.tags = {
            "date": str(fit_time),
            "branch_name": repo.active_branch.name,
            "sha": repo.head.object.hexsha,
            "short_sha": repo.git.rev_parse(repo.head.object.hexsha, short=7),
            "model_type": MODEL_TYPE,
            "model_identifier": model_identifier,
        }
        model_training_config.model_identifier = model_identifier

    # Load the ClearML credentials
    secret_manager_client = SecretManagerServiceClient()
    clearml_task = get_clearml_task(secret_manager_client, model_training_config.clearml_config)
    reporter = ClearMLReporter(model_training_config.model_dump(), clearml_task)

    # Load the data
    logger.info(" -- selecting the following features in model -- ")
    logger.info(model_training_config.data_selection_config.get_features_description())
    data_selector = DataSelector(bq_client, model_training_config.data_selection_config, reporter)
    data = data_selector.generate_data_for_fit()

    # Define our model
    # TODO: move the features into their own area.
    model = StaticStartOffsetModel(
        model_training_config.forecast_model_config,
        model_training_config.data_selection_config.features,
    )

    # Train our model
    trainer = ModelTrainer(model, model_training_config.training_config, data, reporter)

    model_storage = ModelStorage(
        gcs_client,
        model_type=MODEL_TYPE,
        model_identifier=model_training_config.model_identifier,
    )

    if args.train:
        fit_models = trainer.train()

        for location in model_training_config.training_config.locations_to_fit:
            if location not in fit_models:
                print(f"Skipping model dump for {location} as the model was not fit.")
                continue
            # this one requires dill
            filename = f"{MODEL_TYPE}_{location}.dill"
            local_path = model_storage._get_local_data_file_location(filename)
            local_path.parent.mkdir(parents=True, exist_ok=True)
            with open(local_path, "wb") as io:
                dill.dump(fit_models[location], io)
        # write our config out as well
        config_local_file_path = model_storage._get_local_data_file_location(CONFIG_FILE_NAME)
        json.dump(model_training_config.model_dump(), open(config_local_file_path, "w"))
        model_storage.upload_to_google_storage()

    if args.evaluate:
        print("Evaluating model")
        fit_models = model_storage.load_models(serialization_package_name="dill")
        # Evaluate results
        evaluator = Evaluator(
            fit_models, trainer.df_test, model_training_config.training_config, reporter
        )
        df_evaluation_results = evaluator.evaluate_models()
        df_evaluation_results["model_identifier"] = model_training_config.model_identifier
        evaluator.upload_evaluation_data(bq_client, df_evaluation_results)


if __name__ == "__main__":
    main()
