import logging
from datetime import date, timedelta

import pandas as pd
from google.cloud.bigquery import Client as BQClient
from training_utils.clearml_reporter import Clear<PERSON>Reporter

from static_start_offset.configs.prod_config import DataSelectionConfig

logger = logging.getLogger(__name__)


class DataSelector:
    def __init__(
        self,
        bq_client: BQClient,
        config: DataSelectionConfig,
        reporter: ClearMLReporter,
    ):
        self.bq_client = bq_client
        self.config = config
        self.reporter = reporter

    def generate_data_for_fit(self) -> pd.DataFrame:
        df = self.query_for_data()
        df = self.filter_data(df)
        self.check_for_data_recency(df)
        # Keep only the desired fields:
        # NOTE: we need to add the outcome_var
        df = df[
            self.config.feature_names
            + [
                "case_date",
                "scheduled_start_datetime_local",
                "business_unit",
                "org_id",
                "start_offset",
                "case_id",
            ]
        ]
        print("site_ids in full dataset: ", sorted(list(df["site_id"].unique())))
        return df

    def filter_data(self, df: pd.DataFrame) -> pd.DataFrame:
        df = self.filter_addons(df)
        if self.config.filter_out_reordered_cases:
            df = self.filter_reordered_cases(df)
        if self.config.elim_high_abs_start_offset:
            df = self.filter_high_abs_offset(df)
        return df

    def filter_high_abs_offset(self, df: pd.DataFrame) -> pd.DataFrame:
        in_length = len(df)
        df = df[(df["abs_start_offset"] < self.config.max_absolute_start_offset)]
        out_length = len(df)
        logger.info(f"Removed {in_length - out_length} days due to high absolute start offset")
        return df

    def filter_reordered_cases(self, df: pd.DataFrame) -> pd.DataFrame:
        in_length = len(df)
        # in the following two cases, ruff will try to change them to "is True" and "not is True",
        # both of which are incorrect because what we'd like is an array of True/False

        # this does not pass tests when there are nulls b/c of issues defining a "null" boolean value
        # however, this works with real data
        df = df[df["is_num_phases_match_num_cases"] | df["is_num_phases_match_num_cases"].isnull()]
        df = df[
            ~df["case_has_experienced_reordering"] | df["case_has_experienced_reordering"].isnull()
        ]

        # ... the below works with None values in a test set, but filters out everything when using real data.
        # so, just eliminate testing for filter_reordered_cases for now
        # df = df[
        #     [x is None or x is True for x in df["is_num_phases_match_num_cases"]]
        # ]  # True or null
        # df = df[
        #     [x is None or not x for x in df["case_has_experienced_reordering"]]
        # ]  # False or null

        out_length = len(df)
        logger.info(f"Removed {in_length - out_length} days due to re-ordered cases")
        return df

    def filter_addons(self, df: pd.DataFrame) -> pd.DataFrame:
        # TODO: Write a decorate to measure before/after
        in_length = len(df)
        df = df[df["add_on"] != 1]
        out_length = len(df)
        logger.info(f"Removed {in_length - out_length} days due to add-on cases")
        return df

    def check_for_data_recency(self, df: pd.DataFrame) -> None:
        dfg = (
            df.groupby("org_id")["scheduled_start_datetime_local"]
            .agg(["min", "max", "count"])
            .reset_index()
        )
        self.reporter.report_dataframe_as_table(
            dfg, report_group="Data Preparation", table_name="Data Recency Table"
        )

        # check we have more than 400 data points
        # check that we have a year's worth of data.
        # TODO: calculate the actual max date from the dataset, not the config
        six_months_ago = date.today() - timedelta(days=180)
        assert dfg["min"].max() < pd.Timestamp(six_months_ago), "Data too recent for fit"
        assert (dfg["count"] > 400).all(), "Not enough data present"

    def query_for_data(self) -> pd.DataFrame:
        query_string = self.bq_query_string()
        data_df = self.bq_client.query(query_string).to_dataframe()
        return data_df

    def bq_query_string(self) -> str:
        return """
                  SELECT
                    case_id,
                    site_id,
                    org_id,
                    business_unit,
                    room,
                    date_of_surgery as case_date,
                    scheduled_start_datetime_local,
                    scheduled_end_datetime_local,
                    add_on,
                    start_offset,
                    abs(start_offset) as abs_start_offset,
                    first_case,
                    scheduled_starting_hour,
                    minutes_after_previous_case_scheduled_end,
                    cumsum_scheduled_case_duration_so_far,
                    to_follow_case,
                    first_primary_surgeon,
                    number_first_cases_started_at_same_time,
                    is_num_phases_match_num_cases,
                    case_has_experienced_reordering
                  FROM `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
                  WHERE 
                    ((site_id = "HMH-OPC19" and date_of_surgery >= "2022-09-15") or (site_id != "HMH-OPC19"))
                """
