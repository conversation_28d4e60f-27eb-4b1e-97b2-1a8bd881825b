import math
from collections.abc import Mapping
from datetime import datetime, timezone
from typing import <PERSON>ple

import matplotlib.pyplot as plt
import numpy as np
import numpy.typing as npt
import pandas as pd
from google.api_core.retry import Retry
from google.cloud.bigquery import Client as B<PERSON><PERSON>lient
from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON><PERSON>, Table, TimePartitioning, TimePartitioningType
from sklearn.pipeline import Pipeline
from training_utils.clearml_reporter import ClearMLReporter

from static_start_offset.configs.prod_config import TrainingConfig


class Evaluator:
    def __init__(
        self,
        models: Mapping[str, Pipeline],
        df_test: pd.DataFrame,
        training_config: TrainingConfig,
        reporter: ClearMLReporter,
    ):
        self.models = models
        self.df_test = df_test
        self.reporter = reporter
        self.training_config = training_config

    def evaluate_models(self) -> pd.DataFrame:
        # evaluatation 1 - Residuals
        df_model_eval_mae, df_individual_evaluation_results = (
            self.compute_and_plot_model_residuals()
        )
        # evaluation 2 - "Schedule"
        df_eval_mae = self.compute_schedule_error(df_model_eval_mae)

        # Report single values to form bar chart in Scatters tab
        for location in df_eval_mae[self.training_config.location_type_to_evaluate].unique():
            self.reporter.clearml_task.get_logger().report_single_value(
                name=f"ΔMAE - {location}",
                value=df_eval_mae.loc[
                    df_eval_mae[self.training_config.location_type_to_evaluate] == location,
                    "model_improvement",
                ].values[0],
            )

        self.reporter.report_dataframe_as_table(
            df_eval_mae.sort_values(self.training_config.location_type_to_evaluate).set_index(
                self.training_config.location_type_to_evaluate
            ),
            report_group="Validation",
            table_name="Model Improvement over Schedule",
        )

        return df_individual_evaluation_results

    def compute_and_plot_model_residuals(self) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Compute model residuals and evaluation metrics.

        This method calculates the Mean Absolute Error (MAE) for the static start offset
        prediction model across different locations. It processes the test data, computes
        evaluation metrics, and prepares the results in a format suitable for reporting
        and further analysis.

        Returns:
            pd.DataFrame: A DataFrame containing MAE values for each location. The DataFrame
            has the following columns:
            - location_type_to_evaluate: The location identifier
            - mae: The Mean Absolute Error for the corresponding location

        This method also populates the model_eval_mae_dict and model_eval_accuracy_dict
        instance variables with MAE and accuracy values respectively for each location.
        """
        # model evaluation
        now_timestamp = datetime.now(tz=timezone.utc).isoformat()  # for writing predictions to DWH
        ds_date = datetime.now(tz=timezone.utc).date()
        df_individual_evaluation_results = pd.DataFrame()

        model_eval_mae_dict = {}
        model_eval_accuracy_dict = {}
        for location in self.training_config.evaluation_to_fit_mapping:
            df_test_site = self.df_test[
                self.df_test[self.training_config.location_type_to_evaluate] == location
            ]

            if df_test_site.empty:
                print(f"Skipping fit for {location} as the test site is empty.")
                continue

            # set the model we fit.
            if (
                self.training_config.location_type_to_evaluate
                != self.training_config.location_type_to_fit
            ):
                if self.training_config.evaluation_to_fit_mapping[location] not in self.models:
                    print(
                        f"Skipping fit for {self.training_config.evaluation_to_fit_mapping[location]} as the model was not fit."
                    )
                    continue

                print(
                    f"loading model for {self.training_config.evaluation_to_fit_mapping[location]}"
                )
                fit_pipe = self.models[self.training_config.evaluation_to_fit_mapping[location]]
            else:
                if location in self.models:
                    fit_pipe = self.models[location]
                elif "all" in self.models:
                    fit_pipe = self.models["all"]
                else:
                    # we did not fit for this site
                    continue

            # if one model was fit for all sites, use the saved start_rf_pipe
            y_actual = df_test_site[self.training_config.outcome_column]
            df_test_site = df_test_site.drop(columns=[self.training_config.outcome_column])
            y_pred = fit_pipe.predict(df_test_site)
            df_test_site[self.training_config.outcome_column] = y_actual
            df_test_site["prediction"] = y_pred
            df_test_site["error"] = y_pred - y_actual

            df_test_site["ds"] = ds_date
            df_test_site["training_run_at"] = now_timestamp

            cols_to_upload = [
                "case_id",
                "org_id",
                "site_id",
                "start_offset",
                "prediction",
                "error",
                "training_run_at",
                "ds",
            ]

            df_individual_evaluation_results = pd.concat(
                [df_individual_evaluation_results, df_test_site[cols_to_upload]]
            )

            rmse, mean_abs_error = self.compute_error(y_actual.to_numpy(), y_pred)

            self.plot_residuals(
                y_actual.to_numpy(), y_pred, f"Residuals for {location}", rmse, mean_abs_error
            )

            model_eval_mae_dict[location] = mean_abs_error
            accuracy = 100 * np.mean(np.abs(y_pred - y_actual) <= 30)
            model_eval_accuracy_dict[location] = accuracy

            self.reporter.report_dataframe_as_table(
                df_test_site[[self.training_config.outcome_column, "prediction"]].corr(),
                report_group="Validation",
                table_name=f"correlation between actual and pred start offset for {location}",
            )

        # this needs to be cleaned up
        df_model_eval_mae = pd.DataFrame.from_dict(
            model_eval_mae_dict, orient="index"
        ).reset_index()
        return df_model_eval_mae, df_individual_evaluation_results

    def compute_error(
        self, y_actual: npt.NDArray[np.float64], y_pred: npt.NDArray[np.float64]
    ) -> Tuple[float, float]:
        if y_actual.size == 0 or y_pred.size == 0:
            MSE = float("nan")
            mean_abs_error = float("nan")
        else:
            MSE = np.square(np.subtract(y_actual, y_pred)).mean()
            mean_abs_error = np.abs(np.subtract(y_actual, y_pred)).mean()
        return math.sqrt(MSE), mean_abs_error

    def compute_schedule_error(self, df_model_eval_mae: pd.DataFrame) -> pd.DataFrame:
        """
        Compute the error metrics for the schedule (baseline) predictions.

        This method calculates the Mean Absolute Error (MAE) and accuracy for each location
        using the scheduled start times as predictions (i.e., assuming no offset).

        The results are then compared with the model's predictions to evaluate the improvement
        achieved by the model over the schedule. (This is done by saying the model predicted no changes in the results and seeing how off the "0" start offset would be.)

        Returns:
            None: The results are reported directly to the ClearML reporter.

        Side effects:
            - Computes MAE and accuracy for schedule predictions
            - Merges schedule and model evaluation results
            - Calculates model improvement metrics
            - Reports the comparison results to ClearML
        """

        schedule_eval_mae_dict = {}
        schedule_eval_accuracy_dict = {}
        for location in self.training_config.evaluation_to_fit_mapping:
            df_test_site = self.df_test[
                self.df_test[self.training_config.location_type_to_evaluate] == location
            ]
            y_pred = np.zeros(len(df_test_site))
            y_actual = df_test_site[self.training_config.outcome_column]

            rmse, mean_abs_error = self.compute_error(y_pred, y_actual.to_numpy())
            schedule_eval_mae_dict[location] = mean_abs_error

            accuracy = 100 * np.mean(
                np.abs(df_test_site[self.training_config.outcome_column]) <= 30
            )
            schedule_eval_accuracy_dict[location] = accuracy

        df_schedule_eval_mae = pd.DataFrame.from_dict(
            schedule_eval_mae_dict, orient="index"
        ).reset_index()
        df_model_eval_mae.columns = pd.Index(
            [self.training_config.location_type_to_evaluate, "mae"]
        )
        df_schedule_eval_mae.columns = pd.Index(
            [self.training_config.location_type_to_evaluate, "mae"]
        )

        df_eval_mae = df_model_eval_mae.merge(
            df_schedule_eval_mae,
            on=self.training_config.location_type_to_evaluate,
            suffixes=("_model", "_schedule"),
        )
        df_eval_mae["model_improvement"] = df_eval_mae["mae_schedule"] - df_eval_mae["mae_model"]

        print(df_eval_mae)
        return df_eval_mae

    def plot_residuals(
        self,
        y_pred: npt.NDArray[np.float64],
        y_actual: npt.NDArray[np.float64],
        model_name: str,
        rmse_val: float,
        mean_abs_error: float,
    ) -> None:
        plt.scatter(x=y_actual, y=y_pred, alpha=0.5)
        plt.plot(y_actual, y_actual, color="k")
        plt.xlabel("actual")
        plt.ylabel("predicted")
        plt.title(
            model_name
            + " \n actual vs predicted for test set, \n RMSE = "
            + str(round(rmse_val))
            + " mins, mean abs error = "
            + str(round(mean_abs_error))
            + " mins, \n",
            fontsize=15,
        )
        self.reporter.add_figure_to_report(figure_title=model_name, figure=plt)

    def start_offset_evaluation_schema(self) -> list[SchemaField]:
        return [
            SchemaField("case_id", "STRING", mode="REQUIRED"),
            SchemaField("org_id", "STRING", mode="REQUIRED"),
            SchemaField("site_id", "STRING", mode="REQUIRED"),
            SchemaField("start_offset", "INTEGER", mode="REQUIRED"),
            SchemaField("prediction", "FLOAT", mode="REQUIRED"),
            SchemaField("error", "FLOAT", mode="REQUIRED"),
            SchemaField("training_run_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("ds", "DATE", mode="REQUIRED"),
            SchemaField("model_identifier", "STRING", mode="REQUIRED"),
        ]

    def upload_evaluation_data(self, bq_client: BQClient, df: pd.DataFrame) -> None:
        table_id = "prod-data-platform-027529.case_forecasting.start_offset_model_training_results"
        table = Table(
            table_id,
            schema=self.start_offset_evaluation_schema(),
        )
        table.time_partitioning = TimePartitioning(
            type_=TimePartitioningType.DAY,
            field="ds",
        )
        table = bq_client.create_table(table, exists_ok=True, retry=Retry(deadline=60))
        bq_client.insert_rows_from_dataframe(table, df)
