from dataclasses import dataclass

from pydantic import BaseModel, ConfigDict
from training_utils.clearml_reporter import ClearMLBaseModel


@dataclass
class FeatureClass:
    # Our features fall into numeric or categorical, this is needed for fits.
    name: str
    data_type: str  # numeric or categorical


class ModelConfig(BaseModel):
    iterations: int = 500
    learning_rate: float = 0.1
    depth: int = 10


class TrainingConfig(BaseModel):
    # model fitting
    do_hyperparam_tuning: bool = True
    do_feature_selection: bool = True

    fit_one_model_for_all_sites: bool = False
    outcome_column: str = "start_offset"
    location_type_to_fit: str = "org_id"  # site_id, business_unit, or org_id
    location_type_to_evaluate: str = "site_id"  # site_id, business_unit, or org_id
    locations_to_fit: list[str] = [
        "houston_methodist",
        "tampa_general",
        "north_bay",
        "health_first",
        "nyu",
        "lifebridge",
    ]
    evaluation_to_fit_mapping: dict[str, str] = {
        "HMH-OPC18": "houston_methodist",
        "HMH-OPC19": "houston_methodist",
        "HMH-WT03": "houston_methodist",
        "HMH-DUNN03": "houston_methodist",
        "HMH-DUNN06": "houston_methodist",
        "HMH-MAIN03": "houston_methodist",
        "HMH-HMBA": "houston_methodist",
        "HMH-LD06": "houston_methodist",
        "HMH-HMW-LD": "houston_methodist",
        "HMH-HMW-OR": "houston_methodist",
        "HMH-HMBT-ASC": "houston_methodist",
        "HMH-HMBT-LD": "houston_methodist",
        "HMH-HMBT-OR": "houston_methodist",
        "HMH-HMCL-ASC": "houston_methodist",
        "HMH-HMCL-CBC": "houston_methodist",
        "HMH-HMCL-OR": "houston_methodist",
        "HMH-HMCY-CATH": "houston_methodist",
        "HMH-HMCY-ENDO": "houston_methodist",
        "HMH-HMCY-IR": "houston_methodist",
        "HMH-HMCY-LD": "houston_methodist",
        "HMH-HMCY-OR": "houston_methodist",
        "HMH-HMSL-LD": "houston_methodist",
        "HMH-HMSL-OR": "houston_methodist",
        "HMH-HMTW": "houston_methodist",
        "HMH-HMWB-C": "houston_methodist",
        "HMH-HMWB-N": "houston_methodist",
        "HMH-HMWB-S": "houston_methodist",
        "TGH-MAIN02": "tampa_general",
        "TGH-CVTOR03": "tampa_general",
        "HF-VH02": "health_first",
        "nb_medical_center_fairfield": "north_bay",
        "NYU-LI4": "nyu",
        "NYU-KP4": "nyu",
        "NYU-KP5": "nyu",
        "NYU-KP5-CATH": "nyu",
        "LBHS-GOR": "lifebridge",
        # "LBHS-RIAO": "lifebridge"
    }
    # defines train/test split
    test_on_most_recent_x_days: int = 7


class DataSelectionConfig(BaseModel):
    # TODO: Clean this up once we've reproduced what we have
    # data filtering
    filter_out_reordered_cases: bool = True

    # data quality. if start offset is more than this, elim from train and test
    elim_high_abs_start_offset: bool = True
    max_absolute_start_offset: int = 250

    # NOTE: if features change, reset the indices of the categorical features in model pipeline
    features: list[FeatureClass] = [
        FeatureClass(name="scheduled_starting_hour", data_type="numeric"),
        FeatureClass(name="cumsum_scheduled_case_duration_so_far", data_type="numeric"),
        FeatureClass(name="minutes_after_previous_case_scheduled_end", data_type="numeric"),
        FeatureClass(name="to_follow_case", data_type="categorical"),
        FeatureClass(name="first_primary_surgeon", data_type="categorical"),
        FeatureClass(name="site_id", data_type="categorical"),
    ]

    @property
    def feature_names(self) -> list[str]:
        return [feat.name for feat in self.features]

    def get_features_description(self) -> str:
        features_string = ""
        for feature in self.features:
            features_string += f"\n - {feature.name}"
        return features_string

    def get_features_list(self, feature_data_type: str = "") -> list[str]:
        features_list = []
        for feature in self.features:
            if feature_data_type != "":
                if feature.data_type == feature_data_type:
                    features_list.append(feature.name)
            else:
                features_list.append(feature.name)
        return features_list

    # set titles for plots
    def get_config_description(self) -> str:
        description = ""

        if self.filter_out_reordered_cases:
            description += "\n elim reordered cases"
        else:
            description += "\n kept reordered cases"

        return description


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "Static Start Offset Model"
    task_name: str = "Experiment Training: Catboost with StringTransformer"
    tags: dict[str, str] = {"params": "100"}


class ModelTrainingConfig(BaseModel):
    forecast_model_config: ModelConfig = ModelConfig()
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "experiment_model_saving_bucket"

    # See https://docs.pydantic.dev/latest/api/config/#pydantic.config.ConfigDict.protected_namespaces
    model_config = ConfigDict(protected_namespaces=())
