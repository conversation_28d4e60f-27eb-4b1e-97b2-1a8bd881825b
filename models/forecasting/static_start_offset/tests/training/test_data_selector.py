from datetime import datetime, timed<PERSON>ta
from unittest.mock import MagicMock

import pandas as pd
import pytest

# clients
from google.cloud.bigquery import Client as BQ<PERSON>lient
from training_utils.clearml_reporter import ClearMLReporter

from static_start_offset.configs.prod_config import DataSelectionConfig
from static_start_offset.training.data_selector import DataSelector

# def test_generate_data_for_fit
# def test_filter_data


@pytest.fixture
def mock_bq_client() -> BQClient:
    """
    Fixture that simulates BQ client
    """
    bq_client = MagicMock(BQClient)
    return bq_client


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    """
    Fixture to simulate the reporter
    """
    reporter = MagicMock(ClearMLReporter)
    return reporter


@pytest.fixture
def default_config() -> DataSelectionConfig:
    return DataSelectionConfig()


@pytest.fixture
def default_selector(
    mock_bq_client: BQClient,
    default_config: DataSelectionConfig,
    mock_reporter: ClearMLReporter,
) -> DataSelector:
    return DataSelector(mock_bq_client, default_config, mock_reporter)


@pytest.fixture
def selector_with_bq_data(
    default_config: DataSelectionConfig, mock_reporter: ClearMLReporter
) -> DataSelector:
    bq_client = MagicMock(BQClient)
    # Yes, this is what that query is currently reporting back.
    bq_return_values = [
        {
            "case_id": "85c68966-a04b-446d-be08-b8e05d161dda",
            "business_unit": "Fairfield",
            "site_id": "Fairfield",
            "org_id": "north_bay",
            "room": "NB-OR3",
            "case_date": "2023-11-13",
            "scheduled_start_datetime_local": datetime(2022, 10, 13, 19, 15, 0),
            "scheduled_end_datetime_local": "2022-10-13T20:00:00",
            "add_on": "0",
            "start_offset": -30,
            "scheduled_starting_hour": 19,
            "scheduled_duration": 45,
            "minutes_after_previous_case_scheduled_end": 30,
            "cumsum_scheduled_case_duration_so_far": 430,
            "first_case": 0,
            "to_follow_case": 0,
            "first_primary_surgeon": "BIRKBECK, DANIEL",
            "day_of_week": 5,
            "is_weekend": "false",
            "abs_start_offset": 30,
            "month_first_date": "2022-10-01",
            "quarter_first_date": "2022-10-01",
            "recent_surgeon_avg_start_offset": 70.0,
            "recent_room_avg_start_offset": 38.38360655737705,
            "apella_data": "1",
            "first_case_1": "0",
            "is_last_case": "true",
            "case_order": "6",
            "cumsum_scheduled_turnover": "1170",
            "surgeon_first_case_today_scheduled_starting_hour": "19",
            "surgeon_diff_case_starting_hour_vs_first_case_today": "0",
            "surgeon_case_order": "1",
            "prev_flip_case_id": None,
            "has_prev_flip_case": "0",
            "num_prev_flip_cases_for_surgeon": "0",
            "prev_first_primary_surgeon_init": "DUDLEY, BENJAMIN",
            "turnover_type": "short_turnover",
            "days_since_jan1_2022": "285",
            "days_before_today": "389",
            "first_primary_procedure": "I \u0026 D FINGER",
            "procedure_contains_robot": "0",
            "prev_first_primary_procedure": "TURBT",
            "actual_minus_scheduled_duration": "14",
            "service_line": None,
            "is_num_phases_match_num_cases": True,
            "case_has_experienced_reordering": False,
            "mean_scheduled_case_duration_so_far": "86.0",
            "prev_first_primary_surgeon": "DUDLEY, BENJAMIN",
            "is_prev_surgeon_same": "0",
            "is_prev_first_primary_procedure_same": "0",
        },
        {
            "case_id": "f8a9f0f5-2232-4884-b96b-3853abfedb0b",
            "business_unit": "Fairfield",
            "site_id": "Fairfield",
            "org_id": "north_bay",
            "room": "NB-OR4",
            "case_date": "2022-10-04",
            "scheduled_start_datetime_local": datetime(2022, 10, 4, 7, 45, 0),
            "scheduled_end_datetime_local": "2022-10-04T09:45:00",
            "add_on": "0",
            "start_offset": 9,
            "scheduled_starting_hour": 7,
            "scheduled_duration": 120,
            "minutes_after_previous_case_scheduled_end": 1000,
            "cumsum_scheduled_case_duration_so_far": 0,
            "first_case": 1,
            "to_follow_case": 0,
            "first_primary_surgeon": "BROOKS, ANDREW",
            "day_of_week": 3,
            "is_weekend": "false",
            "abs_start_offset": 9,
            "month_first_date": "2022-10-01",
            "quarter_first_date": "2022-10-01",
            "recent_surgeon_avg_start_offset": 44.103448275862071,
            "recent_room_avg_start_offset": 35.036496350364963,
            "apella_data": "1",
            "first_case_1": "1",
            "is_last_case": "false",
            "case_order": "1",
            "cumsum_scheduled_turnover": "1000",
            "surgeon_first_case_today_scheduled_starting_hour": "7",
            "surgeon_diff_case_starting_hour_vs_first_case_today": "0",
            "surgeon_case_order": "1",
            "prev_flip_case_id": None,
            "has_prev_flip_case": "0",
            "num_prev_flip_cases_for_surgeon": "0",
            "prev_first_primary_surgeon_init": None,
            "turnover_type": "long_turnover",
            "days_since_jan1_2022": "276",
            "days_before_today": "398",
            "first_primary_procedure": "KNEE ARTHROPLASTY JRP   TOTAL REPLACEMEN",
            "procedure_contains_robot": "0",
            "prev_first_primary_procedure": None,
            "actual_minus_scheduled_duration": "42",
            "service_line": None,
            "is_num_phases_match_num_cases": True,
            "case_has_experienced_reordering": False,
            "mean_scheduled_case_duration_so_far": "0.0",
            "prev_first_primary_surgeon": "Placeholder, Surgeon",
            "is_prev_surgeon_same": "0",
            "is_prev_first_primary_procedure_same": "0",
        },
    ] * 400  # need more than 400 so it wont crash
    bq_client.query().to_dataframe.return_value = pd.DataFrame(bq_return_values)
    return DataSelector(bq_client, default_config, mock_reporter)


def test_generate_data_for_fit(selector_with_bq_data: DataSelector) -> None:
    df = selector_with_bq_data.generate_data_for_fit()
    # print(df)
    assert len(df) == 800
    # check the columns
    expected_columns = [
        "case_date",
        "scheduled_start_datetime_local",
        "business_unit",
        "site_id",
        "org_id",
        "case_id",
        "start_offset",
        "scheduled_starting_hour",
        "cumsum_scheduled_case_duration_so_far",
        "minutes_after_previous_case_scheduled_end",
        "to_follow_case",
        "first_primary_surgeon",
    ]
    assert sorted(df.columns) == sorted(expected_columns)


def test_filter_data(default_selector: DataSelector) -> None:
    # this test doesn't really do anything that isn't tested elsewhere, so just
    # make sure the proper methods are called
    df = pd.DataFrame(
        [
            {
                "add_on": 0,
                "is_num_phases_match_num_cases": True,
                "case_has_experienced_reordering": False,
                "business_unit": "OPC19",
                "abs_start_offset": 200,
            }
        ]
    )
    default_selector.config.filter_out_reordered_cases = True
    default_selector.config.elim_high_abs_start_offset = True
    # everything should be called
    df2 = default_selector.filter_data(df)
    assert pd.DataFrame.equals(df2, df)


def test_filter_high_abs_offset(default_selector: DataSelector) -> None:
    full_data = list(range(300))  # nmbers from 0 to 299
    df = pd.DataFrame(full_data)
    df.columns = pd.Index(["abs_start_offset"])
    assert len(df) == 300
    default_selector.config.max_absolute_start_offset = 100
    df_100 = default_selector.filter_high_abs_offset(df)
    assert len(df_100) == 100
    default_selector.config.max_absolute_start_offset = 250
    df_250 = default_selector.filter_high_abs_offset(df)
    assert len(df_250) == 250


def test_filter_reordered_cases(default_selector: DataSelector) -> None:
    # ideally, this will also test null values, but could not come up with
    # a way to denote "null boolean" here to pass the current filtering in
    # filter_reordered_cases. Testing with real data confirms that the filter works
    full_data = [
        {
            "is_num_phases_match_num_cases": True,
            "case_has_experienced_reordering": True,
        },
        {
            "is_num_phases_match_num_cases": True,
            "case_has_experienced_reordering": False,
        },
        {
            "is_num_phases_match_num_cases": False,
            "case_has_experienced_reordering": True,
        },
        {
            "is_num_phases_match_num_cases": False,
            "case_has_experienced_reordering": False,
        },
    ]
    df = pd.DataFrame(full_data)
    assert len(df) == 4
    df_filtered = default_selector.filter_reordered_cases(df)
    assert len(df_filtered) == 1


def test_filter_addons(default_selector: DataSelector) -> None:
    num_not_addon = 10
    num_addon = 5
    full_data = [{"add_on": 0}] * num_not_addon + [{"add_on": 1}] * num_addon
    df = pd.DataFrame(full_data)
    assert len(df) == num_not_addon + num_addon
    df_filter = default_selector.filter_addons(df)
    assert len(df_filter) == num_not_addon


def test_check_for_data_recency(default_selector: DataSelector) -> None:
    # create a fake dataset
    data = [
        {"org_id": "OPC19", "scheduled_start_datetime_local": datetime.now()},
        {
            "org_id": "OPC19",
            "scheduled_start_datetime_local": datetime.now() - timedelta(days=10),
        },
        {
            "org_id": "OPC19",
            "scheduled_start_datetime_local": datetime.now() - timedelta(days=300),
        },
    ] * 100
    df = pd.DataFrame(data)
    with pytest.raises(AssertionError) as excinfo:
        default_selector.check_for_data_recency(df)
        default_selector.reporter.report_dataframe_as_table.assert_called()  # type: ignore
        assert str(excinfo.value) == "Data too recent for fit"

    # check for the age of the data
    data = [
        {"org_id": "OPC19", "scheduled_start_datetime_local": datetime.now()},
        {
            "org_id": "OPC19",
            "scheduled_start_datetime_local": datetime.now() - timedelta(days=10),
        },
        {
            "org_id": "OPC19",
            "scheduled_start_datetime_local": datetime.now() - timedelta(days=370),
        },
    ] * 150
    # this should not raise
    df = pd.DataFrame(data)
    default_selector.check_for_data_recency(df)
    default_selector.reporter.report_dataframe_as_table.assert_called()  # type: ignore
