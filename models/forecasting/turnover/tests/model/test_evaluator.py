from unittest.mock import MagicMock, patch

import numpy as np
import numpy.typing as npt
import pandas as pd
import pytest
from google.cloud.bigquery import Client as BQClient
from sklearn.pipeline import Pipeline
from training_utils.clearml_reporter import Clear<PERSON>Reporter

from turnover.configs.prod_config import TrainingConfig
from turnover.model.evaluator import Evaluator
from turnover.model.inference import ModelInference


@pytest.fixture
def mock_reporter() -> ClearMLReporter:
    """
    Fixture to simulate the reporter
    """
    reporter = MagicMock(ClearMLReporter)
    return reporter


@pytest.fixture
def mock_bq_client() -> BQClient:
    """
    Fixture to simulate the BQclient
    """
    bq_client = MagicMock(BQClient)
    return bq_client


@pytest.fixture
def mock_pipeline() -> Pipeline:
    """
    Fixture to simulate the reporter
    """
    pipeline = MagicMock(Pipeline)
    return pipeline


@pytest.fixture
def mock_model_inference(mock_pipeline: Pipeline) -> ModelInference:
    """
    Fixture to simulate the reporter
    """
    model_inference = ModelInference(mock_pipeline)
    return model_inference


@pytest.fixture
def default_training_config() -> TrainingConfig:
    training_config = TrainingConfig()
    training_config.locations_to_fit = ["HMH-OPC19"]
    training_config.location_type_to_fit = "site_id"
    training_config.outcome_variables = [
        "turnover_before_case",
        "turnover_after_case",
        "turnover_open_before_case",
        "turnover_clean_after_case",
    ]
    training_config.eliminate_high_scheduled_turnovers_in_testing = False
    return training_config


def get_fake_preds(self: Evaluator, df_test_location: pd.DataFrame) -> npt.NDArray[np.float64]:
    fake_preds = np.array([5, 20])
    return fake_preds


# raw test set includes null values in actual turnovers; nulls are filtered out inside compute_model_residuals()
# so that there should only be predicted values corresponding to the non-null actual turnovers
fake_df_test = pd.DataFrame(
    [
        {
            "site_id": "HMH-OPC19",
            "org_id": "houston_methodist",
            "case_id": "1",
            "first_primary_procedure": "A PROCEDURE",
            "first_primary_surgeon": "A SURGEON",
            "turnover_minutes": 10,
            "scheduled_turnover_minutes": 10,
            "flip_block_case": 1,
            "turnover_before_case": np.nan,  # first case doesn't have a previous turnover
            "turnover_after_case": 10,
            "turnover_open_before_case": 1,
            "turnover_clean_after_case": 5,
            "first_case_by_actual_start": 1,
            "first_case": 1,
            "scheduled_duration": 70,
            "room_id": "room1",
            "actual_start_datetime_local": "2023-11-30 10:00:00",
        },
        {
            "site_id": "HMH-OPC19",
            "org_id": "houston_methodist",
            "case_id": "2",
            "first_primary_procedure": "A PROCEDURE",
            "first_primary_surgeon": "A SURGEON",
            "turnover_minutes": 10,
            "scheduled_turnover_minutes": 10,
            "flip_block_case": 1,
            "turnover_before_case": 5,
            "turnover_after_case": 10,
            "turnover_open_before_case": 1,
            "turnover_clean_after_case": 5,
            "first_case_by_actual_start": 0,
            "first_case": 0,
            "scheduled_duration": 70,
            "room_id": "room1",
            "actual_start_datetime_local": "2023-11-30 11:00:00",
        },
        {
            "site_id": "HMH-OPC19",
            "org_id": "houston_methodist",
            "case_id": "3",
            "first_primary_procedure": "A PROCEDURE",
            "first_primary_surgeon": "A SURGEON",
            "turnover_minutes": 10,
            "scheduled_turnover_minutes": 10,
            "flip_block_case": 1,
            "turnover_before_case": 40,
            "turnover_after_case": np.nan,  # no turnover after a last case
            "turnover_open_before_case": 1,
            "turnover_clean_after_case": np.nan,
            "first_case_by_actual_start": 0,
            "first_case": 0,
            "scheduled_duration": 70,
            "room_id": "room1",
            "actual_start_datetime_local": "2023-11-30 12:00:00",
        },
    ]
)

fake_custom_turnover_after_cutoffs = pd.DataFrame(
    {"site_id": "HMH-OPC19", "custom_cutoff": 60}, index=[0]
)


@pytest.fixture
def default_evaluator(
    default_training_config: TrainingConfig,
    mock_reporter: ClearMLReporter,
    mock_bq_client: BQClient,
) -> Evaluator:
    models = {
        "houston_methodist": {
            "turnover_before_case": MagicMock(),
            "turnover_after_case": MagicMock(),
            "turnover_open_before_case": MagicMock(),
            "turnover_clean_after_case": MagicMock(),
        }
    }
    default_evaluator = Evaluator(
        models=models,
        df_test=fake_df_test,
        custom_turnover_after_cutoffs=fake_custom_turnover_after_cutoffs,
        training_config=default_training_config,
        reporter=mock_reporter,
        bq_client=mock_bq_client,
    )
    return default_evaluator


@patch.object(ModelInference, "predict_with_df", get_fake_preds)
def test_compute_model_residuals(default_evaluator: Evaluator) -> None:
    df_model_mape, df_model_mae, _, _, _, _ = default_evaluator.compute_model_residuals()

    # these are percent errors and absolute errors of non-null actuals
    # here, the errors are for the second and third cases in fake_df_test
    #   first case is ignored b/c there is no turnover_before_case for a first case
    assert round(df_model_mape["turnover_before_case"][0]) == round((0 + 50) / 2)
    assert round(df_model_mae["turnover_before_case"][0]) == round((0 + 20) / 2)

    # here, the errors are for the first and second cases in fake_df_test
    #   the first case is retained b/c the outcome variable is not turnover_open_before_case
    assert round(df_model_mape["turnover_after_case"][0]) == round((50 + 100) / 2)
    assert round(df_model_mae["turnover_after_case"][0]) == round((5 + 10) / 2)

    assert len(df_model_mape["turnover_before_case"]) == 1
    assert len(df_model_mae["turnover_before_case"]) == 1


def test_compute_eval_metrics(default_evaluator: Evaluator) -> None:
    y_actual = np.array([10.0, 1.0])
    y_pred = np.array([5.0, 1.5])

    mape, mae = default_evaluator.compute_eval_metrics(y_actual, y_pred)

    assert mape == 50.0
    assert mae == 2.75
