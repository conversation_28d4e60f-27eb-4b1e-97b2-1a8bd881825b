import os
from http import HTTPStatus
from typing import List

import pandas as pd
import serving_utils.config as config
from fastapi import API<PERSON><PERSON><PERSON>, Header, Response
from feature_store import FeatureStore
from feature_store.entity_catalog import CaseSchema
from google.cloud.bigtable import Client as BTClient
from google.cloud.storage import Client as GCSClient
from pydantic import BaseModel, Field, RootModel
from serving_utils.consts import X_APELLA_DAGSTER_RUN_ID_HEADER
from serving_utils.setup_json_logger import setup_json_logger
from serving_utils.utils import VersionedModel, get_service_version
from training_utils.model_storage import ModelStorage

from turnover.configs.prod_config import ModelTrainingConfig
from turnover.features import TurnoverDurationResultSchema, get_features_for_cases
from turnover.model.inference import ListInputs, TurnoverInputs

logger = setup_json_logger(logger_name="TurnoverService")


class APIInputs(BaseModel):
    org_id: str
    first_primary_surgeon: str
    first_primary_procedure: str


class APIRequest(BaseModel):
    inputs: APIInputs


class APICaseInputs(BaseModel):
    case_id: str


class APICaseRequest(BaseModel):
    # This API Input is used by the forecast combiner to simplify calls to it, and enable
    # better siloing of responsibilities, this means that the service only requires the case_id
    # rather than requiring all the features for prediction
    inputs: APICaseInputs


class ManyAPIRequests(BaseModel):
    inputs: list[APIInputs]

    def to_turnover_inputs(self) -> ListInputs:
        list_of_turnovers = [TurnoverInputs(**val.model_dump()) for val in self.inputs]
        return ListInputs(list_of_turnovers)

    def to_df(self) -> pd.DataFrame:
        return self.to_turnover_inputs().to_df()


class ManyAPICaseRequests(BaseModel):
    inputs: list[APICaseInputs]

    def to_list(self) -> list[APICaseInputs]:
        return self.inputs


class PredictedOutput(BaseModel):
    prediction_turnover_before_case: float | None
    prediction_turnover_after_case: float | None
    prediction_turnover_open_before_case: float | None
    prediction_turnover_clean_after_case: float | None


# This is used by the forecast combiner to properly match the outputs to the case
# as well as adding the versioning information to the output
class PredictedCaseOutput(VersionedModel):
    turnover_duration: float | None
    case_id: str
    feature_timestamps: dict[str, float] = Field(default={})


class ManyOutputs(RootModel[list[PredictedOutput]]):
    pass


class ManyCaseOutputs(RootModel[list[PredictedCaseOutput]]):
    pass


class TurnoverService:
    """Service class for turnover duration model inference."""

    def __init__(self) -> None:
        self.is_dev_stub = config.get_dev_stub() == "1"

        gcs_client = GCSClient()
        model_storage = ModelStorage(
            gcs_client,
            # TODO: need to set model_type in one place. current it is set here and in the training main.py
            model_type="turnover_model",
            model_identifier="automatic_production_training/2025-05-16",
        )

        logger.info("Loading models. This may take a while")

        self.results_store = None

        if self.is_dev_stub:
            logger.warning("Running in dev stub mode. Not loading models.")
            self.models_by_location = {}
            self.location_type_to_fit = "org_id"
            self.locations_to_fit = ["nope"]
            self.model_identifier = "None"

        else:
            model_storage.load_config_file()
            self.model_training_config = ModelTrainingConfig(**model_storage.json_config)
            self.models_by_location = model_storage.load_nested_models(
                self.model_training_config.training_config.locations_to_fit,
                self.model_training_config.training_config.outcome_variables,
            )
            logger.info("Done loading models")
            self.location_type_to_fit = (
                self.model_training_config.training_config.location_type_to_fit
            )
            self.locations_to_fit = self.model_training_config.training_config.locations_to_fit
            self.model_identifier = self.model_training_config.model_identifier

            bigtable_client = BTClient(project=os.environ["FEATURE_STORE_PROJECT"])
            instance = bigtable_client.instance(os.environ["FEATURE_STORE_INSTANCE"])
            self.feature_store = FeatureStore(instance, "cases", CaseSchema)
            if config.get_store_forecast_result():
                self.results_store = FeatureStore(
                    instance, "results", TurnoverDurationResultSchema, prefix="ml_forecast"
                )

        # TODO: find a better way to determine which orgs we trained the model for
        if self.location_type_to_fit != "org_id":
            logger.error(
                "Invalid location type for model",
                extra={"location_type": self.location_type_to_fit, "expected_type": "org_id"},
            )
            raise ValueError("Service only works for models trained by org")

        logger.info("TurnoverService initialized")
        self.version_information = self.version_info()
        self.naive_version_information = self.naive_version_info()

        self.router = APIRouter()
        self.router.add_api_route("/livez", self.livez, methods=["GET"])
        self.router.add_api_route("/readyz", self.readyz, methods=["GET"])

        self.router.add_api_route("/predict", self.predict, methods=["POST"])
        self.router.add_api_route("/predict_many", self.predict_many, methods=["POST"])
        self.router.add_api_route("/predict_case", self.predict_case, methods=["POST"])
        self.router.add_api_route("/predict_many_cases", self.predict_many_cases, methods=["POST"])

        self.router.add_api_route("/test_inputs", self.test_inputs, methods=["POST"])
        self.router.add_api_route("/supported_org_ids", self.supported_org_ids, methods=["GET"])
        self.router.add_api_route(
            "/model_version_deployed", self.model_version_deployed, methods=["GET"]
        )
        self.router.add_api_route("/version_info", self.version_info, methods=["GET"])
        self.router.add_api_route("/naive_version_info", self.naive_version_info, methods=["GET"])

    def livez(self) -> Response:
        """Health check endpoint that verifies the service is running."""
        # A liveness probe should simply check if the service is running
        # and not depend on actual prediction traffic
        return Response(status_code=HTTPStatus.OK, content="Service is running")

    async def readyz(self) -> Response:
        """Readiness probe that verifies model and device are properly configured."""
        if not hasattr(self, "models_by_location") or self.models_by_location is None:
            return Response(
                status_code=HTTPStatus.INTERNAL_SERVER_ERROR, content="Model not loaded"
            )

        return Response(status_code=HTTPStatus.OK, content="OK")

    def predict(
        self,
        request: APIRequest,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> PredictedOutput:
        return self._predict_many(ManyAPIRequests(inputs=[request.inputs]), run_id).root[0]

    def predict_many(
        self,
        requests: ManyAPIRequests,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> ManyOutputs:
        return self._predict_many(requests, run_id)

    def predict_case(
        self,
        request: APICaseRequest,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> PredictedCaseOutput:
        return self._predict_many_cases(ManyAPICaseRequests(inputs=[request.inputs]), run_id).root[
            0
        ]

    def predict_many_cases(
        self,
        requests: ManyAPICaseRequests,
        run_id: str | None = Header(None, alias=X_APELLA_DAGSTER_RUN_ID_HEADER),
    ) -> ManyCaseOutputs:
        return self._predict_many_cases(requests, run_id)

    def _upsert_forecasts(self, results: list[PredictedCaseOutput]) -> None:
        if self.results_store:
            results_df = pd.DataFrame(
                [
                    {
                        "turnover_duration_forecast": result.turnover_duration,
                        "turnover_duration_version": result.version,
                        "turnover_duration_service_version": result.service_version,
                        "turnover_duration_model_version": result.model_version,
                    }
                    for result in results
                ],
                index=[result.case_id for result in results],
            )

            self.results_store.store_features(
                results_df,  # type: ignore
            )

    def _predict_many(self, requests: ManyAPIRequests, run_id: str | None = None) -> ManyOutputs:
        if self.is_dev_stub:
            return ManyOutputs(
                [
                    PredictedOutput(
                        prediction_turnover_before_case=2.72,
                        prediction_turnover_after_case=2.72,
                        prediction_turnover_open_before_case=2.72,
                        prediction_turnover_clean_after_case=2.72,
                    )
                    for _ in requests.inputs
                ]
            )

        inference_inputs = requests.to_df()
        org_id_set = set([val.org_id for val in requests.inputs])

        if len(org_id_set) != 1:
            logger.error(
                "Multiple orgs in request",
                extra={
                    "org_ids": list(org_id_set),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            raise Exception("Multiple orgs not supported yet")
        org_id = [*org_id_set][0]

        if org_id not in self.models_by_location:
            logger.error(
                "No model found",
                extra={
                    "org_id": org_id,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            raise Exception(f"Org {org_id} is not supported")
        models = self.models_by_location[org_id]

        prediction_turnover_before = models["turnover_before_case"].predict(inference_inputs)
        prediction_turnover_after = models["turnover_after_case"].predict(inference_inputs)
        prediction_turnover_open = models["turnover_open_before_case"].predict(inference_inputs)
        prediction_turnover_clean = models["turnover_clean_after_case"].predict(inference_inputs)
        # this model will always produce a result
        output_list = []
        for index in range(len(prediction_turnover_before)):
            output_list.append(
                PredictedOutput(
                    prediction_turnover_before_case=prediction_turnover_before[index],
                    prediction_turnover_after_case=prediction_turnover_after[index],
                    prediction_turnover_open_before_case=prediction_turnover_open[index],
                    prediction_turnover_clean_after_case=prediction_turnover_clean[index],
                )
            )
        logger.info(
            "Predicting for surgeon, procedure combo",
            extra={
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                "inputs": requests.inputs,
            },
        )
        return ManyOutputs(output_list)

    def _predict_many_cases(
        self, requests: ManyAPICaseRequests, run_id: str | None = None
    ) -> ManyCaseOutputs:
        requested_case_ids = [input_row.case_id for input_row in requests.inputs]

        if self.is_dev_stub:
            logger.info(
                "Running in dev stub mode. Not loading models.",
                extra={
                    "requested_case_ids": requested_case_ids,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            result = [
                PredictedCaseOutput(
                    turnover_duration=2.72,
                    case_id=input_row.case_id,
                    **self.version_information,  # type: ignore[arg-type]
                )
                for input_row in requests.inputs
            ]
            self._upsert_forecasts(result)

            return ManyCaseOutputs(result)

        requested_case_ids_set = set(requested_case_ids)

        features, bigtable_timestamps = get_features_for_cases(
            self.feature_store, requested_case_ids_set
        )

        if len(features) == 0:
            logger.warning(
                f"No features found for {len(requested_case_ids_set)} cases",
                extra={
                    "case_ids": requested_case_ids,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            output_list = []
            for case_id in requested_case_ids_set:
                output_list.append(
                    PredictedCaseOutput(
                        turnover_duration=None,
                        case_id=case_id,
                        **self.version_information,  # type: ignore[arg-type]
                    )
                )

            self._upsert_forecasts(output_list)

            return ManyCaseOutputs(output_list)
        org_id_set = set(features.org_id.to_list())

        if len(org_id_set) > 1:
            logger.error(
                "Multiple orgs in request",
                extra={
                    "case_ids": requested_case_ids,
                    "orgs": list(org_id_set),
                    "num_orgs": len(org_id_set),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            raise Exception("Multiple orgs not supported yet")

        feature_timestamps = bigtable_timestamps.to_dict(orient="index")

        logger.info(
            "Predicting turnover duration for cases",
            extra={
                "case_ids": requested_case_ids,
                "org_id": list(org_id_set)[0],
                X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
            },
        )
        output_list = []
        org_id = [*org_id_set][0]
        if org_id not in self.models_by_location:
            logger.warning(
                f"No model available for org {org_id}",
                extra={
                    "available_orgs": list(self.models_by_location.keys()),
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )
            output_list = []
            for case_id in requested_case_ids_set:
                output_list.append(
                    PredictedCaseOutput(
                        turnover_duration=None,
                        case_id=case_id,
                        **self.version_information,  # type: ignore[arg-type]
                    )
                )

            return ManyCaseOutputs(output_list)

        missing_case_ids = requested_case_ids_set - set(features.index.to_list())

        models = self.models_by_location[org_id]

        prediction_turnover_after = models["turnover_after_case"].predict(features)

        # this model will always produce a result
        for case_id, index in zip(features.index.to_list(), range(len(prediction_turnover_after))):
            output_list.append(
                PredictedCaseOutput(
                    turnover_duration=prediction_turnover_after[index],
                    case_id=case_id,
                    feature_timestamps=feature_timestamps[case_id],
                    **self.version_information,
                )
            )

        if len(missing_case_ids) > 0:
            logger.warning(
                f"Missing {len(missing_case_ids)} cases",
                extra={
                    "case_ids": missing_case_ids,
                    X_APELLA_DAGSTER_RUN_ID_HEADER: run_id,
                },
            )

        for case_id in missing_case_ids:
            output_list.append(
                PredictedCaseOutput(
                    turnover_duration=None,
                    case_id=case_id,
                    **self.naive_version_information,  # type: ignore[arg-type]
                )
            )

        self._upsert_forecasts(output_list)

        return ManyCaseOutputs(output_list)

    def test_inputs(self, api_request: APIRequest) -> TurnoverInputs:
        return TurnoverInputs(**api_request.inputs.model_dump())

    def supported_org_ids(self) -> List[str]:
        return self.locations_to_fit

    def model_version_deployed(self) -> str:
        return self.model_identifier

    def version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---{self.model_version_deployed()}",
            "service_version": get_service_version(),
            "model_version": self.model_version_deployed(),
        }

    def naive_version_info(self) -> dict[str, str]:
        return {
            "version": f"{get_service_version()}---naive",
            "service_version": get_service_version(),
            "model_version": "naive_turnover_model",
        }
