from collections.abc import Mapping
from datetime import datetime, timezone
from typing import Dict, <PERSON><PERSON>

import numpy as np
import numpy.typing as npt
import pandas as pd
from google.api_core.retry import Retry
from google.cloud.bigquery import Client as BQClient
from google.cloud.bigquery import <PERSON><PERSON><PERSON><PERSON>ield, Table, TimePartitioning, TimePartitioningType
from sklearn.pipeline import Pipeline
from training_utils.clearml_reporter import ClearMLReporter

from turnover.configs.prod_config import TrainingConfig
from turnover.model.inference import ModelInference


class Evaluator:
    def __init__(
        self,
        models: Mapping[str, Pipeline],
        df_test: pd.DataFrame,
        custom_turnover_after_cutoffs: pd.DataFrame,
        training_config: TrainingConfig,
        reporter: ClearMLReporter,
        bq_client: BQClient,
    ):
        self.models = models
        self.df_test = df_test
        self.reporter = reporter
        self.training_config = training_config
        self.bq_client = bq_client

        self.df_test = self.df_test.merge(custom_turnover_after_cutoffs, on="site_id", how="left")
        self.df_test["custom_cutoff"] = self.df_test["custom_cutoff"].fillna(
            self.training_config.default_turnover_limit
        )

    def evaluate_models(self) -> pd.DataFrame:
        (
            df_model_mape,
            df_model_mae,
            df_testing_counts,
            df_first_case_testing_counts,
            df_minimum_turnover_mae,
            df_turnover_predictions_to_upload,
        ) = self.compute_model_residuals()

        # Report single values to form bar chart in Scatters tab
        for location in df_minimum_turnover_mae[
            self.training_config.location_type_to_evaluate
        ].unique():
            self.reporter.clearml_task.get_logger().report_single_value(
                name=f"ΔMAE - {location}",
                value=df_minimum_turnover_mae.loc[
                    df_minimum_turnover_mae[self.training_config.location_type_to_evaluate]
                    == location,
                    "model_improvement_over_heuristic",
                ].values[0],
            )

        self.reporter.report_dataframe_as_table(
            df_testing_counts.set_index(self.training_config.location_type_to_evaluate),
            report_group="Validation",
            table_name="Turnover Counts in Test Set",
        )

        self.reporter.report_dataframe_as_table(
            df_first_case_testing_counts.set_index(self.training_config.location_type_to_evaluate),
            report_group="Validation",
            table_name="Turnover Counts for First Cases in Test Set",
        )

        self.reporter.report_dataframe_as_table(
            df_minimum_turnover_mae.set_index(self.training_config.location_type_to_evaluate),
            report_group="Validation",
            table_name="Minimum Turnover Heuristic MAE: compare to turnover_after_case",
        )

        self.reporter.report_dataframe_as_table(
            df_model_mape.set_index(self.training_config.location_type_to_evaluate),
            report_group="Validation",
            table_name="MAPE",
        )

        self.reporter.report_dataframe_as_table(
            df_model_mae.set_index(self.training_config.location_type_to_evaluate),
            report_group="Validation",
            table_name="MAE",
        )

        return df_turnover_predictions_to_upload

    def compute_model_residuals(
        self,
    ) -> tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """
        Compute model residuals and evaluation metrics.

        This method calculates various evaluation metrics for the turnover prediction model,
        including Mean Absolute Percentage Error (MAPE) and Mean Absolute Error (MAE) for
        different turnover types across locations. It also computes counts for turnovers
        in the test set and evaluates a minimum turnover heuristic.

        Returns:
            tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame, pd.DataFrame]:
                - df_model_mape: DataFrame containing MAPE values for each location and turnover type
                - df_model_mae: DataFrame containing MAE values for each location and turnover type
                - df_testing_counts: DataFrame containing counts of turnovers in the test set
                - df_first_case_testing_counts: DataFrame containing counts of first case turnovers in the test set
                - df_minimum_turnover_mae: DataFrame containing MAE values for the minimum turnover heuristic

        This method processes the test data, computes evaluation metrics, and prepares
        the results in a format suitable for reporting and further analysis.
        """
        # model evaluation
        now_timestamp = datetime.now(tz=timezone.utc).isoformat()  # for writing predictions to DWH
        ds_date = datetime.now(tz=timezone.utc).date()
        df_turnover_predictions_to_upload = pd.DataFrame()

        model_eval_mape_dict: Dict[str, Dict[str, float]] = {}
        model_eval_mae_dict: Dict[str, Dict[str, float]] = {}
        minimum_turnover_eval_mae_dict: Dict[str, Dict[str, float]] = {}
        testing_counts_dict: Dict[str, Dict[str, float]] = {}
        testing_first_case_counts_dict: Dict[str, Dict[str, float]] = {}

        # approximates minimum turnover heuristic in forecasting
        # if forecasted duration < 30 min, minimum turnover after the case is 20 min
        # if forecasted duration between 30 and 60, minimum turnover after the case is 25 min
        # if forecasted duration > 60 min, minimum turnover after the case is 30
        # we don't have forecasted duration in this pipeline, so use scheduled duration instead
        self.df_test["minimum_turnover_heuristic_after_case"] = self.df_test[
            "scheduled_duration"
        ].apply(lambda x: 20 if x < 30 else (25 if 30 <= x <= 60 else 30))

        for location in self.training_config.evaluation_to_fit_mapping:
            model_eval_mape_dict[location] = {}
            model_eval_mae_dict[location] = {}
            minimum_turnover_eval_mae_dict[location] = {}
            testing_counts_dict[location] = {}
            testing_first_case_counts_dict[location] = {}

            df_location_pred_values = self.df_test[
                (self.df_test[self.training_config.location_type_to_evaluate] == location)
            ][["case_id"]]  # save predicted values to compare

            # evaluate minimum turnover heuristic, which applies a turnover duration to
            # based on the forecasted duration of the previous case
            # compare to error for turnover_after_case
            df_test_location_minimum_turnover = self.df_test[
                (self.df_test[self.training_config.location_type_to_evaluate] == location)
                & ~self.df_test["turnover_after_case"].isna()
            ]

            if self.training_config.eliminate_high_scheduled_turnovers_in_testing:
                df_test_location_minimum_turnover = df_test_location_minimum_turnover[
                    df_test_location_minimum_turnover["scheduled_turnover_minutes"]
                    <= self.training_config.scheduled_turnover_duration_limit
                ]

            if self.training_config.eliminate_high_actual_turnovers_in_testing:
                df_test_location_minimum_turnover = df_test_location_minimum_turnover[
                    (
                        df_test_location_minimum_turnover["turnover_after_case"]
                        <= df_test_location_minimum_turnover["custom_cutoff"]
                    )
                    & (df_test_location_minimum_turnover["turnover_after_case"] >= 0)
                ]

            _, minimum_turnover_mae = self.compute_eval_metrics(
                np.array(df_test_location_minimum_turnover["turnover_after_case"]),
                np.array(
                    df_test_location_minimum_turnover["minimum_turnover_heuristic_after_case"]
                ),
            )
            minimum_turnover_eval_mae_dict[location]["minimum_turnover_mae"] = minimum_turnover_mae

            for outcome_variable in self.training_config.outcome_variables:
                # don't eliminate null y values before this b/c different outcome variables
                # will be null for different rows
                df_test_location = self.df_test[
                    (self.df_test[self.training_config.location_type_to_evaluate] == location)
                    & ~self.df_test[outcome_variable].isna()
                ]

                if (
                    self.training_config.eliminate_first_case_for_turnover_open
                    and outcome_variable == "turnover_open_before_case"
                ):
                    # this eliminates first cases by scheduled start time, IF it's the turnover_open_before_case
                    df_test_location = df_test_location[df_test_location["first_case"] == 0]

                if self.training_config.eliminate_high_scheduled_turnovers_in_testing:
                    df_test_location = df_test_location[
                        df_test_location["scheduled_turnover_minutes"]
                        <= self.training_config.scheduled_turnover_duration_limit
                    ]

                if self.training_config.eliminate_high_actual_turnovers_in_testing:
                    # If we are fitting the turnover_open, take out turnovers where the full turnover before the case
                    # was over the limit. If we are fitting hte turnover_clean, take out turnovers where the full
                    # turnover after the case was over the limit.
                    if outcome_variable == "turnover_open_before_case":
                        outcome_variable_for_duration_limit = "turnover_before_case"
                    elif outcome_variable == "turnover_clean_after_case":
                        outcome_variable_for_duration_limit = "turnover_after_case"
                    else:
                        outcome_variable_for_duration_limit = outcome_variable
                    df_test_location = df_test_location[
                        (
                            df_test_location[outcome_variable_for_duration_limit]
                            <= df_test_location["custom_cutoff"]
                        )
                        & (df_test_location[outcome_variable] >= 0)
                    ]
                if len(df_test_location) == 0:
                    print(f"No turnovers found for {location} and {outcome_variable}")
                    break

                testing_counts_dict[location][outcome_variable] = len(df_test_location)
                testing_first_case_counts_dict[location][outcome_variable] = len(
                    df_test_location[df_test_location["first_case_by_actual_start"] == 1]
                )

                # set the model we fit.
                fit_pipe = self.models[self.training_config.evaluation_to_fit_mapping[location]][
                    outcome_variable
                ]

                # if one model was fit for all sites, use the saved start_rf_pipe
                y_actual = df_test_location[outcome_variable]
                df_test_location = df_test_location.drop(columns=[outcome_variable])
                model_inference = ModelInference(fit_pipe)

                y_pred = model_inference.predict_with_df(df_test_location)

                df_predicted_values_location_outcome = pd.DataFrame(
                    {
                        "case_id": df_test_location["case_id"],
                        "predicted_" + outcome_variable: y_pred,
                    }
                )

                df_location_pred_values = df_location_pred_values.merge(
                    df_predicted_values_location_outcome, on="case_id", how="left"
                )

                df_test_location[outcome_variable] = y_actual
                df_test_location["prediction"] = y_pred
                df_test_location["error"] = y_pred - y_actual
                df_test_location["actual"] = y_actual

                if outcome_variable == "turnover_after_case":
                    df_test_location.sort_values("prediction").to_csv(
                        "df_turnover_predictions_"
                        + location
                        + "_outcome_"
                        + outcome_variable
                        + ".csv"
                    )

                # Upload turnover evaluation metrics to BigQuery
                df_test_location["model_type"] = "turnover"
                df_test_location["ds"] = ds_date
                df_test_location["turnover_type"] = outcome_variable
                df_test_location["training_run_at"] = now_timestamp
                cols_to_upload = [
                    "case_id",
                    "org_id",
                    "site_id",
                    "actual",
                    "prediction",
                    "turnover_type",
                    "minimum_turnover_heuristic_after_case",
                    "error",
                    "model_type",
                    "training_run_at",
                    "ds",
                ]

                df_turnover_predictions_to_upload = pd.concat(
                    [df_turnover_predictions_to_upload, df_test_location[cols_to_upload]]
                )

                # eliminate zero-value actuals because MAPE calculation will fail with division by zero
                # make sure we're not eliminating too many turnovers with this filter
                if outcome_variable in ["turnover_before_case", "turnover_after_case"]:
                    # only do this check for full turnovers, not ones that require a back table
                    # ASC sites often have many cases that don't need a back table, and will fail this test
                    # for turnover_open_before_case and turnover_clean_after_case
                    if sum(df_test_location["actual"] == 0) > 0.1 * len(df_test_location):
                        print(
                            f"WARNING: More than 10% of turnovers are 0 for {location} and {outcome_variable}"
                        )
                df_test_location = df_test_location[df_test_location["actual"] != 0]

                mape, mae = self.compute_eval_metrics(
                    np.array(df_test_location["actual"]), np.array(df_test_location["prediction"])
                )

                model_eval_mape_dict[location][outcome_variable] = mape
                model_eval_mae_dict[location][outcome_variable] = mae

            # within each location, check whether predicted turnover_open > predicted turnover before case
            # or whether predicted turnover_clean > predicted turnover after case (this shouldn't happen)
            if {
                "predicted_turnover_before_case",
                "predicted_turnover_open_before_case",
                "predicted_turnover_after_case",
                "predicted_turnover_clean_after_case",
            }.issubset(df_location_pred_values.columns):
                idx_turnover_before_lower_than_turnover_open = (
                    df_location_pred_values["predicted_turnover_before_case"]
                    < df_location_pred_values["predicted_turnover_open_before_case"]
                )
                idx_turnover_after_lower_than_turnover_clean = (
                    df_location_pred_values["predicted_turnover_after_case"]
                    < df_location_pred_values["predicted_turnover_clean_after_case"]
                )

                total_cases = len(df_location_pred_values)
                pct_turnover_before_lower_than_turnover_open = (
                    100 * sum(idx_turnover_before_lower_than_turnover_open) / total_cases
                )
                pct_turnover_after_lower_than_turnover_clean = (
                    100 * sum(idx_turnover_after_lower_than_turnover_clean) / total_cases
                )

                if (
                    pct_turnover_before_lower_than_turnover_open > 0
                    or pct_turnover_after_lower_than_turnover_clean > 0
                ):
                    df_location_pred_values[
                        idx_turnover_before_lower_than_turnover_open
                        | idx_turnover_after_lower_than_turnover_clean
                    ].to_csv("df_location_pred_values_" + location + "_bad_cases.csv")

                # TODO: once we begin training on historical data, turn on these assert statements
                #   ticket to turn on historical data: DATA-2970
                # assert pct_turnover_before_lower_than_turnover_open < 5, (
                #     location
                #     + " predicted turnover before is sometimes lower than predicted turnover open"
                # )
                # assert pct_turnover_after_lower_than_turnover_clean < 5, (
                #     location
                #     + " predicted turnover after is sometimes lower than predicted turnover clean"
                # )

        df_model_mape = pd.DataFrame.from_dict(model_eval_mape_dict, orient="index").reset_index()
        df_model_mape = df_model_mape.rename(
            columns={"index": self.training_config.location_type_to_evaluate}
        )
        print("Model MAPE")
        print(df_model_mape)

        df_model_mae = pd.DataFrame.from_dict(model_eval_mae_dict, orient="index").reset_index()
        df_model_mae = df_model_mae.rename(
            columns={"index": self.training_config.location_type_to_evaluate}
        )

        print("Model MAE")
        print(df_model_mae)

        df_testing_counts = pd.DataFrame.from_dict(
            testing_counts_dict, orient="index"
        ).reset_index()
        df_testing_counts = df_testing_counts.rename(
            columns={"index": self.training_config.location_type_to_evaluate}
        )

        df_first_case_testing_counts = pd.DataFrame.from_dict(
            testing_first_case_counts_dict, orient="index"
        ).reset_index()
        df_first_case_testing_counts = df_first_case_testing_counts.rename(
            columns={"index": self.training_config.location_type_to_evaluate}
        )

        print("Turnovers in testing")
        print(df_testing_counts)
        print("First cases in testing")
        print(df_first_case_testing_counts)

        df_minimum_turnover_mae = pd.DataFrame.from_dict(
            minimum_turnover_eval_mae_dict, orient="index"
        ).reset_index()
        df_minimum_turnover_mae = df_minimum_turnover_mae.rename(
            columns={"index": self.training_config.location_type_to_evaluate}
        )
        df_minimum_turnover_mae = df_minimum_turnover_mae.merge(
            df_model_mae[[self.training_config.location_type_to_evaluate, "turnover_after_case"]],
            on=self.training_config.location_type_to_evaluate,
        )
        df_minimum_turnover_mae["model_improvement_over_heuristic"] = (
            df_minimum_turnover_mae["minimum_turnover_mae"]
            - df_minimum_turnover_mae["turnover_after_case"]
        )
        print(
            "minimum turnover heuristic MAE (only makes sense when idle time is off; compare to turnover after case)"
        )
        print(df_minimum_turnover_mae)

        return (
            df_model_mape.sort_values(self.training_config.location_type_to_evaluate),
            df_model_mae.sort_values(self.training_config.location_type_to_evaluate),
            df_testing_counts.sort_values(self.training_config.location_type_to_evaluate),
            df_first_case_testing_counts.sort_values(
                self.training_config.location_type_to_evaluate
            ),
            df_minimum_turnover_mae.sort_values(self.training_config.location_type_to_evaluate),
            df_turnover_predictions_to_upload,
        )

    def compute_eval_metrics(
        self, y_actual: npt.NDArray[np.float64], y_pred: npt.NDArray[np.float64]
    ) -> Tuple[float, float]:
        # TODO: this is a patch to filter out a tiny portion of turnovers are zero duration,
        # move this upstream
        mask = y_actual != 0

        if np.any(mask):
            mape = 100 * np.nanmean(np.abs(y_pred[mask] - y_actual[mask]) / y_actual[mask])
        else:
            mape = np.nan

        if y_actual.size > 0:
            mae = np.nanmean(np.abs(y_pred - y_actual))
        else:
            mae = np.nan

        return mape, mae

    def turnover_evaluation_schema(self) -> list[SchemaField]:
        return [
            SchemaField("case_id", "STRING", mode="REQUIRED"),
            SchemaField("org_id", "STRING", mode="REQUIRED"),
            SchemaField("site_id", "STRING", mode="REQUIRED"),
            SchemaField("actual", "INTEGER", mode="REQUIRED"),
            SchemaField("prediction", "FLOAT", mode="REQUIRED"),
            SchemaField("turnover_type", "STRING", mode="REQUIRED"),
            SchemaField("minimum_turnover_heuristic_after_case", "INTEGER", mode="REQUIRED"),
            SchemaField("error", "FLOAT", mode="REQUIRED"),
            SchemaField("model_type", "STRING", mode="REQUIRED"),
            SchemaField("training_run_at", "TIMESTAMP", mode="REQUIRED"),
            SchemaField("ds", "DATE", mode="REQUIRED"),
            SchemaField("model_identifier", "STRING", mode="REQUIRED"),
        ]

    def upload_evaluation_data(self, bq_client: BQClient, df: pd.DataFrame) -> None:
        table_id = "prod-data-platform-027529.case_forecasting.turnover_model_training_results"
        table = Table(
            table_id,
            schema=self.turnover_evaluation_schema(),
        )
        table.time_partitioning = TimePartitioning(
            type_=TimePartitioningType.DAY,
            field="ds",
        )
        table = bq_client.create_table(table, exists_ok=True, retry=Retry(deadline=60))
        bq_client.insert_rows_from_dataframe(table, df)
