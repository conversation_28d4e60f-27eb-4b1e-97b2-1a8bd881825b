import logging

import pandas as pd
from google.cloud.bigquery import Client as B<PERSON><PERSON><PERSON>
from pandasql import sqldf  # noqa
from training_utils.clearml_reporter import <PERSON><PERSON><PERSON>eporter

from turnover.configs.prod_config import DataSelectionConfig

logger = logging.getLogger(__name__)


class DataSelector:
    def __init__(
        self,
        bq_client: BQClient,
        config: DataSelectionConfig,
        reporter: ClearMLReporter,
    ):
        self.bq_client = bq_client
        self.config = config
        self.reporter = reporter

    def generate_data_for_fit(self) -> pd.DataFrame:
        # get turnovers; these turnovers include both idle and active time
        df_turnovers = self.query_for_turnover_data()

        num_or_days_in_df_turnovers = len(df_turnovers.groupby(["room_id", "case_date"]).count())
        print("num of OR-days in df_turnovers:", num_or_days_in_df_turnovers)

        # TODO: improve how we toggle between modeling active vs (active + idle) turnovers...
        if not self.config.eliminate_idle_time:
            # if not taking out idle time, don't need to rename any columns
            df_turnovers_to_return = df_turnovers

        if self.config.eliminate_idle_time:
            # need to rename the "active_" columns to be the same as the outcome variables
            del df_turnovers["turnover_before_case"]
            del df_turnovers["turnover_after_case"]
            del df_turnovers["turnover_open_before_case"]
            del df_turnovers["turnover_clean_after_case"]

            df_turnovers_to_return = df_turnovers.rename(
                columns={
                    "active_turnover_before_case": "turnover_before_case",
                    "active_turnover_after_case": "turnover_after_case",
                    "active_turnover_open_before_case": "turnover_open_before_case",
                    "active_turnover_clean_after_case": "turnover_clean_after_case",
                }
            )

        return df_turnovers_to_return

    def query_for_turnover_data(self) -> pd.DataFrame:
        query_string = self.bq_query_string_for_turnover(self.config.min_date)
        df_turnovers = self.bq_client.query(query_string).to_dataframe()
        print("in query_for_turnover_data, max date:", max(df_turnovers["case_date"]))
        print("site_ids in all turnover data: ", sorted(list(df_turnovers["site_id"].unique())))
        return df_turnovers

    def bq_query_string_for_turnover(self, min_date: str) -> str:
        return f"""
        with features as (
            select 
                org_id,
                site_id,
                room_id,
                date_of_surgery as case_date,
                case_id,
                cast(row_number() over (
                        partition by site_id, room_id, date_of_surgery
                        order by actual_start_datetime_local
                    ) = 1 as tinyint) as first_case_by_actual_start,
                actual_start_datetime_local,
                actual_end_datetime_local,
                lead(actual_start_datetime_local) over (
                        partition by site_id, room_id, date_of_surgery 
                        order by actual_start_datetime_local
                    ) as next_case_actual_start_datetime_local,
                lag(actual_end_datetime_local) over (
                    partition by site_id, room_id, date_of_surgery 
                    order by actual_start_datetime_local
                ) as prev_case_actual_end_datetime_local,
                scheduled_start_datetime_local,
                scheduled_end_datetime_local,
                lead(scheduled_start_datetime_local) over (
                        partition by site_id, room_id, date_of_surgery
                        order by actual_start_datetime_local
                    ) as next_case_scheduled_start_datetime_local,
                first_primary_procedure,
                first_primary_surgeon,
                scheduled_duration,
                first_case
            from `prod-data-platform-027529.gold.forecasting_case_features_combined_latest`
        ),
    
        -- note, this includes historical data
        full_turnovers as (
            select *,
                timestamp_diff(
                    features.next_case_scheduled_start_datetime_local,
                    features.scheduled_end_datetime_local,
                    minute
                ) as scheduled_turnover_after_case,
                timestamp_diff(
                    features.next_case_actual_start_datetime_local,
                    features.actual_end_datetime_local,
                    minute
                ) as turnover_after_case,
                timestamp_diff(
                    features.actual_start_datetime_local,
                    features.prev_case_actual_end_datetime_local,
                    minute
                ) as turnover_before_case
            from features
        ),
        
        -- note, this includes only apella live data
        cleans_and_opens as (
            select
                apella_case_id,
                turnover_open_before_case,
                turnover_clean_after_case
            from `prod-data-platform-027529.gold.turnovers_active_and_idle_latest`
        ),
        
        all_turnovers as (
        
            select *
            from full_turnovers
            left join cleans_and_opens
                on full_turnovers.case_id = cleans_and_opens.apella_case_id
        )
        
        select *
        from all_turnovers
        where case_date >= "{min_date}"
            -- sometimes a case scheduled for the future occurs many days before scheduled date
            and case_date = date(actual_start_datetime_local)
        """
