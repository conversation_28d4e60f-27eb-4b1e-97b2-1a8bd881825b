from dataclasses import dataclass

from pydantic import BaseModel, ConfigDict
from training_utils.clearml_reporter import ClearMLBaseModel


@dataclass
class FeatureClass:
    # Our features fall into numeric or categorical, this is needed for fits.
    name: str
    data_type: str  # numeric or categorical


class ModelConfig(BaseModel):
    # Define parameter for the model
    n_estimators: int = 200

    min_samples_split: int = 10
    min_samples_leaf: int = 2
    max_depth: int = 40


class TrainingConfig(BaseModel):
    outcome_variables: list[str] = [
        # Note that turnover_open_before_case and turnover_clean_after case only use live data
        # the other use historical + live data
        "turnover_before_case",
        "turnover_after_case",
        "turnover_open_before_case",
        "turnover_clean_after_case",
        # "turnover_open_and_clean_for_relevant_case",  # experimental
        # "turnover_open_after_case_irrelevant",  # experimental
        # "turnover_clean_minutes_before_case_irrelevant"  # experimental
    ]

    # location_type_to_fit corresponds to the column name (site_id or org_id) used to
    # split the data before training. Each "location" will have its own model per outcome variable
    # sites: ["OPC19", "OPC18", "VH02", "WT03", "Fairfield"]
    # org_ids: ["houston_methodist", "health_first", "north_bay", "tampa_general"]
    locations_to_fit: list[str] = [
        "houston_methodist",
        "health_first",
        "north_bay",
        "tampa_general",
        "nyu",
        "lifebridge",
    ]
    location_type_to_fit: str = "org_id"  # "site" or "org_id"
    location_type_to_evaluate: str = "site_id"  # site_id, business_unit, or org_id

    evaluation_to_fit_mapping: dict[str, str] = {
        "HMH-OPC18": "houston_methodist",
        "HMH-OPC19": "houston_methodist",
        "HMH-WT03": "houston_methodist",
        "HMH-DUNN03": "houston_methodist",
        "HMH-DUNN06": "houston_methodist",
        "HMH-MAIN03": "houston_methodist",
        "HMH-HMBA": "houston_methodist",
        "HMH-LD06": "houston_methodist",
        "HMH-HMW-LD": "houston_methodist",
        "HMH-HMW-OR": "houston_methodist",
        "HMH-HMBT-ASC": "houston_methodist",
        "HMH-HMBT-LD": "houston_methodist",
        "HMH-HMBT-OR": "houston_methodist",
        "HMH-HMCL-ASC": "houston_methodist",
        "HMH-HMCL-CBC": "houston_methodist",
        "HMH-HMCL-OR": "houston_methodist",
        "HMH-HMCY-ENDO": "houston_methodist",
        "HMH-HMCY-CATH": "houston_methodist",
        "HMH-HMCY-IR": "houston_methodist",
        "HMH-HMCY-LD": "houston_methodist",
        "HMH-HMCY-OR": "houston_methodist",
        "HMH-HMSL-LD": "houston_methodist",
        "HMH-HMSL-OR": "houston_methodist",
        "HMH-HMTW": "houston_methodist",
        "HMH-HMWB-C": "houston_methodist",
        "HMH-HMWB-N": "houston_methodist",
        "HMH-HMWB-S": "houston_methodist",
        "TGH-MAIN02": "tampa_general",
        "TGH-CVTOR03": "tampa_general",
        "HF-VH02": "health_first",
        "nb_medical_center_fairfield": "north_bay",
        "NYU-LI4": "nyu",
        "NYU-KP4": "nyu",
        "NYU-KP5": "nyu",
        "NYU-KP5-CATH": "nyu",
        "LBHS-GOR": "lifebridge",
        # "LBHS-RIAO": "lifebridge"
    }

    # defines train/test split
    test_on_most_recent_x_days: int = (
        7  # TODO: change back to 60 after gathering more live data at nyu
    )

    # as an alternative or in addition to eliminating idle time,
    # train and/or test on only short scheduled turnovers
    # note that setting these to True will eliminate all first cases
    # (there's no scheduled turnover before a first case)
    eliminate_high_scheduled_turnovers_in_training: bool = False
    eliminate_high_scheduled_turnovers_in_testing: bool = False
    scheduled_turnover_duration_limit: int = 60

    # eliminate any actual (whether total or active) from training and test set
    # when it is a large outlier
    # this will not eliminate first cases b/c first cases can have a non-null turnover_open
    # when True, also eliminate negative turnovers
    # the cutoff is calculated on a site-by-site basis from the training data, and is
    # the minimum of: (1) the selected percentile of turnover_afters at the site, (2) default_turnover_limit
    eliminate_high_actual_turnovers_in_training: bool = True
    eliminate_high_actual_turnovers_in_testing: bool = True
    percentile_for_actual_turnover_duration_limit: float = 0.8
    default_turnover_limit: int = 60

    # if True, eliminate in both train and test IF the outcome variable is turnover_open_before_case
    # eliminating first cases can get rid of cases where they open the back table ASAP b/c there's no previous case,
    # then there's a long wait before any active work happens. this wouldn't be eliminated by idle time if
    # there are still people in the room
    # not eliminating first cases could cause a predicted turnover_before to be shorter than turnover_open
    # because turnover_before only exists for non-first cases, while turnover_opens can be artificially
    # amplified by cases where they open back table very early
    eliminate_first_case_for_turnover_open: bool = True


class DataSelectionConfig(BaseModel):
    min_date: str = "2023-09-01"

    eliminate_idle_time: bool = False  # note: this has to be False b/c we can't eliminate idle time for historical data. see DATA-2970

    # caution: there may be more features calculated in the model pipeline that are automatically included
    #   the automatically included features are not required to be provided during inference, but rather are
    #   calculated from the features in this features list
    features: list[FeatureClass] = [
        FeatureClass(name="first_primary_surgeon", data_type="categorical"),
        FeatureClass(name="first_primary_procedure", data_type="categorical"),
    ]

    @property
    def feature_names(self) -> list[str]:
        return [feat.name for feat in self.features]

    def get_features_description(self) -> str:
        features_string = ""
        for feature in self.features:
            features_string += f"\n - {feature.name}"
        return features_string

    def get_features_list(self, feature_data_type: str = "") -> list[str]:
        features_list = []
        for feature in self.features:
            if feature_data_type != "":
                if feature.data_type == feature_data_type:
                    features_list.append(feature.name)
            else:
                features_list.append(feature.name)
        return features_list


class ClearMLConfig(ClearMLBaseModel):
    project_name: str = "Turnover Model"
    task_name: str = "Experiment: Include historical data, custom threshold, and ordinal encoding"
    tags: dict[str, str] = {"params": "200"}


class ModelTrainingConfig(BaseModel):
    forecast_model_config: ModelConfig = ModelConfig()
    data_selection_config: DataSelectionConfig = DataSelectionConfig()
    training_config: TrainingConfig = TrainingConfig()
    clearml_config: ClearMLConfig = ClearMLConfig()
    model_identifier: str = "experiment_model_eliminate_actuals_over60"

    # See https://docs.pydantic.dev/latest/api/config/#pydantic.config.ConfigDict.protected_namespaces
    model_config = ConfigDict(protected_namespaces=())
