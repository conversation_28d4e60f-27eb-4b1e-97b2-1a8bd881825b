SHELL := /bin/bash
PORT := 3000
MAKEFILE_DIR=$(realpath $(dir $(abspath $(lastword $(MAKEFILE_LIST)))))

.PHONY: format
format:
	poetry run ruff check --fix .
	poetry run ruff format .

.PHONY: lint
lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

.PHONY: test
test:
	poetry run python -m pytest tests

.PHONY: test-cov
test-cov:
	set -o pipefail && poetry run python -m pytest --junitxml=pytest.xml --cov-report=term-missing \
	--cov=resnet_embedding tests | tee pytest-coverage.txt

.PHONY: curl
curl:
	@python3 -m http.server -d $(MAKEFILE_DIR)/resnet_embedding 8000 & \
		echo $$! > $(MAKEFILE_DIR)/file-server-process.pid
	@sleep 3
	curl -X 'POST' 'http://localhost:3000/predict' \
		-H 'accept: application/json' \
		-H 'Content-Type: multipart/form-data' \
		-F 'image=http://localhost:8000/test-image.jpg;type=image/jpeg'
	@echo -e "\nStopping http file server process..."
	@kill `cat $(MAKEFILE_DIR)/file-server-process.pid` && \
		rm $(MAKEFILE_DIR)/file-server-process.pid

locust-test-container:
	docker build --secret id=google-application-credentials,src=${HOME}/.config/gcloud/application_default_credentials.json -t resnet_embedding_locust_tests -f locust.Dockerfile .

locust-test-run-local: locust-test-container
	docker run \
		--rm \
		-p 8000:8000 \
		resnet_embedding_locust_tests  \
		-f resnet_embedding/locust/basic.py \
		-H http://host.docker.internal:9999 \
		--headless \
		-u 10 \
		-t 1m

run-local:
	DEV_STUB=1 poetry run fastapi dev --port $(PORT) --reload

run-uvicorn:
	DEV_STUB=1 poetry run uvicorn app.main:app --reload --port $(PORT) --log-config log_config.yml

# Default Google credentials path for Mac
GOOGLE_CREDS ?= $(HOME)/.config/gcloud/application_default_credentials.json

.PHONY: build-container

build-container:
	docker build \
		--build-arg PROJECT_DIR=models/image-embedding/resnet_embedding \
		--build-arg PROJECT_NAME=resnet_embedding \
		--secret id=google-application-credentials,src=$(GOOGLE_CREDS) \
		-t resnet_embedding:latest \
		-f Dockerfile \
		../../..
