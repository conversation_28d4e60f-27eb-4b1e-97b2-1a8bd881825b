FROM nvidia/cuda:12.6.2-base-ubuntu22.04

ARG PROJECT_DIR
ARG PROJECT_NAME

ENV POETRY_VERSION=1.8.3

RUN linuxDeps="build-essential curl pkg-config python3.10 python3.10-distutils python3-pip" && \
  apt-get -yqq update && \
  apt-get install -yq --no-install-recommends ${linuxDeps} && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*

# Upgrade setuptools globally to fix CVE-2024-6345 before dropping privileges
RUN pip3 uninstall -y setuptools && \
  pip3 install --upgrade setuptools --no-cache-dir

RUN useradd -ms /bin/bash apella
USER apella
WORKDIR /home/<USER>
SHELL ["/bin/bash", "-c"]

# Setup poetry
RUN curl -sSL https://install.python-poetry.org | python3.10 - --version ${POETRY_VERSION}
ENV PATH="${PATH}:/home/<USER>/.local/bin"

# Configure Poetry to talk to our Python artifactory
RUN poetry self add keyrings.google-artifactregistry-auth
# this creates a .venv in the project directory
RUN poetry config virtualenvs.in-project true

# Create src directory and symlink project
RUN mkdir -p src /home/<USER>/.config/gcloud

# Copy serving_utils in one step to reduce image layers
COPY serving_utils/serving_utils src/serving_utils/serving_utils
COPY serving_utils/pyproject.toml serving_utils/poetry.lock serving_utils/README.md src/serving_utils/

# Copy project code in one step to reduce layers and optimize caching
COPY --chown=apella:apella ${PROJECT_DIR}/pyproject.toml ${PROJECT_DIR}/poetry.lock ${PROJECT_DIR}/log_config.yml src/${PROJECT_DIR}/

# Run Poetry Install
# Artifact Registry credentials can be provided with via the
# `--secret id=google-application-credentials,src=<local path to credentials>` argument to `docker build`.
# If no secret is specified, the `--mount` silently fails, which is desirable for cases like Cloud
# Build where credentials are provided via instance metadata.
RUN --mount=type=secret,id=google-application-credentials,mode=444,target=/home/<USER>/.config/gcloud/application_default_credentials.json \
  poetry install --directory /home/<USER>/src/${PROJECT_DIR} --without dev --no-interaction --no-root && \
  poetry cache clear --all . && \
  rm -rf /home/<USER>/.cache/pypoetry/cache

COPY --chown=apella:apella ${PROJECT_DIR}/app src/${PROJECT_DIR}/app
COPY --chown=apella:apella ${PROJECT_DIR}/resnet_embedding src/${PROJECT_DIR}/resnet_embedding

WORKDIR /home/<USER>/src/${PROJECT_DIR}

# necessary for executing ray commands used by Ray Jobs
ENV PATH="/home/<USER>/src/${PROJECT_DIR}/.venv/bin:${PATH}"
ENV SERVICE_NUM_WORKERS=1

EXPOSE 3000
CMD poetry run uvicorn app.main:app --host 0.0.0.0 --port 3000 --workers ${SERVICE_NUM_WORKERS} --timeout-keep-alive 120 --proxy-headers --log-config log_config.yml
