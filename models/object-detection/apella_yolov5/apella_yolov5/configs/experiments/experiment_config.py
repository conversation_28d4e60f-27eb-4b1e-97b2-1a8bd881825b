from apella_yolov5.configs.prod_config import config

config = config.model_copy()

# Modify config params as needed to experiment

# When running the experiment locally, we'll look for the images in the local filesystem
# TODO: explain how to download them
config.training_config.pv_images_dir = "/tmp/images"

# When running the experiment locally, we'll save the dataset and the model in the local filesystem
config.training_config.data_root = "/tmp/data"

# Limit the number of rows to 100 for testing
config.data_selection_config.limit_rows = 100

# Train a nano model
config.training_config.original_model_weights = "yolo11n.pt"

# Train for only 1 epoch
config.training_config.epochs = 1

# Download missing images to the local filesystem
config.training_config.download_missing_images = True

# Set the task name to the experiment name
config.clearml_config.task_name = "Experiment Training - initial local training"

# Set the model identifier to the experiment name
config.model_identifier = "yolo_model_exp"
