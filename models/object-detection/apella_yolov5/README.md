# FastAPI Service for YOLO (v11) Object Detection model trained on Apella annotated data.

## Deployment

[Instructions for Rollouts](https://www.notion.so/apella/Object-Detection-Model-Deployment-Playbook-1e4db55445b9465f83b32fe1e541a7ad)  

## Running the service

```
make run-local
```

## Inference

### curl examples

```bash
curl -X 'POST' \
  'http://localhost:9981/predict' \
  -H 'accept: application/json' \
  -H 'Content-Type: multipart/form-data' \
  -F 'input_image=@tests/locust/test-image.jpg;type=image/jpeg'
```

```
curl -X 'GET' \
  'http://localhost:9981/livez' \
  -H 'accept: application/json'
```

```
curl -X 'GET' \
  'http://localhost:9981/readyz' \
  -H 'accept: application/json'
```

## Local docker builds
An alternative to locally building and pushing the Docker image is to use gcloud builds. See [here](https://github.com/Apella-Technology/event-transformer-model/blob/a4a37cab1033f5d0c1a065d97770478e08a8ed1b/transformer_training/README.md?plain=1#L55C1-L56C1) for an example of how to do this.
1. Get gcloud credentials: `gcloud auth login --update-adc`  
2. Authorize docker to push/pull to/from GAR: `gcloud auth configure-docker us-central1-docker.pkg.dev`
3. From ~/apella/ml-services (needs to be run from here b/c ~/apella/ml-services/serving_utils/ is used in the Dockerfile), run: 
```
docker build . -f models/object-detection/apella_yolov5/Dockerfile --build-arg PROJECT_DIR=models/object-detection/apella_yolov5 --build-arg PROJECT_NAME=raytest --platform linux/amd64 --secret id=google-application-credentials,src=${GOOGLE_APPLICATION_CREDENTIALS} -t us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ray/yolo_image_embedding:$(git rev-parse HEAD)
```

4. Push the image to the Google Artifact Registry
```
docker push us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/ray/yolo_image_embedding:$(git rev-parse HEAD)
```
The first time you push will be slow, but subsequent pushes will be fast because the layers will be cached. 

5. Update `ml-services/models/object-detection/apella_yolov5/scripts/batch_image_embedding/ray_cluster.yaml` with the new docker image and apply it
```
cd ./models/object-detection/apella_yolov5
kubectl apply -f scripts/batch_image_embedding/ray_cluster.yaml -n ray
```
## Submitting a Ray job
This example is for batch inference using YOLO
1. Port-forward the Ray cluster:
```
kubectl port-forward service/ray-cluster-kuberay-head-svc -n ray 8265:8265
```
2. Submit the job to the Ray cluster (this also pushes local code changes):    

dev:
```
export RAY_ADDRESS="http://localhost:8265"
ray job submit --runtime-env-json='{"working_dir": "./", "env_vars": {"DEV_STUB": "1"}}' -- python scripts/batch_image_embedding
```
prod:
```
export RAY_ADDRESS="http://localhost:8265"
ray job submit --runtime-env-json='{"working_dir": "./"}' -- python scripts/batch_image_embedding
```

## Training
To train a model, you'll want to modify the `apella_yolov5/configs/experiment_config.py` file with the settings you need. In particular, you'll want to modify the `config.model_identifier` to a unique name representing your model (this name determines which folder we use for saving the model in the models bucket).

Then, to run the model locally by calling `make run-training-locally`.

Alternatively, you can use the `async_trainer` to kick off a training job in the cloud. Use the makefile command and provide the experiment config module:
```
make run-async-trainer MODEL_CONFIG_MODULE_NAME=apella_yolov5.configs.experiments.experiment_config
```

### The images Persistent Volume
To save time, we use a Persistent Volume for the images when training in k8s. 
* A daily job writes all of the new images to the Persistent Volume. This job uses the `cloud_download_images_config.py` and just runs the training job with the training or evaluation parts
* The training jobs mount the Persistent Volume and use symbolic links to create the directory structure that the Yolo model training requires