MAKEFILE_DIR=$(realpath $(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
format:
	poetry run ruff check --fix .
	poetry run ruff format .

lint:
	poetry run ruff check .
	poetry run ruff format --check .
	poetry run mypy .

test:
	poetry run pytest tests/

container:
	docker build --secret id=google-application-credentials,src=${HOME}/.config/gcloud/application_default_credentials.json -t feature-processor -f $(MAKEFILE_DIR)/Dockerfile $(MAKEFILE_DIR)/../..

run-forecasting-dev:
	poetry run feature_processor \
		--GCP_PROJECT_ID "dev-data-platform-439b4c" \
		--INSTANCE_ID "dev-general-ssd" \
		--PUB_SUB_SUBSCRIPTION "dev-forecasting-feature-store-subscription" \
		--FEATURE_PROCESSOR_TYPE "forecasting" \
		--TABLE_NAME "cases" \
		--disable_json_logging

run-forecasting-prod:
	poetry run feature_processor \
		--G<PERSON>_PROJECT_ID "prod-data-platform-027529" \
		--INSTANCE_ID "prod-general-ssd" \
		--PUB_SUB_SUBSCRIPTION "prod-forecasting-feature-store-subscription" \
		--FEATURE_PROCESSOR_TYPE "forecasting" \
		--TABLE_NAME "cases" \
		--disable_json_logging

run-event-model-dev:
	poetry run feature_processor \
		--GCP_PROJECT_ID "dev-data-platform-439b4c" \
		--INSTANCE_ID "dev-general-ssd" \
		--PUB_SUB_SUBSCRIPTION "dev-event-model-features-subscription" \
		--FEATURE_PROCESSOR_TYPE "event_model" \
		--TABLE_NAME "ml_event_model_features" \
		--debug_mode

run-event-model-prod:
	poetry run feature_processor \
		--GCP_PROJECT_ID "prod-data-platform-027529" \
		--INSTANCE_ID "prod-general-ssd" \
		--PUB_SUB_SUBSCRIPTION "prod-event-model-features-subscription" \
		--FEATURE_PROCESSOR_TYPE "event_model" \
		--TABLE_NAME "ml_event_model_features" \
		--debug_mode

process-single-message:
	poetry run python feature_processor/process_single_message.py \
		--GCP_PROJECT_ID "$$GCP_PROJECT_ID" \
		--INSTANCE_ID "$$INSTANCE_ID" \
		--TABLE_NAME "$$TABLE_NAME" \
		--FEATURE_PROCESSOR_TYPE "$$FEATURE_PROCESSOR_TYPE" \
		--MESSAGE_DATA "$$MESSAGE_DATA" \
		--disable_json_logging \
		--debug
