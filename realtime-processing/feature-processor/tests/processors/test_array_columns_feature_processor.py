import datetime
import json
from unittest.mock import MagicMock

import pytest
from google.cloud.bigtable.instance import Instance
from google.cloud.bigtable.table import Table

from feature_processor.processors.array_columns_feature_processor import (
    ArrayColumnsFeatureProcessor,
    MessageData,
)


@pytest.fixture
def mock_instance():
    return MagicMock(spec=Instance)


@pytest.fixture
def mock_table():
    return MagicMock(spec=Table)


@pytest.fixture
def processor(mock_instance):
    return ArrayColumnsFeatureProcessor(mock_instance, "test_table")


def test_process_message_with_valid_data(processor, mock_table) -> None:
    # Setup
    processor._table = mock_table
    mock_row = MagicMock()
    mock_table.row.return_value = mock_row

    # Test data with different timestamp formats and both field names
    test_cases = [
        {
            "float_features": [{"name": "test_feature", "value": 1.234}],
            "float_array_features": [{"name": "test_array", "value": [1.1, 2.2, 3.3]}],
            "room_id": "room1",
            "time_window_start_utc": "2024-01-01 00:00:00Z",  # With Z
            "update_time": "2024-02-03 00:00:00+00:00",  # with "update_time" instead of "updated_time"
        },
        {
            "float_features": [{"name": "test_feature", "value": 1.234}],
            "float_array_features": [{"name": "test_array", "value": [1.1, 2.2, 3.3]}],
            "room_id": "room1",
            "time_window_start_utc": "2024-01-01 00:00:00Z",  # With Z
            "updated_time": "2024-02-03 00:00:00+00:00",  # With +00:00
        },
    ]

    for message_data_dict in test_cases:
        message_data = json.dumps(message_data_dict)
        # Execute
        processor.process_message(message_data)

        # Verify
        mock_table.row.assert_called_with("room1#2024-01-01T00:00:00+00:00")
        assert mock_row.set_cell.call_count == 2  # 1 float feature + 1 array feature
        mock_row.commit.assert_called_once()

        # Reset mocks for next iteration
        mock_table.row.reset_mock()
        mock_row.set_cell.reset_mock()
        mock_row.commit.reset_mock()


def test_process_message_with_different_timestamp_formats(
    processor, mock_table
) -> None:
    # Setup
    processor._table = mock_table
    mock_row = MagicMock()
    mock_table.row.return_value = mock_row

    # Test different timestamp formats
    formats = [
        "2024-01-01 00:00:00Z",  # With Z
        "2024-01-01 00:00:00+00:00",  # With +00:00
        "2024-01-01 00:00:00",  # Without timezone
        "2024-01-01",  # Just date
        "2024-01-01 00:00:00.00Z",
        "2024-01-01 00:00:00.00+00:00",
    ]

    for time_format in formats:
        message_data = json.dumps(
            {
                "float_features": [{"name": "test_feature", "value": 1.234}],
                "float_array_features": [
                    {"name": "test_array", "value": [1.1, 2.2, 3.3]}
                ],
                "room_id": "room1",
                "time_window_start_utc": time_format,
                "updated_time": "2025-01-01 00:00:00Z",
            }
        )

        # Execute
        processor.process_message(message_data)

        # Verify
        mock_table.row.assert_called_with("room1#2024-01-01T00:00:00+00:00")
        mock_row.commit.assert_called()
        mock_table.row.reset_mock()
        mock_row.commit.reset_mock()


def test_process_message_with_invalid_float_feature(processor) -> None:
    # Test data with invalid types
    message_data = json.dumps(
        {
            "float_features": [{"name": "test", "value": "not_a_float"}],
            "float_array_features": [{"name": "test", "value": [1.0]}],
            "room_id": "room1",
            "time_window_start_utc": "2024-01-01 00:00:00Z",
        }
    )

    with pytest.raises(ValueError, match="Input should be a valid number"):
        processor.process_message(message_data)


def test_process_message_with_invalid_float_array_feature(processor) -> None:
    # Test data with invalid types
    message_data = json.dumps(
        {
            "float_features": [{"name": "test", "value": 1.0}],
            "float_array_features": [{"name": "test", "value": "not_a_list"}],
            "room_id": "room1",
            "time_window_start_utc": "2024-01-01 00:00:00Z",
        }
    )

    with pytest.raises(ValueError, match="Input should be a valid array"):
        processor.process_message(message_data)


def test_process_message_with_missing_required_field(processor) -> None:
    # Test data missing room_id
    message_data = json.dumps(
        {
            "float_features": [{"name": "test", "value": 1.0}],
            "float_array_features": [{"name": "test", "value": [1.0]}],
            "time_window_start_utc": "2024-01-01 00:00:00Z",
        }
    )

    with pytest.raises(ValueError, match="room_id\n  Field required"):
        processor.process_message(message_data)


def test_generate_row_key(processor) -> None:
    room_id = "room1"
    timestamp = datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)

    row_key = processor._generate_row_key(room_id, timestamp)

    assert row_key == "room1#2024-01-01T00:00:00+00:00"


def test_add_float_feature_to_row(processor, mock_table) -> None:
    mock_row = MagicMock()
    feature_name = "test_feature"
    feature_value = 1.234
    timestamp = datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)

    processor._add_float_feature_to_row(
        mock_row, feature_name, feature_value, timestamp
    )

    expected_feature_value = int(round(feature_value * processor.MULTIPLIER))
    expected_feature_value_bytes = expected_feature_value.to_bytes(
        length=8, byteorder="big", signed=True
    )
    mock_row.set_cell.assert_called_once_with(
        "features",
        feature_name,
        expected_feature_value_bytes,
        timestamp=timestamp,
    )


def test_add_negative_float_feature_to_row(processor, mock_table) -> None:
    mock_row = MagicMock()
    feature_name = "test_feature"
    feature_value = -1.234
    timestamp = datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)

    processor._add_float_feature_to_row(
        mock_row, feature_name, feature_value, timestamp
    )

    expected_feature_value = int(round(feature_value * processor.MULTIPLIER))
    expected_feature_value_bytes = expected_feature_value.to_bytes(
        length=8, byteorder="big", signed=True
    )
    mock_row.set_cell.assert_called_once_with(
        "features",
        feature_name,
        expected_feature_value_bytes,
        timestamp=timestamp,
    )


def test_add_float_array_feature_to_row(processor, mock_table) -> None:
    mock_row = MagicMock()
    feature_name = "test_array"
    feature_value = [1.1, 2.2, 3.3]
    timestamp = datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc)

    processor._add_float_array_feature_to_row(
        mock_row, feature_name, feature_value, timestamp
    )

    mock_row.set_cell.assert_called_once_with(
        "features",
        feature_name,
        b"1100,2200,3300",  # Each value * 1000 (MULTIPLIER)
        timestamp=timestamp,
    )


@pytest.mark.parametrize(
    "time_window,updated_time,expected_time_window,expected_timestamp",
    [
        # String formats
        (
            "2024-01-01 00:00:00Z",
            "2024-02-03 00:00:00Z",
            datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 2, 3, tzinfo=datetime.timezone.utc),
        ),
        (
            "2024-01-01 00:00:00+00:00",
            "2024-02-03 00:00:00+00:00",
            datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 2, 3, tzinfo=datetime.timezone.utc),
        ),
        (
            "2024-01-01 00:00:00",
            "2024-02-03 00:00:00",
            datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 2, 3, tzinfo=datetime.timezone.utc),
        ),
        (
            "2024-01-01",
            "2024-02-03",
            datetime.datetime(2024, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 2, 3, 0, 0, 0, tzinfo=datetime.timezone.utc),
        ),
        # Datetime objects
        (
            datetime.datetime(2024, 1, 1),
            datetime.datetime(2024, 2, 3, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 1, 1, tzinfo=datetime.timezone.utc),
            datetime.datetime(2024, 2, 3, tzinfo=datetime.timezone.utc),
        ),
    ],
)
def test_message_data_timestamp_validation(
    time_window, updated_time, expected_time_window, expected_timestamp
) -> None:
    """Test that MessageData correctly handles different timestamp formats."""
    data = MessageData(
        float_features=[],
        float_array_features=[],
        room_id="room1",
        time_window_start_utc=time_window,
        updated_time=updated_time,
    )

    assert isinstance(data.time_window_start_utc, datetime.datetime)
    assert data.time_window_start_utc.tzinfo == datetime.timezone.utc
    assert data.time_window_start_utc == expected_time_window

    if expected_timestamp is None:
        assert data.updated_time is None
    else:
        assert isinstance(data.updated_time, datetime.datetime)
        assert data.updated_time.tzinfo == datetime.timezone.utc
        assert data.updated_time == expected_timestamp


@pytest.mark.parametrize(
    "time_window,updated_time",
    [
        ("invalid-date", "2024-02-03 00:00:00Z"),
        ("2024-01-01 00:00:00Z", "invalid-date"),
    ],
)
def test_message_data_invalid_timestamps(time_window, updated_time) -> None:
    """Test that MessageData rejects invalid timestamp formats."""
    with pytest.raises(ValueError):
        MessageData(
            float_features=[],
            float_array_features=[],
            room_id="room1",
            time_window_start_utc=time_window,
            updated_time=updated_time,
        )
