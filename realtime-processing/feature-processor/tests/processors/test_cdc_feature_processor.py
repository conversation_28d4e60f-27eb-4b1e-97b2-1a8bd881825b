import datetime
import json
from unittest.mock import MagicMock, patch
from zoneinfo import ZoneInfo

from pydantic import ValidationError
import pytest

from feature_processor.dtos.forecasting import PythiaFeaturesSchema
from feature_processor.processors.cdc_feature_processor import CDCFeatureProcessor

UPDATED_TIME = datetime.datetime(
    year=2023, month=11, day=30, hour=12, minute=0, second=0, tzinfo=ZoneInfo("UTC")
)

START_TIME = datetime.datetime(
    year=2023,
    month=11,
    day=30,
    hour=13,
    minute=0,
    second=0,
    tzinfo=ZoneInfo("UTC"),
)

TIMEFORMAT_1 = "%Y-%m-%dT%H:%M:%S.%fZ"
TIMEFORMAT_2 = "%Y-%m-%dT%H:%M:%SZ"


def diff_utc_now(delta: datetime.timedelta) -> str:
    """
    Calculate a UTC timestamp that is a specified time delta before the current time.

    Args:
        delta: The time delta to subtract from current UTC time

    Returns:
        ISO-formatted UTC timestamp string
    """
    return (datetime.datetime.now(datetime.timezone.utc) - delta).strftime(TIMEFORMAT_1)


test_dict = {
    "case_id": "1",
    "actual_start_datetime_local": START_TIME.strftime(TIMEFORMAT_1),
    "first_patient_xfer_to_or_table_datetime_local": START_TIME.strftime(TIMEFORMAT_1),
    "second_patient_xfer_to_or_table_datetime_local": None,
    "num_times_patient_xfer_to_or_table_in_case": 1,
    "first_patient_draped_datetime_local": (
        START_TIME + datetime.timedelta(hours=1)
    ).strftime(TIMEFORMAT_1),
    "second_patient_draped_datetime_local": (
        START_TIME + datetime.timedelta(hours=2)
    ).strftime(TIMEFORMAT_2),
    "num_times_patient_draped_in_case": 2,
    "first_patient_undraped_datetime_local": None,
    "second_patient_undraped_datetime_local": None,
    "num_times_patient_undraped_in_case": 0,
    "first_patient_xfer_to_bed_datetime_local": None,
    "second_patient_xfer_to_bed_datetime_local": None,
    "num_times_patient_xfer_to_bed_in_case": 0,
    "first_case_closing_datetime_local": None,
    "second_case_closing_datetime_local": None,
    "num_times_case_closing_in_case": 0,
    "updated_time": UPDATED_TIME.strftime(TIMEFORMAT_2),
    "timezone": "America/New_York",
}


class TestFeatureProcessor:
    def test_apply_schema(self):
        after_dict = test_dict
        df = CDCFeatureProcessor._apply_schema(
            PythiaFeaturesSchema.model_validate(after_dict)
        )
        assert df.index.values[0] == "1"
        assert df["actual_start_datetime_local"].values[0].to_pydatetime() == START_TIME
        assert (
            df["first_patient_xfer_to_or_table_datetime_local"].values[0] == START_TIME
        )
        assert df["second_patient_xfer_to_or_table_datetime_local"].values[0] is None
        assert df["num_times_patient_xfer_to_or_table_in_case"].values[0] == 1
        assert df["first_patient_draped_datetime_local"].values[
            0
        ] == START_TIME + datetime.timedelta(hours=1)
        assert df["second_patient_draped_datetime_local"].values[
            0
        ] == START_TIME + datetime.timedelta(hours=2)
        assert df["num_times_patient_draped_in_case"].values[0] == 2
        assert df["first_patient_undraped_datetime_local"].values[0] is None
        assert df["second_patient_undraped_datetime_local"].values[0] is None
        assert df["num_times_patient_undraped_in_case"].values[0] == 0
        assert df["first_patient_xfer_to_bed_datetime_local"].values[0] is None
        assert df["second_patient_xfer_to_bed_datetime_local"].values[0] is None
        assert df["num_times_patient_xfer_to_bed_in_case"].values[0] == 0
        assert df["first_case_closing_datetime_local"].values[0] is None
        assert df["second_case_closing_datetime_local"].values[0] is None
        assert df["num_times_case_closing_in_case"].values[0] == 0

    @pytest.mark.parametrize("op", ["c", "u"])
    def test_feature_processor(self, op: str):
        after_dict = test_dict
        data_str = json.dumps(
            {
                "op": op,
                "after_json": json.dumps(after_dict),
            }
        )

        message = MagicMock(data=data_str.encode("utf-8"))

        instance = MagicMock()
        store_features = MagicMock()
        # Patch the FeatureStore class and the store_features method so we can assert that it was called
        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(store_features=store_features),
        ):
            # Create a FeatureProcessor object
            feature_processor = CDCFeatureProcessor(instance, "TEST")
            apply_schema = MagicMock()
            feature_processor._apply_schema = apply_schema  # type: ignore

            # Call the callback method
            feature_processor.callback(message)
            store_features.assert_called_once_with(
                apply_schema.return_value, timestamp=UPDATED_TIME
            )

    def test_feature_processor_log_assertion_error(self, caplog):
        after_dict = test_dict.copy()
        before_dict = test_dict.copy()

        # Set first_event to a fixed time
        first_event_time = UPDATED_TIME.isoformat()
        after_dict["first_patient_xfer_to_or_table_datetime_local"] = first_event_time

        # Set second_event to a time after first_event
        second_event_time = UPDATED_TIME.isoformat()
        after_dict["second_patient_xfer_to_or_table_datetime_local"] = second_event_time

        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        message = MagicMock(data=data_str.encode("utf-8"))

        instance = MagicMock()

        # Create a FeatureProcessor object
        feature_processor = CDCFeatureProcessor(instance, "TEST")
        apply_schema = MagicMock()
        feature_processor._apply_schema = apply_schema  # type: ignore

        # Call the callback method and check for warning log
        feature_processor.callback(message)

        assert len(caplog.records) == 1

        # Verify the warning log contains the assertion error message
        assert (
            "Failed to process message due to assertion error"
            in caplog.records[0].message
        )

    @patch(
        "feature_processor.processors.cdc_feature_processor.FEATURE_STALENESS_SECONDS_HISTOGRAM"
    )
    def test_feature_staleness_metric(self, mock_histogram: MagicMock):
        after_dict = test_dict.copy()
        after_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=120))
        before_dict = test_dict.copy()
        before_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=180))
        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        message = MagicMock(data=data_str.encode("utf-8"))

        instance = MagicMock()
        store_features = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(store_features=store_features),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")
            feature_processor.callback(message)

            # Assert that the histogram was observed with approximately 120 seconds
            mock_histogram.observe.assert_called_once()
            observed_value = mock_histogram.observe.call_args[0][0]
            assert 110 <= observed_value <= 130, (
                "Staleness should be around 120 seconds"
            )

    @patch(
        "feature_processor.processors.cdc_feature_processor.FEATURE_STALENESS_SECONDS_HISTOGRAM"
    )
    def test_feature_staleness_metric_excluded(self, mock_histogram: MagicMock):
        after_dict = test_dict.copy()
        after_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=100))
        before_dict = test_dict.copy()
        before_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=90))
        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        message = MagicMock(data=data_str.encode("utf-8"))

        instance = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")
            feature_processor.callback(message)

            # Assert that the histogram was not observed since after_source_ts < before_source_ts
            mock_histogram.observe.assert_not_called()

    @patch(
        "feature_processor.processors.cdc_feature_processor.FEATURE_STALENESS_SECONDS_HISTOGRAM"
    )
    def test_feature_staleness_metric_before_after_source_ts_order(
        self, mock_histogram: MagicMock
    ):
        # Prepare after_data with a source_timestamp 100 seconds ago
        after_dict = test_dict.copy()
        after_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=100))

        # Prepare before_data with a source_timestamp 200 seconds ago
        before_dict = test_dict.copy()
        before_dict["source_timestamp"] = diff_utc_now(datetime.timedelta(seconds=200))

        # Create CDC message data
        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        # Mock the Pub/Sub message
        message = MagicMock(data=data_str.encode("utf-8"))

        # Mock the FeatureStore and its store_features method
        instance = MagicMock()
        store_features = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(store_features=store_features),
        ):
            # Instantiate the FeatureProcessor
            feature_processor = CDCFeatureProcessor(instance, "TEST")

            # Process the message
            feature_processor.callback(message)

            # Assert that the histogram was observed
            mock_histogram.observe.assert_called_once()

            # Assert that store_features was called once with the correct parameters
            store_features.assert_called_once()

    @pytest.mark.parametrize(
        "event_name",
        [
            "patient_xfer_to_or_table",
            "patient_draped",
            "patient_undraped",
            "patient_xfer_to_bed",
        ],
    )
    def test_process_cdc_message_event_ordering_failure(self, event_name: str) -> None:
        """
        Test that an AssertionError is raised when the second event occurs before the first event
        by directly calling process_cdc_message.
        """
        after_dict = test_dict.copy()
        before_dict = test_dict.copy()

        # Set first_event to a fixed time
        first_event_time = UPDATED_TIME.isoformat()
        after_dict[f"first_{event_name}_datetime_local"] = first_event_time

        # Set second_event to same time as first_event
        second_event_time = UPDATED_TIME.isoformat()
        after_dict[f"second_{event_name}_datetime_local"] = second_event_time

        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        instance = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")

            with pytest.raises(AssertionError) as exc_info:
                feature_processor.process_message(data_str)

            assert (
                f"second_{event_name.replace('second_', '')}_datetime_local is before first_{event_name.replace('second_', '')}_datetime_local"
                in str(exc_info.value)
            )

    @pytest.mark.parametrize(
        "event_name",
        [
            "patient_xfer_to_or_table",
            "patient_draped",
            "patient_undraped",
            "patient_xfer_to_bed",
        ],
    )
    def test_process_cdc_message_event_ordering_success(self, event_name: str) -> None:
        """
        Test that no AssertionError is raised when the second event occurs after the first event
        by directly calling process_cdc_message.
        """
        after_dict = test_dict.copy()
        before_dict = test_dict.copy()

        # Set first_event to a fixed time
        first_event_time = UPDATED_TIME.isoformat()
        after_dict[f"first_{event_name}_datetime_local"] = first_event_time

        # Set second_event to a time after first_event
        second_event_time = (UPDATED_TIME + datetime.timedelta(hours=1)).isoformat()
        after_dict[f"second_{event_name}_datetime_local"] = second_event_time

        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "before_json": json.dumps(before_dict),
                "schema_name": "PythiaSchema",
            }
        )

        instance = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")
            feature_processor.process_message(data_str)

            feature_processor._feature_store.store_features.assert_called_once()  # type: ignore

    def test_process_message_validation_error(self) -> None:
        """Test that validation errors are properly handled when required fields are missing."""
        # Create a message with missing required fields
        invalid_after_dict = {
            "case_id": "1",  # Only include case_id
            "timezone": "America/New_York",
        }

        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(invalid_after_dict),
            }
        )

        instance = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")

            with pytest.raises(ValueError) as exc_info:
                feature_processor.process_message(data_str)

            # Verify that the error message mentions the missing required field
            assert "actual_start_datetime_local" in str(exc_info.value)

    def test_message_updated_time_precedence(self) -> None:
        """Test that message updated_time takes precedence over after_json updated_time."""
        after_dict = test_dict.copy()
        after_dict["updated_time"] = UPDATED_TIME.strftime(TIMEFORMAT_2)

        # Create a different timestamp for the message level
        message_updated_time = UPDATED_TIME + datetime.timedelta(hours=1)

        data_str = json.dumps(
            {
                "op": "u",
                "after_json": json.dumps(after_dict),
                "updated_time": message_updated_time.strftime(TIMEFORMAT_2),
            }
        )

        instance = MagicMock()
        store_features = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(store_features=store_features),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")
            feature_processor.process_message(data_str)

            # Verify that store_features was called with the message's updated_time
            store_features.assert_called_once()
            _, kwargs = store_features.call_args
            assert kwargs["timestamp"] == message_updated_time

    @pytest.mark.parametrize(
        "test_case,message_data",
        [
            (
                "invalid_json_format",
                json.dumps(
                    {
                        "op": "u",
                        "after_json": "not_valid_json",
                    }
                ),
            ),
            (
                "missing_after_json",
                json.dumps(
                    {
                        "op": "u",
                    }
                ),
            ),
            (
                "invalid_timezone_in_updated_time",
                json.dumps(
                    {
                        "op": "u",
                        "after_json": json.dumps(test_dict),
                        "updated_time": "2024-01-01T00:00:00+invalid",
                    }
                ),
            ),
            (
                "invalid_before_json",
                json.dumps(
                    {
                        "op": "u",
                        "after_json": json.dumps(test_dict),
                        "before_json": "not_valid_json",
                    }
                ),
            ),
        ],
    )
    def test_pythia_message_schema_validation(
        self, test_case: str, message_data: str
    ) -> None:
        """Test various validation scenarios for PythiaMessageSchema."""
        instance = MagicMock()

        with patch(
            "feature_processor.processors.cdc_feature_processor.FeatureStore",
            return_value=MagicMock(),
        ):
            feature_processor = CDCFeatureProcessor(instance, "TEST")

            with pytest.raises(ValueError) as exc_info:
                feature_processor.process_message(message_data)

            assert isinstance(exc_info.value, ValidationError), (
                f"Test case '{test_case}' failed"
            )
