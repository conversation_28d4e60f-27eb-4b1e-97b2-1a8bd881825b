import argparse
import logging

import prometheus_client
from google.cloud import bigtable  # type: ignore
from lib_python_logging.apella_logger import ApellaLogger
from feature_processor.processors.cdc_feature_processor import CDCFeatureProcessor
from feature_processor.processors.array_columns_feature_processor import (
    ArrayColumnsFeatureProcessor,
)
from feature_processor.processors.base_feature_processor import FeatureProcessorType

logger = logging.getLogger(__name__)


def create_feature_processor(
    processor_type: FeatureProcessorType, instance, table_name: str
):
    """
    Factory function to create the appropriate feature processor based on type.

    Args:
        processor_type: The type of processor to create
        instance: The Bigtable instance
        table_name: The name of the table to process

    Returns:
        A feature processor instance

    Raises:
        ValueError: If the processor type is not supported
    """
    if processor_type == FeatureProcessorType.FORECASTING:
        return CDCFeatureProcessor(instance, table_name)
    elif processor_type == FeatureProcessorType.EVENT_MODEL:
        return ArrayColumnsFeatureProcessor(instance, table_name)
    else:
        raise ValueError(f"Invalid processor type: {processor_type}")


def main() -> None:
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument("--GCP_PROJECT_ID", type=str, required=True)
    arg_parser.add_argument("--INSTANCE_ID", type=str, required=True)
    arg_parser.add_argument("--TABLE_NAME", type=str, required=True)
    arg_parser.add_argument("--MESSAGE_DATA", type=str, required=True)
    arg_parser.add_argument(
        "--FEATURE_PROCESSOR_TYPE",
        type=str,
        required=True,
        choices=[t.value for t in FeatureProcessorType],
        help=f"Type of feature processor to use. Valid choices: {[t.value for t in FeatureProcessorType]}",
    )
    arg_parser.add_argument("--PROMETHEUS_PORT", type=int, default=8000)
    arg_parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    arg_parser.add_argument(
        "--disable_json_logging",
        action="store_true",
        help="Print logs in human-readable format instead of JSON",
    )
    args = arg_parser.parse_args()

    log_level = logging.DEBUG if args.debug else logging.INFO
    ApellaLogger().setup_logging(
        log_level=log_level, use_json_logging=not args.disable_json_logging
    )

    GCP_PROJECT_ID = args.GCP_PROJECT_ID
    INSTANCE_ID = args.INSTANCE_ID
    TABLE_NAME = args.TABLE_NAME
    MESSAGE_DATA = args.MESSAGE_DATA
    PROMETHEUS_PORT = args.PROMETHEUS_PORT
    FEATURE_PROCESSOR_TYPE = FeatureProcessorType(args.FEATURE_PROCESSOR_TYPE)

    prometheus_client.start_http_server(PROMETHEUS_PORT)

    client = bigtable.Client(project=GCP_PROJECT_ID, admin=True)
    instance = client.instance(INSTANCE_ID)
    feature_processor = create_feature_processor(
        FEATURE_PROCESSOR_TYPE, instance, TABLE_NAME
    )

    # Process the message
    feature_processor.process_message(MESSAGE_DATA)


if __name__ == "__main__":
    main()
