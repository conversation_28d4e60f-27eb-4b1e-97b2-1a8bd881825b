from abc import ABC, abstractmethod
from enum import Enum

from google.cloud.pubsub_v1.subscriber.message import Message

import logging
import traceback
import json

from pydantic import ValidationError

logger = logging.getLogger(__name__)


class MessageAction(Enum):
    NACK = "nack"
    ACK = "ack"


class FeatureProcessorType(Enum):
    FORECASTING = "forecasting"
    EVENT_MODEL = "event_model"


class BaseFeatureProcessor(ABC):
    """
    Abstract base class for feature processors.
    This class defines the interface that all feature processors must implement.
    """

    @abstractmethod
    def process_message(self, message_data: str) -> None:
        """
        Process a single message.

        Args:
            message_data: The message data to process
        """
        pass

    def handle_success(self, message_data: str) -> None:
        """
        Handle successful message processing. Useful for logging or metrics.
        """
        pass

    def handle_assertion_exception(self, exc: Exception) -> MessageAction:
        """
        How to handle assertion errors. By default, we NACK the message. Please make sure to setup a dead letter topic so these message are not stuck in the queue indefinitely.
        """
        return MessageAction.NACK

    def handle_exception(self, exc: Exception) -> None:
        pass

    def callback(self, message: Message) -> None:
        try:
            logger.debug(f"Received message: {message}")
            data = message.data.decode("utf-8")
            self.process_message(data)
            self.handle_success(message)
            message.ack()

        except (ValidationError, AssertionError) as exc:
            action = self.handle_assertion_exception(exc)
            logger.warning(
                f"Failed to process message due to assertion error. Action taken: {action}. Error: {json.dumps(traceback.format_exc())}"
            )
            if action == MessageAction.NACK:
                message.nack()
            else:
                message.ack()

        except Exception as exc:
            # Catch all other exceptions raised in the callback, NACK the message, and squelch the exception.
            #
            # The behavior of the PubSub client as of v2.19.4 is that any exception encountered in a
            # callback nacks the message and then causes the entire subscription client to exit by
            # calling `set_exception()` on the Future returned by subscribe(). We don't have any
            # exceptions in our callback that should be fatal to the entire application, so we'll just
            # let PubSub know that this message failed to process and then carry on processing more
            # messages
            self.handle_exception(exc)
            logger.error(
                f"Failed to process message due to exception: {json.dumps(traceback.format_exc())}\n"
                f"Message data: {message.data.decode('utf-8')}\n"
            )
            message.nack()
