import datetime
import logging
from dateutil.parser import isoparse


from google.cloud.bigtable.instance import Instance
from google.cloud.bigtable.table import Table
from google.cloud.bigtable.row import Row
from feature_processor.processors.base_feature_processor import BaseFeatureProcessor
from pydantic import BaseModel, ConfigDict, field_validator, Field, AliasChoices

logger = logging.getLogger(__name__)


class FloatFeature(BaseModel):
    name: str
    value: float
    model_config = ConfigDict(strict=True)


class FloatArrayFeature(BaseModel):
    name: str
    value: list[float]
    model_config = ConfigDict(strict=True)


class MessageData(BaseModel):
    float_features: list[FloatFeature]
    float_array_features: list[FloatArrayFeature]
    room_id: str
    time_window_start_utc: datetime.datetime
    updated_time: datetime.datetime = Field(
        validation_alias=AliasChoices("updated_time", "update_time")
    )

    model_config = ConfigDict(
        strict=True, extra="ignore"
    )  # ignore extra fields in the message, but do not coerce types

    @field_validator("time_window_start_utc", "updated_time", mode="before")
    def parse_and_ensure_utc(cls, v: str | datetime.datetime) -> datetime.datetime:
        if isinstance(v, datetime.datetime):
            return v if v.tzinfo else v.replace(tzinfo=datetime.timezone.utc)

        dt = isoparse(v)
        if dt.tzinfo is None:
            return dt.replace(tzinfo=datetime.timezone.utc)
        return dt.astimezone(datetime.timezone.utc)


class ArrayColumnsFeatureProcessor(BaseFeatureProcessor):
    """This class processes messages directly into Bigtable. The input message contains two columns, for the two types of data we can handle:
    - float_features - a list of float features, where each record contains a name and a value.
      For example: [{"name":"confidence_sum_or_table_occupied","value":5.48259}, ...]
    - float_array_features - list of float arrays, where each record contains a name and an array
      of floats.
      For example: [{"name":"resnet34_512_sum","value":[5.48259, 5.48259, 5.48259]}, ...]

    Float_features will be stored as scaled integers. Float_array_features will be stored as space-separated strings or scaled integers.
    """

    SIGNIFICANT_DIGITS = 3
    MULTIPLIER = 10**SIGNIFICANT_DIGITS

    def __init__(self, instance: Instance, table_name: str) -> None:
        self._table: Table = instance.table(table_name)

    def _generate_row_key(
        self, room_id: str, time_window_start_utc: datetime.datetime
    ) -> str:
        """Generate a row key from room_id and timestamp."""
        return f"{room_id}#{time_window_start_utc.isoformat()}"

    def process_message(self, message_data: str) -> None:
        """
        Process a single message and store it in Bigtable.

        Args:
            message_data: JSON string containing the message data
        """
        # Parse and validate the message data using Pydantic
        data = MessageData.model_validate_json(message_data)

        logger.debug(f"Processing message: {data}")

        # TODO: require a timestamp in the message. Alternatively,consider using the message publish time as a fallback. See CV-175
        timestamp_utc = data.updated_time

        # Generate row key and create row
        row_key = self._generate_row_key(data.room_id, data.time_window_start_utc)
        row = self._table.row(row_key)

        # Process features
        for float_feature in data.float_features:
            self._add_float_feature_to_row(
                row, float_feature.name, float_feature.value, timestamp_utc
            )

        for float_array_feature in data.float_array_features:
            self._add_float_array_feature_to_row(
                row, float_array_feature.name, float_array_feature.value, timestamp_utc
            )

        logger.debug(f"Storing values for row key: {row_key}")
        row.commit()

    def _add_float_feature_to_row(
        self,
        row: Row,
        feature_name: str,
        feature_value: float,
        timestamp: datetime.datetime,
    ) -> None:
        """Add a float feature to the row as a scaled integer."""
        scaled_feature_value = int(round(feature_value * self.MULTIPLIER))
        feature_as_bytes = scaled_feature_value.to_bytes(
            length=8, byteorder="big", signed=True
        )
        row.set_cell(
            "features",
            feature_name,
            feature_as_bytes,
            timestamp=timestamp,
        )

    def _add_float_array_feature_to_row(
        self,
        row: Row,
        feature_name: str,
        feature_value: list[float],
        timestamp: datetime.datetime,
    ) -> None:
        """Add a float array feature to the row as a comma-separated string of scaled integers."""
        scaled_feature_value = ",".join(
            f"{int(round(y * self.MULTIPLIER))}" for y in feature_value
        ).encode("utf-8")
        row.set_cell(
            "features",
            feature_name,
            scaled_feature_value,
            timestamp=timestamp,
        )
