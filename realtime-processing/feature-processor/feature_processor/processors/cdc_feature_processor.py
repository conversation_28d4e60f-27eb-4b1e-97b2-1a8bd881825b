import datetime
import json
import traceback
from zoneinfo import ZoneInfo

import pandera as pa
import pandas as pd
from dateutil import parser
from feature_store import FeatureStore
from google.cloud.bigtable.instance import Instance  # type: ignore
import logging

from prometheus_client import Counter, Histogram

from feature_processor.schemas.forecasting_schema import ForecastingSchema
from feature_processor.dtos.forecasting import PythiaFeaturesSchema, PythiaMessageSchema
from feature_processor.processors.base_feature_processor import (
    BaseFeatureProcessor,
    MessageAction,
)

logger = logging.getLogger(__name__)

FEATURE_PROCESSOR_FEATURES_STATUS_COUNTER = Counter(
    "feature_processor_features_status",
    "Status of features processed by the feature processor",
    ["status"],
)

FEATURE_STALENESS_SECONDS_HISTOGRAM = Histogram(
    "feature_staleness_seconds",
    "Staleness of the feature data in seconds",
    # We want to track the staleness of the data in seconds, so we'll use buckets that are powers of 5
    buckets=[
        1,
        5,
        25,
        125,
        625,
        3125,
        15625,
        78125,
        390625,
        float("inf"),
    ],
)


class CDCFeatureProcessor(BaseFeatureProcessor):
    """This class is responsible for processing the CDC messages and storing the features in the feature store. For now, it only supports the forecasting schema."""

    _feature_store: FeatureStore

    def __init__(self, instance: Instance, table_name: str) -> None:
        # Eventually we'll have multiple schemas and this will be more complex
        self._feature_store = FeatureStore(instance, table_name, ForecastingSchema)

    @staticmethod
    def _parse_to_datetime(date_str: str, timezone: str) -> datetime.datetime | None:
        """
        Parse a date string into a datetime object, returning None if the string is not in a valid format
        :param date_str:
        :param timezone:
        :return:
        """
        try:
            return parser.isoparse(date_str).astimezone(ZoneInfo(timezone))
        except ValueError:
            return None

    @staticmethod
    def _apply_schema(
        after_data: PythiaFeaturesSchema,
    ) -> pa.typing.DataFrame[ForecastingSchema]:
        """
        Apply the schema to the data and return a DataFrame
        :param data:
        :param timezone:
        :return:
        """
        raw_features = {}

        for column_name, column_val in after_data.model_dump().items():
            if column_name not in {"updated_time", "timezone", "source_timestamp"}:
                raw_features[column_name] = column_val

        return pa.typing.DataFrame[ForecastingSchema](
            pd.DataFrame([raw_features]).set_index("case_id", drop=True)
        )

    def _handle_staleness_metric(
        self, after_data: PythiaFeaturesSchema, before_data: PythiaFeaturesSchema | None
    ) -> None:
        """
        Handle the source_timestamp logic for feature staleness metrics.

        - Calculates staleness based on the 'source_timestamp' field.
        - Records the staleness in the Prometheus histogram.
        - Excludes metrics if 'after_source_timestamp' is less than 'before_source_timestamp'.
        """
        # Extract source_timestamp from after_data and before_data
        after_source_ts = after_data.source_timestamp
        before_source_ts = before_data.source_timestamp if before_data else None

        if after_source_ts and before_source_ts and after_source_ts < before_source_ts:
            logger.debug("Excluded metrics due to older source_timestamp.")
        elif after_source_ts:
            current_time = datetime.datetime.now(datetime.timezone.utc)
            staleness_seconds = (current_time - after_source_ts).total_seconds()
            FEATURE_STALENESS_SECONDS_HISTOGRAM.observe(staleness_seconds)

    @staticmethod
    def _validate_event_ordering(
        first_event: datetime.datetime | None, second_event: datetime.datetime | None
    ) -> bool:
        """
        Validate that the second event is after the first event.
        """
        if first_event and second_event:
            return second_event > first_event
        return True

    def process_message(self, message_data: str) -> None:
        """
        Process the CDC message and apply it to Bigtable.
        """
        # Parse the CDC message
        message_schema = PythiaMessageSchema.model_validate_json(message_data)
        operation = message_schema.op
        before_data = message_schema.before_json
        after_data = message_schema.after_json
        updated_time = message_schema.updated_time
        assert after_data is not None, (
            f"No data found in after_json for CDC message {message_data}"
        )

        if operation not in {"c", "u"}:  # Create or Update
            print(f"Skipping CDC message {message_data} due to operation {operation}")
            return

        timestamp = updated_time or after_data.updated_time

        for row_key in after_data.model_fields:
            if row_key.startswith("first_") and row_key.endswith("_datetime_local"):
                second_row_key = row_key.replace("first_", "second_")
                assert self._validate_event_ordering(
                    getattr(after_data, row_key),
                    getattr(after_data, second_row_key),
                ), f"{second_row_key} is before {row_key} in CDC message {message_data}"

        features = self._apply_schema(after_data)  # type: ignore

        self._feature_store.store_features(features, timestamp=timestamp)

        # Handle source_timestamp logic
        self._handle_staleness_metric(after_data, before_data)

    def handle_success(self, message_data: str) -> None:
        FEATURE_PROCESSOR_FEATURES_STATUS_COUNTER.labels(status="success").inc()

    def handle_assertion_exception(self, exc: Exception) -> MessageAction:
        # Catch assertion errors and ACK the message, we don't want to retry processing this message
        # since it's a problem with the data itself and not the processing logic
        # but we do want to log the error
        FEATURE_PROCESSOR_FEATURES_STATUS_COUNTER.labels(
            status="assertion_failed"
        ).inc()
        return MessageAction.ACK

    def handle_exception(self, exc: Exception) -> None:
        FEATURE_PROCESSOR_FEATURES_STATUS_COUNTER.labels(status="failure").inc()
        logger.error(
            f"Failed to process message due to exception: {json.dumps(traceback.format_exc())}"
        )
