import datetime
from typing import TypeVar, Generic
from zoneinfo import ZoneInfo

import pandas as pd
from pydantic import BaseModel, field_validator, model_validator

T = TypeVar("T")


class FeaturePair(BaseModel, Generic[T]):
    name: str
    value: T


class ForecastingSchemaInput(BaseModel):
    case_id: str
    string_features: list[FeaturePair[str]]
    int_features: list[FeaturePair[int]]
    source_timestamp: datetime.datetime

    @field_validator("source_timestamp", mode="before")
    def validate_update_time(cls, v: str | datetime.datetime) -> datetime.datetime:
        if isinstance(v, str):
            return pd.to_datetime(v)
        return v


class MessageSchema(BaseModel):
    op: str
    before_data: ForecastingSchemaInput | None
    after_data: ForecastingSchemaInput | None
    updated_time: datetime.datetime

    @field_validator("updated_time", mode="before")
    def validate_update_time(cls, v: str | datetime.datetime) -> datetime.datetime:
        if isinstance(v, str):
            return pd.to_datetime(v)
        return v


class SimpleForecastingSchemaModel(BaseModel):
    case_id: str

    org_id: str
    site_id: str
    scheduled_starting_hour: int
    day_of_week: str
    running_during_lunch: int


class PythiaFeaturesSchema(BaseModel):
    case_id: str

    actual_start_datetime_local: datetime.datetime

    first_patient_xfer_to_or_table_datetime_local: datetime.datetime | None = None
    second_patient_xfer_to_or_table_datetime_local: datetime.datetime | None = None
    num_times_patient_xfer_to_or_table_in_case: float

    first_patient_draped_datetime_local: datetime.datetime | None = None
    second_patient_draped_datetime_local: datetime.datetime | None = None
    num_times_patient_draped_in_case: float

    first_patient_undraped_datetime_local: datetime.datetime | None = None
    second_patient_undraped_datetime_local: datetime.datetime | None = None
    num_times_patient_undraped_in_case: float

    first_patient_xfer_to_bed_datetime_local: datetime.datetime | None = None
    second_patient_xfer_to_bed_datetime_local: datetime.datetime | None = None
    num_times_patient_xfer_to_bed_in_case: float

    first_case_closing_datetime_local: datetime.datetime | None = None
    second_case_closing_datetime_local: datetime.datetime | None = None
    num_times_case_closing_in_case: float = 0

    static_forecasted_duration: float | None = None

    updated_time: datetime.datetime
    source_timestamp: datetime.datetime | None = None
    timezone: ZoneInfo

    @field_validator("actual_start_datetime_local", "updated_time", mode="before")
    def validate_actual_start_datetime_local(
        cls, v: str | datetime.datetime
    ) -> datetime.datetime:
        if isinstance(v, str):
            return pd.to_datetime(v)
        return v

    @field_validator(
        "first_patient_xfer_to_or_table_datetime_local",
        "second_patient_xfer_to_or_table_datetime_local",
        "first_patient_draped_datetime_local",
        "second_patient_draped_datetime_local",
        "first_patient_undraped_datetime_local",
        "second_patient_undraped_datetime_local",
        "first_patient_xfer_to_bed_datetime_local",
        "second_patient_xfer_to_bed_datetime_local",
        "first_case_closing_datetime_local",
        "second_case_closing_datetime_local",
        "source_timestamp",
        mode="before",
    )
    def validate_first_patient_xfer_to_or_table_datetime_local(
        cls, v: str | datetime.datetime | None
    ) -> datetime.datetime | None:
        if v is None:
            return None
        if isinstance(v, str):
            return pd.to_datetime(v)
        return v

    @field_validator("timezone", mode="before")
    def validate_timezone(cls, v: str) -> ZoneInfo:
        return ZoneInfo(v)

    # Convert all datetime fields to the timezone
    @model_validator(mode="after")
    def convert_datetime_fields_to_timezone(self) -> "PythiaFeaturesSchema":
        for field in self.model_fields:
            if isinstance(getattr(self, field), datetime.datetime):
                setattr(self, field, getattr(self, field).astimezone(self.timezone))
        return self


class PythiaMessageSchema(BaseModel):
    op: str
    before_json: PythiaFeaturesSchema | None = None
    after_json: PythiaFeaturesSchema | None = None
    updated_time: datetime.datetime | None = None

    @field_validator("updated_time", mode="before")
    def validate_update_time(
        cls, v: str | datetime.datetime | None
    ) -> datetime.datetime | None:
        if v is None:
            return None
        if isinstance(v, str):
            return pd.to_datetime(v)
        return v

    @field_validator("after_json", "before_json", mode="before")
    def validate_json(cls, v: str | None) -> PythiaFeaturesSchema | None:
        if v is None:
            return None
        return PythiaFeaturesSchema.model_validate_json(v)

    @model_validator(mode="after")
    def validate_model(self) -> "PythiaMessageSchema":
        if self.op in {"c", "u"} and self.after_json is None:
            raise ValueError("after_json is required for create or update operations")
        return self
