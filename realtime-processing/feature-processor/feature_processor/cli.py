import argparse
import logging
import concurrent.futures as futures

import prometheus_client
from google.cloud import bigtable  # type: ignore
from google.cloud import pubsub_v1
from concurrent.futures import TimeoutError

from lib_python_logging.apella_logger import ApellaLogger
from feature_processor.processors.cdc_feature_processor import CDCFeatureProcessor
from feature_processor.processors.array_columns_feature_processor import (
    ArrayColumnsFeatureProcessor,
)
from feature_processor.processors.base_feature_processor import (
    FeatureProcessorType,
    BaseFeatureProcessor,
)

logger = logging.getLogger(__name__)


def create_feature_processor(
    processor_type: FeatureProcessorType, instance, table_name: str
) -> BaseFeatureProcessor:
    """
    Factory function to create the appropriate feature processor based on type.

    Args:
        processor_type: The type of processor to create
        instance: The Bigtable instance
        table_name: The name of the table to process

    Returns:
        A feature processor instance

    Raises:
        ValueError: If the processor type is not supported
    """
    if processor_type == FeatureProcessorType.FORECASTING:
        return CDCFeatureProcessor(instance, table_name)
    elif processor_type == FeatureProcessorType.EVENT_MODEL:
        return ArrayColumnsFeatureProcessor(instance, table_name)
    else:
        raise ValueError(f"Invalid processor type: {processor_type}")


def main() -> None:
    arg_parser = argparse.ArgumentParser()
    arg_parser.add_argument("--GCP_PROJECT_ID", type=str, required=True)
    arg_parser.add_argument("--INSTANCE_ID", type=str, required=True)

    arg_parser.add_argument("--PUB_SUB_SUBSCRIPTION", type=str, required=True)

    arg_parser.add_argument(
        "--FEATURE_PROCESSOR_TYPE",
        type=str,
        required=True,
        choices=[t.value for t in FeatureProcessorType],
        help=f"Type of feature processor to use. Valid choices: {[t.value for t in FeatureProcessorType]}",
    )
    arg_parser.add_argument("--TABLE_NAME", type=str, required=True)
    arg_parser.add_argument("--PROMETHEUS_PORT", type=int, default=8000)

    arg_parser.add_argument(
        "--debug_mode",
        action="store_true",
        help="Enable debug mode: debug level logging, no JSON logging, single worker thread",
    )
    args = arg_parser.parse_args()

    log_level = logging.DEBUG if args.debug_mode else logging.INFO
    ApellaLogger().setup_logging(
        log_level=log_level, use_json_logging=not args.debug_mode
    )

    GCP_PROJECT_ID = args.GCP_PROJECT_ID
    INSTANCE_ID = args.INSTANCE_ID
    PUB_SUB_SUBSCRIPTION = args.PUB_SUB_SUBSCRIPTION
    TABLE_NAME = args.TABLE_NAME
    PROMETHEUS_PORT = args.PROMETHEUS_PORT
    FEATURE_PROCESSOR_TYPE = FeatureProcessorType(args.FEATURE_PROCESSOR_TYPE)

    prometheus_client.start_http_server(PROMETHEUS_PORT)

    client = bigtable.Client(project=GCP_PROJECT_ID, admin=True)
    instance = client.instance(INSTANCE_ID)
    feature_processor = create_feature_processor(
        FEATURE_PROCESSOR_TYPE, instance, TABLE_NAME
    )

    # Initialize Pub/Sub subscriber
    subscriber = pubsub_v1.SubscriberClient()
    subscription_path = subscriber.subscription_path(
        GCP_PROJECT_ID, PUB_SUB_SUBSCRIPTION
    )
    # Configure subscriber based on single_worker flag
    subscriber_kwargs = {
        "subscription": subscription_path,
        "callback": feature_processor.callback,
    }
    if args.debug_mode:
        scheduler = pubsub_v1.subscriber.scheduler.ThreadScheduler(
            futures.ThreadPoolExecutor(max_workers=1)
        )
        subscriber_kwargs["scheduler"] = scheduler
        logger.info("Using single worker thread for message processing")

    streaming_pull_future = subscriber.subscribe(**subscriber_kwargs)
    logger.info(f"Listening for messages on {subscription_path}...")

    # Wrap subscriber in a 'with' block to automatically call close() when done
    with subscriber:
        try:
            logger.info("Starting subscriber...")
            streaming_pull_future.result()
            logger.info("Subscriber stopped.")
        except TimeoutError:
            logger.error("Subscriber timed out")
            streaming_pull_future.cancel()
            streaming_pull_future.result()
