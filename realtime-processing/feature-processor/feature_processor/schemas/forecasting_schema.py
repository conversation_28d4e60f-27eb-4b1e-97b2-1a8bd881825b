import pandera as pa

from feature_store.feature_store import FeatureStoreDateTime


class ForecastingSchema(pa.DataFrameModel):
    class Config:
        # Try to coerce unvalidated data into the expected type. It's unfortunate that we have to do
        # this, but Pandas is so unpredictable with its type inference on, for example, empty
        # DataFrames or columns that are full of pd.NA, that it's the only practical way to use a
        # schema right now.
        coerce = True

        # Ensure that no extraneous or unexpected columns are provided
        strict = True

    case_id: pa.typing.Index[str]

    actual_start_datetime_local: FeatureStoreDateTime | None

    first_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = (
        pa.Field(nullable=True)
    )
    second_patient_xfer_to_or_table_datetime_local: FeatureStoreDateTime | None = (
        pa.Field(nullable=True)
    )
    num_times_patient_xfer_to_or_table_in_case: float | None

    first_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_patient_draped_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_patient_draped_in_case: float | None

    first_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_patient_undraped_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_patient_undraped_in_case: float | None

    first_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_patient_xfer_to_bed_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_patient_xfer_to_bed_in_case: float | None

    first_case_closing_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    second_case_closing_datetime_local: FeatureStoreDateTime | None = pa.Field(
        nullable=True
    )
    num_times_case_closing_in_case: float | None

    static_forecasted_duration: float | None = pa.Field(nullable=True)
