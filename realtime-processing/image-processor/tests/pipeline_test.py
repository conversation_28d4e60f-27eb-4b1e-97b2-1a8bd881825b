import functools
import json
import logging
import os
import random
import re
import string
import time
from collections.abc import Iterator
from concurrent import futures
from contextlib import contextmanager
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from queue import Queue
from socket import create_connection
from unittest import mock

import docker
import google.cloud.exceptions
import pandera as pa
import pytest
from feature_store import FeatureStore
from feature_store.entity_catalog import FeatureStoreDateTime, ImageSchema
from feature_store.feature_store import Load<PERSON><PERSON>uresResult
from google.api_core.client_options import ClientOptions
from google.cloud.bigtable import Client as BigTableClient
from google.cloud.bigtable.instance import Instance
from google.cloud.bigtable.table import Table
from google.cloud.pubsub import PublisherClient, SubscriberClient
from google.cloud.pubsub_v1.subscriber.message import Message
from google.cloud.storage import Blob
from google.cloud.storage import Client as GCSClient
from pytest_httpserver import HTTPServer

from image_processor.image_embedder import ImageEmbedder
from image_processor.object_detection import ObjectDetector
from image_processor.pipeline import process_one_image, start_pipeline

"""
Basic integration tests of the entire image processing pipeline
"""


def wait_for_docker_port(
    client: docker.DockerClient,
    container_id: str,
    internal_port: int,
) -> int:
    while True:
        public_ports = client.api.port(container_id, internal_port)
        if public_ports:
            return int(public_ports[0]["HostPort"])
        else:
            time.sleep(0.1)


def wait_for_port(host: str, port: int) -> None:
    while True:
        try:
            with create_connection((host, port)):
                break
        except Exception:
            pass


@pytest.fixture(scope="session")
def pubsub_emulator() -> Iterator[int]:
    client = docker.from_env()

    container = client.containers.run(
        "google/cloud-sdk:463.0.0-emulators",
        [
            "gcloud",
            "beta",
            "emulators",
            "pubsub",
            "start",
            "--project=test-project",
            "--host-port=0.0.0.0:8085",
        ],
        detach=True,
        remove=True,
        ports={"8085": None},
    )

    port = wait_for_docker_port(client, container.id, 8085)
    wait_for_port("127.0.0.1", port)

    os.environ["PUBSUB_EMULATOR_HOST"] = f"127.0.0.1:{port}"
    yield port
    del os.environ["PUBSUB_EMULATOR_HOST"]

    container.kill()


@pytest.fixture
def subscriber_client(pubsub_emulator: int) -> SubscriberClient:
    return SubscriberClient()


@pytest.fixture
def publisher_client(pubsub_emulator: int) -> PublisherClient:
    return PublisherClient()


@pytest.fixture(scope="session")
def bigtable_emulator() -> Iterator[int]:
    client = docker.from_env()

    container = client.containers.run(
        "google/cloud-sdk:455.0.0-emulators",
        [
            "gcloud",
            "beta",
            "emulators",
            "bigtable",
            "start",
            "--host-port=0.0.0.0:8086",
        ],
        detach=True,
        remove=True,
        ports={"8086": None},
    )

    port = wait_for_docker_port(client, container.id, 8086)
    wait_for_port("127.0.0.1", port)

    os.environ["BIGTABLE_EMULATOR_HOST"] = f"127.0.0.1:{port}"
    yield port
    del os.environ["BIGTABLE_EMULATOR_HOST"]

    # Graceful shutdown takes several seconds and is completely unncessary for this use case
    container.kill()


@pytest.fixture(scope="session")
def bigtable_instance(bigtable_client: BigTableClient) -> Iterator[Instance]:
    yield bigtable_client.instance("instance_name")


@pytest.fixture
def entity_table(bigtable_instance: Instance) -> Iterator[Table]:
    table_id = "ml_features_image_features"
    table = bigtable_instance.table(table_id)

    while True:
        try:
            table.create()
            break
        except google.api_core.exceptions.ServiceUnavailable:
            # Wait for the BigTable emulator service to be available
            pass

    yield table

    table.delete()


@pytest.fixture(scope="session")
def bigtable_client(bigtable_emulator: int) -> BigTableClient:
    client_options = ClientOptions(api_endpoint=f"localhost:{bigtable_emulator}")  # type: ignore[no-untyped-call]
    return BigTableClient(client_options=client_options, admin=True)


@pytest.fixture(scope="session")
def gcs_emulator() -> Iterator[int]:
    client = docker.from_env()

    container = client.containers.run(
        "fsouza/fake-gcs-server:1.47.8",
        ["-scheme", "http"],
        detach=True,
        remove=True,
        ports={"4443": None},
    )

    port = wait_for_docker_port(client, container.id, 4443)
    wait_for_port("127.0.0.1", port)

    os.environ["STORAGE_EMULATOR_HOST"] = f"http://127.0.0.1:{port}"
    yield port
    del os.environ["STORAGE_EMULATOR_HOST"]

    container.kill()


@pytest.fixture
def gcs_client(gcs_emulator: int) -> Iterator[GCSClient]:
    yield GCSClient()


@pytest.fixture
def input_pubsub_topic(publisher_client: PublisherClient) -> Iterator[str]:
    topic_name = publisher_client.topic_path(
        "test-project", f"test-input-topic-{''.join(random.choices(string.ascii_lowercase, k=10))}"
    )
    publisher_client.create_topic(name=topic_name)

    yield topic_name

    publisher_client.delete_topic(topic=topic_name)


@pytest.fixture
def input_pubsub_subscription(
    subscriber_client: SubscriberClient, input_pubsub_topic: str
) -> Iterator[str]:
    subscription = subscriber_client.create_subscription(topic=input_pubsub_topic)

    yield subscription.name

    subscriber_client.delete_subscription(subscription=subscription.name)


@pytest.fixture
def object_stats_output_topic(publisher_client: PublisherClient) -> Iterator[str]:
    topic_name = publisher_client.topic_path(
        "test-project",
        f"test-output-object-stats-topic-{''.join(random.choices(string.ascii_lowercase, k=10))}",
    )
    publisher_client.create_topic(name=topic_name)

    yield topic_name

    publisher_client.delete_topic(topic=topic_name)


@pytest.fixture
def object_stats_output_pubsub_subscription(
    subscriber_client: SubscriberClient, object_stats_output_topic: str
) -> Iterator[str]:
    subscription = subscriber_client.create_subscription(
        request={"topic": object_stats_output_topic, "enable_message_ordering": True}
    )

    yield subscription.name

    subscriber_client.delete_subscription(subscription=subscription.name)


@pytest.fixture
def image_embedding_output_topic(publisher_client: PublisherClient) -> Iterator[str]:
    topic_name = publisher_client.topic_path(
        "test-project",
        f"test-output-embedding-topic-{''.join(random.choices(string.ascii_lowercase, k=10))}",
    )
    publisher_client.create_topic(name=topic_name)

    yield topic_name

    publisher_client.delete_topic(topic=topic_name)


@pytest.fixture
def image_embedding_output_pubsub_subscription(
    subscriber_client: SubscriberClient, image_embedding_output_topic: str
) -> Iterator[str]:
    subscription = subscriber_client.create_subscription(
        request={"topic": image_embedding_output_topic, "enable_message_ordering": True}
    )

    yield subscription.name

    subscriber_client.delete_subscription(subscription=subscription.name)


@dataclass
class CallbackResult:
    message: Message
    acked: bool
    nacked: bool


def callback_spy(
    done_queue: Queue[CallbackResult],
    gcs_client: GCSClient,
    object_detector: ObjectDetector,
    image_embedder: ImageEmbedder,
    feature_store: FeatureStore[ImageSchema],
    publisher_client: PublisherClient,
    object_stats_output_topic_id: str,
    image_embedding_output_topic_id: str,
    calc_image_embedding: bool,
    message: Message,
) -> None:
    # The only way to tell if a message was ack'd or nack'd is to spy on it
    message_wrapper = mock.MagicMock(wraps=message)

    # wraps=True doesn't seem to work very well for dict attributes. Without this,
    # message.attributes[<key>] returns a MagicMock instance rather than the "wrapped" value.
    message_wrapper.attributes = message.attributes

    # json.dumps() does an isinstance check on its arguments, so the data has to be the real thing
    # and not a wrapper.
    message_wrapper.data = message.data

    message_wrapper.message_id = message.message_id
    message_wrapper.delivery_attempt = message.delivery_attempt

    # If this raises an exception, just bubble it up. It will eventually cause the test to fail when
    # the subscription's result is waited on in pipeline_started().
    process_one_image(
        gcs_client,
        object_detector,
        image_embedder,
        feature_store,
        publisher_client,
        object_stats_output_topic_id,
        image_embedding_output_topic_id,
        calc_image_embedding,
        message_wrapper,
    )

    done_queue.put(CallbackResult(message, message_wrapper.ack.called, message_wrapper.nack.called))


@contextmanager
def pipeline_started(
    subscriber_client: SubscriberClient,
    subscription_id: str,
    gcs_client: GCSClient,
    bigtable_client: BigTableClient,
    object_detection_svc_url: str,
    image_embedding_deployment_url: str,
    publisher_client: PublisherClient,
    object_stats_output_topic_id: str,
    image_embedding_output_topic_id: str,
) -> Iterator[Queue[CallbackResult]]:
    done_queue = Queue[CallbackResult]()
    callback = functools.partial(callback_spy, done_queue)

    pull_future = start_pipeline(
        subscriber_client,
        subscription_id,
        gcs_client,
        bigtable_client,
        object_detection_svc_url,
        image_embedding_deployment_url,
        "instance_name",
        publisher_client,
        object_stats_output_topic_id,
        image_embedding_output_topic_id,
        True,
        callback=callback,
    )

    try:
        yield done_queue
    finally:
        pull_future.cancel()
        pull_future.result()


def upload_test_image(
    gcs_client: GCSClient, bucket_id: str, object_id: str, local_filename: str = "sample_frame.jpg"
) -> Blob:
    # Upload an "image" - this doesn't have to be real yet.
    bucket = gcs_client.bucket(bucket_id)
    try:
        bucket.create()
    except google.cloud.exceptions.Conflict:
        pass

    blob = bucket.blob(object_id)
    blob.upload_from_filename(Path(__file__).parent / "resources" / f"{local_filename}")

    return blob


@pytest.fixture
def fake_object_detection_svc() -> Iterator[HTTPServer]:
    server = HTTPServer("127.0.0.1", 0)
    server.start()  # type: ignore[no-untyped-call]

    mock_predictions = json.dumps(
        [
            {
                "name": "coffee_mug",
                "confidence": 0.9,
                "xmin": 1.0,
                "ymin": 2.0,
                "xmax": 3.0,
                "ymax": 4.0,
            },
        ]
    )

    expectation = server.expect_request("/predict", "POST")
    expectation.respond_with_json(
        {
            "prediction_objects": [
                {
                    "name": "coffee_mug",
                    "confidence": 0.9,
                    "xmin": 1.0,
                    "ymin": 2.0,
                    "xmax": 3.0,
                    "ymax": 4.0,
                },
            ],
            "predictions": mock_predictions,
            "model_version": "1.2.3",
            "embedding": [0.1, 0.2, 0.3],
        }
    )

    yield server

    server.stop()  # type: ignore[no-untyped-call]


@pytest.fixture
def fake_image_embedder() -> Iterator[HTTPServer]:
    server = HTTPServer("127.0.0.1", 0)
    server.start()  # type: ignore[no-untyped-call]

    expectation = server.expect_request("/predict", method="POST")
    expectation.respond_with_json(
        {"model_name": "resnet34", "model_version": "v0.1", "embedding": [0.02, 3.2, 1.0]}
    )

    yield server

    server.stop()  # type: ignore[no-untyped-call]


def _create_message_body(camera_id: str, org_id: str, room_id: str, frame_time: datetime) -> bytes:
    # The full message looks like this, but our code only needs a portion of it:
    #
    # {
    #   "kind": "storage#object",
    #   "id": "dev-customer-apella-image-data/f07eaada75628fc84982766041c9c35a/111665b2b9fb936b919296aa23bc40bb/ATPA-OR1-CAM-0002_2024-03-01T05:13:00.674062+00:00.jpg/1709269981679906",
    #   "selfLink": "https://www.googleapis.com/storage/v1/b/dev-customer-apella-image-data/o/f07eaada75628fc84982766041c9c35a%2F111665b2b9fb936b919296aa23bc40bb%2FATPA-OR1-CAM-0002_2024-03-01T05:13:00.674062%2B00:00.jpg",
    #   "name": "f07eaada75628fc84982766041c9c35a/111665b2b9fb936b919296aa23bc40bb/ATPA-OR1-CAM-0002_2024-03-01T05:13:00.674062+00:00.jpg",
    #   "bucket": "dev-customer-apella-image-data",
    #   "generation": "1709269981679906",
    #   "metageneration": "1",
    #   "contentType": "image/jpeg",
    #   "timeCreated": "2024-03-01T05:13:01.681Z",
    #   "updated": "2024-03-01T05:13:01.681Z",
    #   "storageClass": "STANDARD",
    #   "timeStorageClassUpdated": "2024-03-01T05:13:01.681Z",
    #   "size": "163759",
    #   "md5Hash": "gQw7EiQ0jsog5yWdf9e4jA==",
    #   "mediaLink": "https://storage.googleapis.com/download/storage/v1/b/dev-customer-apella-image-data/o/f07eaada75628fc84982766041c9c35a%2F111665b2b9fb936b919296aa23bc40bb%2FATPA-OR1-CAM-0002_2024-03-01T05:13:00.674062%2B00:00.jpg?generation=1709269981679906&alt=media",
    #   "metadata": {
    #     "site_id": "palo_alto_1",
    #     "camera_id": "ATPA-OR1-CAM-0002",
    #     "timezone": "America/Los_Angeles",
    #     "resolution": "720",
    #     "org_id": "apella_internal_0",
    #     "room_id": "palo_alto_room_0",
    #     "capture_mode": "image",
    #     "instance_id": "palo_alto_room_0",
    #     "bit_rate": "16384",
    #     "frame_rate": "1",
    #     "frame_time": "2024-03-01T05:13:00.674062+00:00",
    #     "format": "image/jpeg"
    #   },
    #   "crc32c": "SNjofw==",
    #   "etag": "CKKi1fCm0oQDEAE="
    # }
    return json.dumps(
        {
            "metadata": {
                "camera_id": camera_id,
                "org_id": org_id,
                "room_id": room_id,
                "frame_time": frame_time.isoformat(),
            },
            "crc32c": "SNjofw==",
            "etag": "CKKi1fCm0oQDEAE=",
        }
    ).encode()


def test_process_wrong_type(
    gcs_client: GCSClient,
    bigtable_client: BigTableClient,
    entity_table: Table,
    subscriber_client: SubscriberClient,
    input_pubsub_topic: str,
    input_pubsub_subscription: str,
    fake_object_detection_svc: HTTPServer,
    fake_image_embedder: HTTPServer,
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    caplog: pytest.LogCaptureFixture,
) -> None:
    entity_table.column_family("features").create()
    with pipeline_started(
        subscriber_client,
        input_pubsub_subscription,
        gcs_client,
        bigtable_client,
        fake_object_detection_svc.url_for("/"),
        fake_image_embedder.url_for("/"),
        publisher_client,
        object_stats_output_topic,
        image_embedding_output_topic,
    ) as done_queue:
        publisher_client.publish(
            input_pubsub_topic,
            b"foo",
            eventType="OBJECT_METADATA_UPDATE",
            bucketId="some_bucket",
            objectId="some_object",
        ).result()

        result = done_queue.get()
        assert result.message.data == b"foo"
        assert result.nacked
        assert not result.acked

    assert (
        "image_processor.pipeline",
        logging.ERROR,
        "Got exception processing image: ValueError('Unexpected pubsub message event type: OBJECT_METADATA_UPDATE')",
    ) in caplog.record_tuples


def test_process_bad_image_data(
    gcs_client: GCSClient,
    bigtable_client: BigTableClient,
    entity_table: Table,
    subscriber_client: SubscriberClient,
    input_pubsub_topic: str,
    input_pubsub_subscription: str,
    fake_object_detection_svc: HTTPServer,
    fake_image_embedder: HTTPServer,
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    caplog: pytest.LogCaptureFixture,
) -> None:
    entity_table.column_family("features").create()
    blob = upload_test_image(gcs_client, "test-bucket", "test-object", "not_an_image.jpg")
    message_body = _create_message_body(
        "test_camera", "test_org", "test_room", datetime(1999, 12, 31, tzinfo=timezone.utc)
    )

    with pipeline_started(
        subscriber_client,
        input_pubsub_subscription,
        gcs_client,
        bigtable_client,
        fake_object_detection_svc.url_for("/"),
        fake_image_embedder.url_for("/"),
        publisher_client,
        object_stats_output_topic,
        image_embedding_output_topic,
    ) as done_queue:
        publisher_client.publish(
            input_pubsub_topic,
            message_body,
            eventType="OBJECT_FINALIZE",
            bucketId=blob.bucket.name,
            objectId=blob.name,
            objectGeneration=str(blob.generation),
        ).result()

        result = done_queue.get()
        assert result.message.data == message_body
        assert result.nacked
        assert not result.acked

    expected_message_pattern = re.compile(
        r"Failed to open blob as an image: cannot identify image file <_io\.BytesIO object at 0x[0-9a-f]+>"
    )
    assert any(
        expected_message_pattern.search(record.message)
        for record in caplog.records
        if record.levelno == logging.ERROR and record.name == "image_processor.pipeline"
    )


def test_process_one_image(
    gcs_client: int,
    bigtable_client: BigTableClient,
    bigtable_instance: Instance,
    entity_table: Table,
    subscriber_client: SubscriberClient,
    input_pubsub_topic: str,
    input_pubsub_subscription: str,
    fake_object_detection_svc: HTTPServer,
    fake_image_embedder: HTTPServer,
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    object_stats_output_pubsub_subscription: str,
    image_embedding_output_pubsub_subscription: str,
) -> None:
    """
    This is a full end-to-end test of the pipeline and asserts two things:
    1. The input message that's processed is properly acked in the input subscription
    2. The output message contains the right values given the input from the test double versions of
       GCS
    """

    entity_table.column_family("features").create()
    blob = upload_test_image(gcs_client, "test-bucket", "test-object")
    message_body = _create_message_body(
        "test_camera", "test_org", "test_room", datetime(1999, 12, 31, tzinfo=timezone.utc)
    )

    with pipeline_started(
        subscriber_client,
        input_pubsub_subscription,
        gcs_client,
        bigtable_client,
        fake_object_detection_svc.url_for("/"),
        fake_image_embedder.url_for("/"),
        publisher_client,
        object_stats_output_topic,
        image_embedding_output_topic,
    ) as done_queue:
        publisher_client.publish(
            input_pubsub_topic,
            message_body,
            eventType="OBJECT_FINALIZE",
            bucketId=blob.bucket.name,
            objectId=blob.name,
            objectGeneration=str(blob.generation),
        ).result()

        result = done_queue.get()
        assert result.message.data == message_body
        assert result.acked
        assert not result.nacked

    object_stats_message = subscriber_client.pull(
        subscription=object_stats_output_pubsub_subscription, max_messages=1
    )
    assert len(object_stats_message.received_messages) == 1

    resnet_embedding_received_message = object_stats_message.received_messages[0]
    assert resnet_embedding_received_message.message.ordering_key == ""

    resnet_embedding_published_output_message = json.loads(
        resnet_embedding_received_message.message.data
    )
    assert resnet_embedding_published_output_message == {
        "camera_id": "test_camera",
        "color_model_output": {
            "average_b": 0.20392156862745098,
            "average_g": 0.20392156862745098,
            "average_r": 0.25098039215686274,
        },
        "color_model_version": "image-processor",
        "frame_time": "1999-12-31T00:00:00+00:00",
        "frame_url": "gs://test-bucket/test-object",
        "guid": resnet_embedding_published_output_message["guid"],
        "object_model_output": [
            {
                "label_display_name": "coffee_mug",
                "confidence": 0.9,
                "x1": 1.0,
                "y1": 2.0,
                "x2": 3.0,
                "y2": 4.0,
            },
        ],
        "object_model_version": "1.2.3",
        "org_id": "test_org",
        "room_id": "test_room",
        "source": "k8s-image-processor",
    }
    resnet_embedding_response = subscriber_client.pull(
        subscription=image_embedding_output_pubsub_subscription, max_messages=2
    )
    assert len(resnet_embedding_response.received_messages) == 1

    resnet_embedding_received_message = resnet_embedding_response.received_messages[0]
    assert resnet_embedding_received_message.message.ordering_key == ""

    resnet_embedding_published_output_message = json.loads(
        resnet_embedding_received_message.message.data
    )
    assert resnet_embedding_published_output_message == {
        "camera_id": "test_camera",
        "frame_time": 946598400000000,
        "frame_url": "gs://test-bucket/test-object",
        "guid": resnet_embedding_published_output_message["guid"],
        "model_name": "resnet34",
        "model_version": "v0.1",
        "org_id": "test_org",
        "room_id": "test_room",
        "source": "k8s-image-processor",
        "embedding": [0.02, 3.2, 1.0],
    }

    row_id = "test_room#1999-12-31T00:00:00+00:00#test_camera"
    assert entity_table.read_row(row_id)

    feature_store = FeatureStore(
        instance=bigtable_instance,
        entity_type="image_features",
        entity_schema=ImageSchema,
    )

    class QuerySchema(pa.DataFrameModel):
        class Config:
            coerce = True
            strict = True

        image_id: pa.typing.Index[str]
        frame_time: FeatureStoreDateTime | None
        resnet34_imagenet1kv1_resize_crop_v1: list[float] | None = pa.Field(nullable=True)

    query_result: LoadFeaturesResult[QuerySchema] = feature_store.load_features(
        {row_id}, QuerySchema
    )
    assert len(query_result.entities_with_missing_features) == 0
    assert len(query_result.entities) == 1

    frame_time_value = query_result.entities.loc[row_id]["frame_time"]
    assert frame_time_value == datetime(1999, 12, 31, tzinfo=timezone.utc)


def test_process_many(
    gcs_client: GCSClient,
    bigtable_client: BigTableClient,
    entity_table: Table,
    subscriber_client: SubscriberClient,
    input_pubsub_topic: str,
    input_pubsub_subscription: str,
    fake_object_detection_svc: HTTPServer,
    fake_image_embedder: HTTPServer,
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
) -> None:
    """
    Process many pubsub notifications, which will hopefully expose any thread safety issues that
    might exist.
    """
    entity_table.column_family("features").create()
    blob = upload_test_image(gcs_client, "test-bucket", "test-object")
    message_body = _create_message_body(
        "test_camera", "test_org", "test_room", datetime(1999, 12, 31, tzinfo=timezone.utc)
    )

    with pipeline_started(
        subscriber_client,
        input_pubsub_subscription,
        gcs_client,
        bigtable_client,
        fake_object_detection_svc.url_for("/"),
        fake_image_embedder.url_for("/"),
        publisher_client,
        object_stats_output_topic,
        image_embedding_output_topic,
    ) as done_queue:
        publish_futures = [
            publisher_client.publish(
                input_pubsub_topic,
                message_body,
                eventType="OBJECT_FINALIZE",
                bucketId=blob.bucket.name,
                objectId=blob.name,
                objectGeneration=str(blob.generation),
            )
            for _ in range(1000)
        ]
        futures.wait(publish_futures)

        for _ in range(1000):
            result = done_queue.get()
            assert result.acked
            assert not result.nacked

    assert len(fake_object_detection_svc.log) == 1000
