import functools
import io
import json
import logging
import os
import time
import uuid
from collections.abc import Callable, Iterable
from datetime import datetime
from pathlib import Path
from typing import Any, cast

import pandas as pd
import pandera as pa
from feature_store import FeatureStore
from feature_store.entity_catalog import ImageSchema
from google.cloud.bigtable import Client as BigTableClient
from google.cloud.pubsub import PublisherClient, SubscriberClient
from google.cloud.pubsub_v1.subscriber.futures import StreamingPullFuture
from google.cloud.pubsub_v1.subscriber.message import Message
from google.cloud.pubsub_v1.types import FlowControl
from google.cloud.storage import Client as GCSClient
from lib_python_logging.apella_logger import ApellaLogger
from PIL import Image
from prometheus_client import Counter, Histogram
from prometheus_client.utils import INF

from image_processor.color_detection import AverageFrameColor, get_average_color_of_frame
from image_processor.image_embedder import EmbeddingModel, ImageEmbedder
from image_processor.object_detection import ObjectDetector
from image_processor.object_detection_definitions import BoundingBox

ApellaLogger().setup_logging(use_json_logging=True)
logger = logging.getLogger(__name__)


class HealthCheckLogger:
    _HEARBEAT_INTERVAL_SEC = 60
    _HEALTH_LOG_FILE = "/tmp/health_logs/health_check.log"

    def __init__(self, logger: logging.Logger) -> None:
        Path(os.path.dirname(self._HEALTH_LOG_FILE)).mkdir(exist_ok=True)
        logger.addHandler(
            logging.handlers.RotatingFileHandler(
                self._HEALTH_LOG_FILE, maxBytes=10000, backupCount=1
            )
        )
        self._logger = logger
        self._last_log_time = time.time()

    def log_heartbeat(self) -> None:
        now_time = time.time()
        if now_time - self._last_log_time > self._HEARBEAT_INTERVAL_SEC:
            self._logger.info("Processing images...")
            self._last_log_time = now_time


health_check_logger = HealthCheckLogger(logger)

_buckets: tuple[float, ...] = (
    0.005,
    0.01,
    0.025,
    0.05,
    0.075,
    0.1,
    0.25,
    0.5,
    0.75,
    1.0,
    2.5,
    5.0,
    10.0,
    30.0,
    60.0,
    120.0,
    240.0,
    480.0,
    960.0,
    INF,
)

_SOURCE_NAME: str = "k8s-image-processor"


def get_histogram(metric_name: str, metric_description: str) -> Histogram:
    return Histogram(metric_name, metric_description, buckets=_buckets)


PROCESS_ONE_IMAGE_LATENCY = get_histogram(
    "process_one_image_latency_seconds", "Total time to process one image"
)
PROCESS_ONE_IMAGE_STARTED = Counter(
    "process_one_image_started",
    "Monotonically increasing count of image processing pipelines started",
)
PROCESS_ONE_IMAGE_FAILED = Counter(
    "process_one_image_failed",
    "Monotonically increasing count of image processing pipeline failures",
)
PROCESS_ONE_IMAGE_SUCCEEDED = Counter(
    "process_one_image_succeeded",
    "Monotonically increasing count of image processing pipeline successes",
)
PROCESS_ONE_IMAGE_COMPLETED = Counter(
    "process_one_image_completed",
    "Monotonically increasing count of image processing pipeline completions (successes and failures)",
)
DOWNLOAD_IMAGE_LATENCY = get_histogram(
    "download_image_latency_seconds", "Total time to download image from GCS"
)
DETECT_OBJECTS_LATENCY = get_histogram(
    "detect_objects_latency_seconds", "Total time to retrieve detected objects from model"
)
COMPUTE_IMAGE_EMBEDDING_LATENCY = get_histogram(
    "compute_image_embedding_latency_seconds",
    "Total time to calculate the embedding of the image",
)
WRITE_EMBEDDING_FEATURE_STORE_LATENCY = get_histogram(
    "write_embedding_to_feature_store_latency_seconds",
    "Total time to write the embedding to feature store",
)

CALCULATE_AVERAGE_COLOR_LATENCY = get_histogram(
    "calclulate_average_color_latency_seconds",
    "Total time to calculate the average color of the image",
)
PUBLISH_OBJECT_COLOR_OUTPUT_MESSAGE_LATENCY = get_histogram(
    "publish_output_message_latency_seconds",
    "Total time to publish the results of the image processor to the output Pub/Sub topic",
)
PUBLISH_IMAGE_EMBEDDINGS_OUTPUT_MESSAGE_LATENCY = get_histogram(
    "publish_image_embeddings_output_message_latency_seconds",
    "Total time to publish the the image embedding to the output Pub/Sub topic",
)


def _download_image(
    gcs_client: GCSClient, bucket_id: str, object_id: str, generation: str
) -> bytes:
    blob = gcs_client.bucket(bucket_id).blob(object_id, generation=generation)
    return cast(bytes, blob.download_as_bytes())


def _serialize_object_stats_output_message(
    model_display_name: str,
    frame_bucket_id: str,
    frame_object_id: str,
    bounding_boxes: Iterable[BoundingBox],
    org_id: str,
    room_id: str,
    camera_id: str,
    frame_time: datetime,
    average_color: AverageFrameColor,
) -> bytes:
    object_data = [
        {
            "label_display_name": bounding_box.object_name,
            "confidence": float(bounding_box.confidence),
            "x1": bounding_box.x1,
            "x2": bounding_box.x2,
            "y1": bounding_box.y1,
            "y2": bounding_box.y2,
        }
        for bounding_box in bounding_boxes
    ]

    frame_url = f"gs://{frame_bucket_id}/{frame_object_id}"
    message = {
        "guid": str(uuid.uuid4()),
        "camera_id": camera_id,
        "color_model_output": {
            "average_r": average_color.R,
            "average_g": average_color.G,
            "average_b": average_color.B,
        },
        "color_model_version": "image-processor",
        "frame_time": frame_time.isoformat(),
        "frame_url": frame_url,
        "object_model_output": object_data,
        "object_model_version": model_display_name,
        "org_id": org_id,
        "room_id": room_id,
        "source": _SOURCE_NAME,
    }

    return json.dumps(message).encode()


def _process_one_image(
    gcs_client: GCSClient,
    object_detector: ObjectDetector,
    image_embedder: ImageEmbedder,
    feature_store: FeatureStore[ImageSchema],
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    calc_image_embedding: bool,
    message: Message,
) -> None:
    logger.debug(f"Raw message: {message}")

    if message.attributes["eventType"] != "OBJECT_FINALIZE":
        raise ValueError(f"Unexpected pubsub message event type: {message.attributes['eventType']}")

    # These Pub/Sub attributes are documented in the GCS documentation:
    # https://cloud.google.com/storage/docs/pubsub-notifications#attributes
    with DOWNLOAD_IMAGE_LATENCY.time():
        image_data = _download_image(
            gcs_client,
            message.attributes["bucketId"],
            message.attributes["objectId"],
            message.attributes["objectGeneration"],
        )
        logger.debug(f"Downloaded image ({len(image_data)} bytes)")

    # Check if the message contains a valid image
    try:
        with Image.open(io.BytesIO(image_data)):
            pass
    except Exception as e:
        logger.error(
            f"Failed to open blob as an image: {e}",
            extra={
                "message_id": message.message_id,
                "bucket_id": message.attributes["bucketId"],
                "object_id": message.attributes["objectId"],
            },
        )
        raise e

    with DETECT_OBJECTS_LATENCY.time():
        objects, object_detector_embedding = object_detector.object_detector_results(image_data)
        logger.debug(
            f"Detected objects in image with model version {objects.model_version}: {objects.bounding_boxes}"
        )

    with CALCULATE_AVERAGE_COLOR_LATENCY.time():
        average_color = get_average_color_of_frame(image_data)
        logger.debug(f"Average frame colors: {average_color}")

    message_data = json.loads(message.data)
    if calc_image_embedding:
        with COMPUTE_IMAGE_EMBEDDING_LATENCY.time():
            resnet_image_embedding = image_embedder.get_image_embedding(image_data)
            logger.debug(
                f"Computed image embedding with model name: {resnet_image_embedding.model_name} and version: {resnet_image_embedding.model_version}"
            )

        with WRITE_EMBEDDING_FEATURE_STORE_LATENCY.time():
            image_id = f"{message_data['metadata']['room_id']}#{message_data['metadata']['frame_time']}#{message_data['metadata']['camera_id']}"
            if (
                resnet_image_embedding.model_name == "resnet34"
                and resnet_image_embedding.model_version == "v0.1"
            ):
                df = pd.DataFrame(
                    {
                        # storing the embeddings as a list of a list of floats
                        "resnet34_imagenet1kv1_resize_crop_v1": [resnet_image_embedding.embedding],
                        "room_id": message_data["metadata"]["room_id"],
                        "camera_id": message_data["metadata"]["camera_id"],
                        "frame_time": datetime.fromisoformat(
                            message_data["metadata"]["frame_time"]
                        ),
                    },
                    index=[image_id],
                )
            else:
                raise ValueError(
                    f"Unexpected embedding model: model_name={resnet_image_embedding.model_name},model_version={resnet_image_embedding.model_version}"
                )
            feature_store.store_features(pa.typing.DataFrame[ImageSchema](df))
            logger.debug(f"Finished writing embedding to FeatureStore with key: {image_id}")

        with PUBLISH_IMAGE_EMBEDDINGS_OUTPUT_MESSAGE_LATENCY.time():
            frame_url = f"gs://{message.attributes['bucketId']}/{message.attributes['objectId']}"
            # this will be enriched with embedding data
            message_precursor: dict[str, Any] = {
                "guid": str(uuid.uuid4()),
                "org_id": message_data["metadata"]["org_id"],
                "room_id": message_data["metadata"]["room_id"],
                "camera_id": message_data["metadata"]["camera_id"],
                "source": _SOURCE_NAME,
                "frame_url": frame_url,
                "frame_time": int(
                    datetime.fromisoformat(message_data["metadata"]["frame_time"]).timestamp() * 1e6
                ),
            }

            def _publish_embedding_message(embedding: EmbeddingModel, data: dict[str, Any]) -> None:
                # .copy() to prevent modification of the original data
                data_copy = data.copy()
                data_copy["embedding"] = embedding.embedding
                data_copy["model_name"] = embedding.model_name
                data_copy["model_version"] = embedding.model_version
                message = json.dumps(data_copy).encode()
                publisher_client.publish(image_embedding_output_topic, message).result()
                logger.debug(f"Published image embedding output message: {message!r}")

            _publish_embedding_message(resnet_image_embedding, message_precursor)

    with PUBLISH_OBJECT_COLOR_OUTPUT_MESSAGE_LATENCY.time():
        object_stats_output_message = _serialize_object_stats_output_message(
            model_display_name=objects.model_version,
            frame_bucket_id=message.attributes["bucketId"],
            frame_object_id=message.attributes["objectId"],
            bounding_boxes=objects.bounding_boxes,
            org_id=message_data["metadata"]["org_id"],
            room_id=message_data["metadata"]["room_id"],
            camera_id=message_data["metadata"]["camera_id"],
            frame_time=datetime.fromisoformat(message_data["metadata"]["frame_time"]),
            average_color=average_color,
        )

        publisher_client.publish(object_stats_output_topic, object_stats_output_message).result()
        logger.debug(f"Published object and color output message: {object_stats_output_message!r}")

    message.ack()


@PROCESS_ONE_IMAGE_LATENCY.time()
def process_one_image(
    gcs_client: GCSClient,
    object_detector: ObjectDetector,
    image_embedder: ImageEmbedder,
    feature_store: FeatureStore[ImageSchema],
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    calc_image_embedding: bool,
    message: Message,
) -> None:
    try:
        PROCESS_ONE_IMAGE_STARTED.inc()
        _process_one_image(
            gcs_client,
            object_detector,
            image_embedder,
            feature_store,
            publisher_client,
            object_stats_output_topic,
            image_embedding_output_topic,
            calc_image_embedding,
            message,
        )
        PROCESS_ONE_IMAGE_SUCCEEDED.inc()
        health_check_logger.log_heartbeat()
    except Exception as e:
        # Catch all exceptions raised in the callback, NACK the message, and squelch the exception.
        #
        # The behavior of the PubSub client as of v2.19.4 is that any exception encountered in a
        # callback nacks the message and then causes the entire subscription client to exit by
        # calling `set_exception()` on the Future returned by subscribe(). We don't have any
        # exceptions in our callback that should be fatal to the entire application, so we'll just
        # let PubSub know that this message failed to process and then carry on processing more
        # images.
        PROCESS_ONE_IMAGE_FAILED.inc()
        message.nack()
        logger.error(
            f"Got exception processing image: {repr(e)}",
            extra={
                "message_id": message.message_id,
                "delivery_attempt": message.delivery_attempt,
                "bucket_id": message.attributes["bucketId"],
                "object_id": message.attributes["objectId"],
            },
        )
    finally:
        PROCESS_ONE_IMAGE_COMPLETED.inc()


ProcessImageCallback = Callable[
    [
        GCSClient,
        ObjectDetector,
        ImageEmbedder,
        FeatureStore[ImageSchema],
        PublisherClient,
        str,
        str,
        bool,
        Message,
    ],
    None,
]


def start_pipeline(
    subscriber_client: SubscriberClient,
    subscription_id: str,
    gcs_client: GCSClient,
    bigtable_client: BigTableClient,
    object_detection_deployment_url: str,
    image_embedding_deployment_url: str,
    feature_store_bt_instance_name: str,
    publisher_client: PublisherClient,
    object_stats_output_topic: str,
    image_embedding_output_topic: str,
    calc_image_embedding: bool,
    callback: ProcessImageCallback = process_one_image,
) -> StreamingPullFuture:
    logger.info(f"Starting subscription to {subscription_id}")

    # As it stands, it appears that a default of 0.25 is set for a confidence threshold in the
    # YOLOvX model (see YOLO's AutoShape.conf default value of 0.25).
    # However, we don't totally understand this behavior, so we'll implement our own threshold
    # checking here as well.
    confidence_threshold = 0.25
    max_connections_per_pool = 10  # Matches the default thread count for the PubSub client

    bigtable_instance = bigtable_client.instance(feature_store_bt_instance_name)
    feature_store = FeatureStore(bigtable_instance, "image_features", ImageSchema)

    with ObjectDetector(
        object_detection_deployment_url,
        confidence_threshold,
        max_connections_per_pool,
    ) as object_detector, ImageEmbedder(
        image_embedding_deployment_url, max_connections_per_pool
    ) as image_embedder:
        on_message = functools.partial(
            callback,
            gcs_client,
            object_detector,
            image_embedder,
            feature_store,
            publisher_client,
            object_stats_output_topic,
            image_embedding_output_topic,
            calc_image_embedding,
        )

        return subscriber_client.subscribe(
            subscription=subscription_id,
            callback=on_message,
            flow_control=FlowControl(
                max_messages=100,
                max_lease_duration=60,
            ),
        )
