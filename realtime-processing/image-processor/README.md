# Image Processor

The image processor is a containerized application whose purpose is to take OR images as they're
uploaded to GCS, extract useful information from them (e.g. objects in the image, average color of
the image, etc) and pusblish that information to a pubsub topic where other interested parties can
read the information.

## Development

`image_processor` uses Poetry to manage its virtual environment, which includes both its
dependencies and the `image_processor` package itself. Use the usual Poetry incantations to install
it locally.

In the virtualenv, the application is wrapped via a script created by Poetry called
`image_processor` that calls into [cli.py](./image_processor/cli.py). After running `poetry install`,
you should be able to invoke it manually like so: `image_processor --help`.

The application is packaged as a container via a two-stage build process outlined in the
[Dockerfile](./Dockerfile). Accessing the ResnetEmbedding Service from you own machine requires
using kubectl for setting up a tunnel. To do so, make sure you have kubectl installed
(`gcloud components install kubectl`) and then run:

`$ gcloud container clusters get-credentials dev-internal-gke --region us-central1 --project dev-internal-b2aa9f`

`$ kubectl -n resnet-embedding port-forward service/resnet-embedding 9999:80`

To build the container locally, you can run `make container`. To execute
the container locally, you can run `make run-dev`.

### Note on boolean flags

For boolean flags to the cli.py script, we shouldn't use python string args with bool converter. [The bool() function converts any string (including "False") to a True bool value.](https://docs.python.org/3/library/argparse.html#:~:text=The%20bool(),arguments%20are%20parsed)
We should instead use an action with [BooleanOptionalAction](https://docs.python.org/3/library/argparse.html#:~:text=import%20argparse%0A%3E%3E%3E%20parser%20%3D%20argparse.ArgumentParser()%0A%3E%3E%3E%20parser.add_argument(%27%2D%2Dfoo%27%2C%20action%3Dargparse.BooleanOptionalAction)%0A%3E%3E%3E%20parser.parse_args(%5B%27%2D%2Dno%2Dfoo%27%5D)%0ANamespace(foo%3DFalse)) to process boolean flags. We can turn the flags off by adding a --no prefix to the flag arg. eg. --foo-flag vs. --no-foo-flag.

## Deployment

The `image_processor` is deployed into the `dev` and `main` environments and the container version
is kept up to date via the ArgoCD image updater (see the [ml-deployments
repo](https://github.com/Apella-Technology/ml-deployments)).

* [Dev Deployment](https://argocd.dev.internal.apella.io/applications/argocd/image-processor?resource=)
* [Prod Deployment](https://argocd.internal.apella.io/applications/argocd/image-processor?resource=)

In `dev`, the mutable `main` tag is tracked, which allows the image processor to reflect the code
that's currently merged into the `main` branch.

In `prod`, the `image-processor-vx.y.z` family of tags are tracked with the latest built image
having one of those tags being the one that ArgoCD image updater deploys. These tags are created by
publishing GitHub releases for the image processor. To release a new version, find the latest draft
release for the image processor in the GitHub releases list and set it to published.

## Metrics and Monitoring

Metrics from the deployed image processor are integrated into the [Object Model Inference Datadog dashboard](https://app.datadoghq.com/dashboard/4f7-amk-esa/object-model-inference?fromUser=true&view=spans&from_ts=1710265177212&to_ts=1710279577212&live=true).

The standard Pub/Sub subscription metrics are (monitored via
Datadog)[https://app.datadoghq.com/monitors/139555483?view=spans] and wired up to Incident.io to alert
the on-call engineer.
