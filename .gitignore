.DS_Store
.idea
.vscode
venv
*.pyc
build
dist
*.egg-info
datasets
checkpoints
*.csv

# Direnv
.venv
.direnv
.envrc

# GraphQL
schema.graphql
schema.json

# Test coverage
.coverage*
coverage.xml
pytest.*
pytest-coverage.txt

# Emacs files
\#*
*~

# Pickled stuff
*pickle
*.pkl
!models/forecasting/bayesian_case_duration/tests/artifacts/dummy_model.pkl

# The Google auth Github action creates this file. Without this, the Google credentials get added to
# published Python packages (!!!)
gha-creds-*.json

# Catboost training logs
**/catboost_info/

# OpenAPI client
**/.openapi-generator/**
**/openapi_client/*
**/build_artifacts/
**/openapi-spec.json

**/forecast_combiner_client/*
/models/forecasting/forecast_combiner/forecast_combiner_client/
/models/forecasting/bayesian_case_duration/bayesian_case_duration_client/

.aider*
