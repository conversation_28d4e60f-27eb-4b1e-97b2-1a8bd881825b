name: OpenAPI Client Reusable Workflow

on:
  workflow_call:
    inputs:
      working_directory:
        required: true
        type: string
        description: "The directory containing the OpenAPI client generation setup (pyproject.toml, Make<PERSON>le, etc.)"
      python_version:
        required: false
        type: string
        default: "3.10"
        description: "Python version to use"
      poetry_version:
        required: false
        type: string
        default: "1.8.4"
        description: "Poetry version to use"
      artifact_name:
        required: false
        type: string
        default: "openapi-client"
        description: "Name of the artifact to upload"
      project_version:
        required: true
        type: string
        description: The version of the project

jobs:
  test-client:
    uses: ./.github/workflows/test_openapi_client.yml
    with:
      working_directory: ${{ inputs.working_directory }}
      python_version: ${{ inputs.python_version }}
      poetry_version: ${{ inputs.poetry_version }}
    secrets: inherit

  build-client-package:
    runs-on: ubuntu-latest
    needs: test-client
    defaults:
      run:
        working-directory: ${{ inputs.working_directory }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          token_format: "access_token"

      - name: Setup Python environment and Install Dependencies
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          enable_package_uploads: true
          poetry_version: ${{ inputs.poetry_version }}
          python_version: ${{ inputs.python_version }}
          working_dir: ${{ inputs.working_directory }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'

      - name: Install OpenAPI Generator
        run: |
          npm install -g @openapitools/openapi-generator-cli
          openapi-generator-cli version

      - name: Generate client code
        run: make generate-client-for-ci

      - name: Set poetry version
        working-directory: ${{ inputs.working_directory }}/build_artifacts
        run: |
          poetry version $( echo "${{ inputs.project_version }}" | sed 's/^.*-*v//') 
          poetry version

      - id: google-auth-upload
        name: "Authenticate to Google Cloud for upload"
        uses: "google-github-actions/auth@v2"
        with:
          credentials_json: "${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}"

      - name: Publish the library package
        env:
          GOOGLE_APPLICATION_CREDENTIALS: "${{ steps.google-auth-upload.outputs.credentials_file_path }}"
          # PYTHON_KEYRING_BACKEND: "keyring.backends.null.Keyring"
        working-directory: ${{ inputs.working_directory }}/build_artifacts
        run: |
          ACCESS_TOKEN=$(gcloud auth print-access-token)
          poetry config repositories.publish_target https://us-central1-python.pkg.dev/prod-platform-29b5cb/prod-python-registry/
          poetry publish -r publish_target --build
