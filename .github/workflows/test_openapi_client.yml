name: Test OpenAPI Client Reusable Workflow

on:
  workflow_dispatch:
    inputs:
      working_directory:
        required: true
        type: string
        default: "models/forecasting/forecast_combiner"
        description: "The directory containing the OpenAPI client generation setup (pyproject.toml, <PERSON><PERSON>le, etc.)"
      python_version:
        required: false
        type: string
        default: "3.10"
        description: "Python version to use"
      poetry_version:
        required: false
        type: string
        default: "1.8.4"
        description: "Poetry version to use"
  workflow_call:
    inputs:
      working_directory:
        required: true
        type: string
        description: "The directory containing the OpenAPI client generation setup (pyproject.toml, Makefile, etc.)"
      python_version:
        required: false
        type: string
        default: "3.10"
        description: "Python version to use"
      poetry_version:
        required: false
        type: string
        default: "1.8.4"
        description: "Poetry version to use"

jobs:
  test-client:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ inputs.working_directory }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          token_format: "access_token"

      - name: Setup Python environment and Install Dependencies
        uses: Apella-Technology/setup-python-env@v3
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          enable_package_uploads: true
          poetry_version: ${{ inputs.poetry_version }}
          python_version: ${{ inputs.python_version }}
          working_dir: ${{ inputs.working_directory }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'

      - name: Install OpenAPI Generator
        run: |
          npm install -g @openapitools/openapi-generator-cli
          openapi-generator-cli version
      
      - name: Show installed poetry packages
        run: |
          poetry show

      - name: Install poetry plugins
        run: |
          poetry self add "poetry-dynamic-versioning[plugin]"
          poetry self add "keyrings-google-artifactregistry-auth"

      - name: Run tests with generated client
        run: make test-with-client