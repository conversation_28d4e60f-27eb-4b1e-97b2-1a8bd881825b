name: Build Forecast Combiner Client

on:
  release:
    types: [published]
    paths:
      - 'models/forecasting/forecast_combiner/'

jobs:
  build-and-test:
    if: startsWith(github.event.release.tag_name, 'forecast-combiner-v')
    uses: ./.github/workflows/build_openapi_client.yml
    with:
      working_directory: models/forecasting/forecast_combiner
      python_version: "3.10"
      poetry_version: "1.8.4"
      artifact_name: forecast-combiner
      project_version: ${{ github.event.release && github.event.release.tag_name && github.event.release.tag_name }}
    secrets: inherit
