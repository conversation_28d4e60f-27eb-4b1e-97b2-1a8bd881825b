name: Validate

on:
  push:
    branches: [main]
  pull_request:
  merge_group:

jobs:
  Find-Python-Projects:
    runs-on: ubuntu-latest
    outputs:
      projects: ${{ steps.find-projects.outputs.projects }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for all branches

      - id: find-projects
        run: |
          python3 .github/scripts/get_projects.py --type poetry --output-key projects --only-changed

      - name: List all projects
        run: echo '${{ steps.find-projects.outputs.projects }}'

  Python-Linter:
    needs: Find-Python-Projects
    strategy:
      matrix:
        project: ${{fromJson(needs.Find-Python-Projects.outputs.projects)}}
      fail-fast: false
    defaults:
      run:
        working-directory: ${{ matrix.project.project_dir }}
    runs-on: ${{ matrix.project.runner }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          token_format: "access_token"
      - name: Setup Python environment
        uses: Apella-Technology/setup-python-env@v2
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.8.4
          python_version: "3.10"
          working_dir: ${{ matrix.project.project_dir }}
      - name: Lint
        run: make lint

  Python-Unit-Tests:
    needs: Find-Python-Projects
    strategy:
      matrix:
        project: ${{fromJson(needs.Find-Python-Projects.outputs.projects)}}
      fail-fast: false
    defaults:
      run:
        working-directory: ${{ matrix.project.project_dir }}
    runs-on: ${{ matrix.project.runner }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Authenticate to Google Cloud
        id: google-auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          token_format: "access_token"
      - name: Setup Python environment
        uses: Apella-Technology/setup-python-env@v2
        with:
          artifact_registry_credentials: ${{ secrets.ARTIFACT_REGISTRY_SA_CREDENTIALS }}
          poetry_version: 1.8.4
          python_version: "3.10"
          working_dir: ${{ matrix.project.project_dir }}
      - name: Run unit tests
        run: make test

  # This job exists so that we can create a single required check in the Github repository. It
  # depends on all the jobs that we require to pass in order for a PR to merge.
  Validated:
    needs: [Python-Linter, Python-Unit-Tests]
    runs-on: ubuntu-latest
    steps:
      - run: echo Validated!
        shell: bash
