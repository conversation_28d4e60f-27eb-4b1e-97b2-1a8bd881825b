name: Build Bayesian Case Duration Client

on:
  release:
    types: [published]
    paths:
      - 'models/forecasting/bayesian_case_duration/'

jobs:
  build-and-test:
    if: startsWith(github.event.release.tag_name, 'bayesian-case-duration-v')
    uses: ./.github/workflows/build_openapi_client.yml
    with:
      working_directory: models/forecasting/bayesian_case_duration
      python_version: "3.10"
      poetry_version: "1.8.4"
      artifact_name: bayesian-case-duration
      project_version: ${{ github.event.release && github.event.release.tag_name && github.event.release.tag_name }}
    secrets: inherit
