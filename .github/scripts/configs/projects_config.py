from typing import Dict, List

DEFAULT_RUNNER = "ubuntu-latest"

RUNNERS_CONFIG: Dict[str, str] = {
    "apella-yolo": "ubuntu-latest-4-cores",
    "apella-yolov5": "ubuntu-latest-4-cores",
    "resnet-embedding": "ubuntu-latest-4-cores",
}

NON_PROJECT_DIRS: List[str] = [".github"]

PROJECT_TYPES: Dict[str, str] = {
    "bento": "bentofile.yaml",
    "fastapi": "fastapi.service",
    "poetry": "pyproject.toml",
}
