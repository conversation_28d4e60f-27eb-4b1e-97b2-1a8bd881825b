import configs.projects_config as config

import argparse
import glob
import os
import re
from typing import List


def _get_runner(project_name: str) -> str:
    if project_name in config.RUNNERS_CONFIG:
        return config.RUNNERS_CONFIG[project_name]
    return config.DEFAULT_RUNNER


class Project:
    def __init__(self, filepath: str) -> None:
        self.project_dir = os.path.dirname(filepath)
        self.project_name = os.path.basename(os.path.dirname(filepath)).replace("_", "-")
        self.runner = _get_runner(self.project_name)

    def __str__(self) -> str:
        return f'{{"project_dir": "{self.project_dir}", "project_name": "{self.project_name}", "runner": "{self.runner}"}}'

    def __repr__(self) -> str:
        return self.__str__()


def _strip_semver(version: str) -> str:
    pattern = re.compile(r"(.*?)-v\d+\.\d+\.\d+")
    match = pattern.search(version)
    if match:
        return match.group(1)
    return version


def _matches_release_tag(filepath: str, release_tag: str) -> bool:
    if not release_tag:
        return True
    # convention to match tag names to corresponding project dirs, e.g. 'apella-yolov5-v.1.0.0' tag and 'apella_yolov5' project dir
    project_dir = _strip_semver(release_tag).replace("-", "_")
    return project_dir == os.path.basename(os.path.dirname(filepath))


def _find_files(directory: str, file_type: str) -> List[str]:
    file_list = []
    for root, dirs, files in os.walk(directory):
        for dir in config.NON_PROJECT_DIRS:
            if dir in dirs:
                dirs.remove(dir)
        dirs.sort()  # consistent ordering for predictable tests
        files.sort()
        for file in files:
            filepath = os.path.join(root, file)
            if file == config.PROJECT_TYPES[file_type]:
                file_list.append(filepath)
    return file_list


def _get_changed_projects(all_projects: list[Project]) -> list[Project]:
    # Get changed files by using git cli
    git_diff = os.popen("git diff --name-only remotes/origin/main").read()
    changed_files = git_diff.split("\n")

    # These changed files are relative to the root of the repo,
    # but the projects are absolute file path on disk.
    # So we need to convert changed files to absolute paths.
    changed_files = [os.path.abspath(file) for file in changed_files]

    # Filter projects that have changed files
    changed_projects = []
    for project in all_projects:
        # projects are the path to a file in the project, we want the directory
        projects_dir = project.project_dir
        for file in changed_files:
            if file.startswith(projects_dir):
                changed_projects.append(project)
                break

    # on merge to main, the git diff is empty, so we need to return all projects
    if len(changed_projects) == 0:
        # If there are no changed projects, likely we are changing something else that may
        # affect all projects.  So validate everything.
        return all_projects

    return changed_projects


def _add_downstream_projects(
    changed_projects: List[Project], all_projects: List[Project]
) -> List[Project]:
    # Start with the changed projects result
    result = set(changed_projects)

    # Create a queue for projects to find downstream projects for
    queue = set(changed_projects)
    while queue:
        # Pop a project off the queue
        project = queue.pop()

        # Find any project that depends on this project
        for candidate_project in all_projects:
            if candidate_project in result:
                continue

            pyproject_toml_file = os.path.join(candidate_project.project_dir, "pyproject.toml")
            downstream_project_names = []
            # Read the toml file into mmeory
            with open(pyproject_toml_file, "r") as f:
                while line := f.readline():
                    # The dependencies that have local paths in this repo look like:
                    # training-utils = {path = "../../../training_utils", develop = true}
                    if re.search(r"path\s*=\s*\"\.\.", line):
                        # This line is a dependency on a local project
                        # The project is the first part of the line before the =
                        downstream_project_name = line.split("=")[0].strip()
                        downstream_project_names.append(downstream_project_name)

            # Sometimes we name things with `-`, other times with `_`, and that messes up this
            # comparison. So we need to normalize the names.
            downstream_project_names = [name.replace("-", "_") for name in downstream_project_names]
            project_name = project.project_name.replace("-", "_")

            if project_name in downstream_project_names:
                result.add(candidate_project)
                queue.add(candidate_project)

    return list(result)


def main() -> None:
    parser = argparse.ArgumentParser(
        description="This script generates a list of projects that can be used as matrix inputs in GitHub workflow jobs."
    )
    parser.add_argument(
        "--type",
        type=str,
        required=True,
        choices=config.PROJECT_TYPES.keys(),
        help="Type of projects.",
    )

    parser.add_argument(
        "--directory",
        type=str,
        default=os.getcwd(),
        help="Directory to search for projects in.",
    )

    parser.add_argument(
        "--output-key",
        type=str,
        required=True,
        help="Key to use for storing the projects in GITHUB_OUTPUT.",
    )

    parser.add_argument(
        "--release-tag",
        type=str,
        default="",
        help="If not empty, only project dirs that match the release tag prefix will be included.",
    )

    parser.add_argument(
        "--only-changed",
        action="store_true",
        default=False,
        help="If set, only include projects that have been changed in the current PR",
    )

    parser.add_argument(
        "--only-releasable",
        action="store_true",
        default=False,
        help="If set, only include projects that can be released",
    )

    args = parser.parse_args()

    # find and create projects
    candidate_files = _find_files(args.directory, args.type)
    files_matching_release_tag = [
        file for file in candidate_files if _matches_release_tag(file, args.release_tag)
    ]
    projects = [Project(file) for file in files_matching_release_tag]

    if args.only_changed:
        changed_projects = _get_changed_projects(projects)
        projects = _add_downstream_projects(changed_projects, projects)

    if args.only_releasable:
        releasable_projects = [
            os.path.splitext(os.path.basename(path))[0].replace("release-drafter-", "")
            for path in glob.glob(os.path.join(".github", "release-drafter-*.yml"))
        ]
        projects = [project for project in projects if project.project_name in releasable_projects]

    # write out projects
    projects_output = f"{args.output_key}=[{', '.join([str(project) for project in projects])}]"
    github_output = os.getenv("GITHUB_OUTPUT")
    if github_output is None:
        raise ValueError("GITHUB_OUTPUT environment variable is not set.")
    with open(github_output, "w") as fh:
        fh.write(projects_output)


if __name__ == "__main__":
    main()
