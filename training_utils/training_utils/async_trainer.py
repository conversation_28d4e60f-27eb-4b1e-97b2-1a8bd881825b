import argparse
import importlib
import os
import subprocess
from enum import Enum
from pathlib import Path
from typing import Any

import yaml
from dagster_graphql import DagsterGraphQLClient
from git import Repo
from pydantic import BaseModel, Field, field_validator


class AsyncTrainingConfig(BaseModel):
    image_name: str
    memory_spec: str = Field(default="8.0Gi")
    cpu_spec: str = Field(default="4000m")
    gpu_type: str | None = Field(default=None)
    enable_images_persistent_volume: bool = Field(
        default=False,
        description="Whether to mount the images Persistent Volume. Used primarily when training an Object Detection model.",
    )
    dagster_job_name: str = Field(
        default="generic_ml_training_run_training_job",
        description="The name of the Dagster job to run.",
    )
    container_args: list[str] = Field(
        default=["--train", "--evaluate"],
        description="Arguments to pass to the container entrypoint.",
    )

    @field_validator("gpu_type")
    def validate_gpu_type(cls, v: str | None) -> str | None:
        if v is not None:
            if v not in [member.value for member in GPUType]:
                raise ValueError(f"Invalid GPU type: {v}")
        return v


class GPUType(Enum):
    NVIDIA_TESLA_A100 = "nvidia-tesla-a100"
    NVIDIA_TESLA_P100 = "nvidia-tesla-p100"
    NVIDIA_TESLA_T4 = "nvidia-tesla-t4"
    NVIDIA_TESLA_V100 = "nvidia-tesla-v100"


class GenericAsyncTrainer:
    """
    This class is used to train a model asynchronously.
    It will build a docker image, run a dagster job, and return the job id.

    Args:
        config: The configuration for the training job.
        config_module_name: The module name where the model config is defined. If not provided, it'll use the default config module defined in the model training code.
    """

    def __init__(self, config: AsyncTrainingConfig, config_module_name: str | None = None):
        self.config = config
        self.config_module_name = config_module_name
        self.repo = self._get_github_repo_info()

    def _get_github_repo_info(self) -> Repo:
        home = self.find_project_root()
        return Repo(home)

    def find_project_root(self) -> Path:
        current = Path(os.getcwd())
        while current != Path("/"):
            if (current / ".git").is_dir():
                return current

            current = current.parent
        raise FileNotFoundError

    @staticmethod
    def validate_config_module_name(config_module_name: str | None) -> None:
        """
        Validate that the config module name is valid by importing it.

        Raises:
            ImportError: If the config module name is not valid.
        """
        if config_module_name is not None:
            try:
                print(f"Validating config module {config_module_name}")
                importlib.import_module(config_module_name)
                print(f"Config module {config_module_name} exists")
            except ImportError:
                raise ImportError(f"The config module {config_module_name} does not exist")
        else:
            print("No config module name provided. Will use the default config module")

    def train(self) -> None:
        # validate the config module name (if provided)
        self.validate_config_module_name(self.config_module_name)
        # check if repo is clean
        branch_name, sha_tag = self.extract_repo_info()
        self.build_docker_image(self.config.image_name, sha_tag)
        self.run_dagster_job(branch_name, sha_tag)

    def extract_repo_info(self) -> tuple[str, str]:
        # Get the github information
        if self.repo.is_dirty():
            # We will not run auto-trainer if you have uncommitted changes. It becomes a nightmare to keep track of
            # what was run since we're tagging the images with a commit-sha.
            raise Exception(
                "You have uncommitted changes in your directory. Please commit or stash those changes"
            )

        branch_name = self.repo.active_branch.name
        sha = self.repo.head.object.hexsha
        short_sha = self.repo.git.rev_parse(sha, short=7)
        sha_tag = f"sha-{short_sha}"
        return branch_name, sha_tag

    def build_docker_image(self, image_name: str, sha_tag: str) -> None:
        if self.image_exists(image_name, sha_tag):
            print("Image exists in docker registry. Will not build it")
            return

        # variables needed for the build command
        directory_to_run = self.repo.working_tree_dir
        cloudbuild_file = (Path(__file__).parent / "cloudbuild.yaml").absolute()
        project_dir = Path(os.getcwd()).relative_to(str(self.repo.working_tree_dir))
        project_name = project_dir.stem
        # check that it doesn't need a relative path
        if self.config.gpu_type is None:
            dockerfile = Path(__file__).parent / "Dockerfile.cpu"
        else:
            dockerfile = Path(__file__).parent / "Dockerfile.gpu"
        dockerfile = dockerfile.relative_to(str(self.repo.working_tree_dir))

        build_command = f"gcloud builds submit --project prod-platform-29b5cb --config {cloudbuild_file} --substitutions _PROJECT={image_name},_DOCKERFILE={dockerfile},_PROJECT_NAME={project_name},_PROJECT_DIR={project_dir},_VERSION={sha_tag},_GCP_REPOSITORY=us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry".split()
        # Note: this subprocess needs to run from the main project directory so
        #  docker has the context of the shared libraries.
        subprocess.run(build_command, check=True, cwd=directory_to_run)

        # Wait until the image exists. We'll be searching by the tag
        print("Check that the image exists")
        if not self.image_exists(image_name, sha_tag):
            raise Exception(
                "Building the image locally failed. Check that your docker daemon is running and that you're logged on to Google Cloud Services"
            )

    def image_exists(self, image_name: str, sha_tag: str) -> bool:
        artifactory_command = f"gcloud artifacts docker tags list us-central1-docker.pkg.dev/prod-platform-29b5cb/prod-docker-registry/{image_name}".split()
        try:
            res = subprocess.check_output(artifactory_command)
        except subprocess.CalledProcessError:
            print("gloud command failed. This is due to one of two reasons")
            print(" 1. you are not logged in")
            print(" 2. this is the first time you are running this script! Welcome!")
            print("Please ensure you are logged in to gcloud")
            return False

        # Output of command looks something like
        # TAG      IMAGE                                                DIGEST
        # sha-123  us-central1-docker.pkg.dev/prod-platform-29b5cb/...  sha256:dc66a91bda8...
        # sha-345  us-central1-docker.pkg.dev/prod-platform-29b5cb/...  sha256:dc66a91bda8...
        # ...
        # So the following line decodes the output and gets the TAG for the images
        tags = [val.split()[0] for val in res.decode("utf-8").split("\n") if len(val.split()) > 1]
        return sha_tag in tags

    @staticmethod
    def generate_run_config(
        config: AsyncTrainingConfig,
        image_tag: str,
        config_module_name: str | None = None,
    ) -> dict[str, Any]:
        # if a config module name is provided, we want to add it to the container args
        if config_module_name is not None:
            model_config_container_args = [
                "--config-module-name",
                config_module_name,
            ]
        else:
            model_config_container_args = []

        return {
            "execution": {
                "config": {"multiprocess": {"max_concurrent": 0, "retries": {"enabled": {}}}}
            },
            "loggers": {},
            "ops": {
                "run_generic_training": {
                    "config": {
                        "cpu_spec": config.cpu_spec,
                        "gpu_type": config.gpu_type,
                        "image_name": config.image_name,
                        "image_tag": image_tag,
                        "load_incluster_config": True,
                        "memory_spec": config.memory_spec,
                        "k8s_job_service_account": "mlops-dags-ksa",
                        "enable_images_persistent_volume": config.enable_images_persistent_volume,
                        "container_args": config.container_args + model_config_container_args,
                    },
                },
            },
            "resources": {"io_manager": {"config": {}}},
        }

    def run_dagster_job(self, branch_name: str, sha_tag: str) -> None:
        # Next, we tell dagster to run
        run_config = self.generate_run_config(self.config, sha_tag, self.config_module_name)
        print(f"Calling Dagster job {self.config.dagster_job_name} with run config: {run_config}")
        run_tags = {"type": "experiment", "branch": branch_name, "gitsha": sha_tag}
        client = DagsterGraphQLClient("dagster.internal.apella.io", use_https=True)
        job_id = client.submit_job_execution(
            job_name=self.config.dagster_job_name,
            repository_name="mlops-dags",
            run_config=run_config,
            tags=run_tags,
        )
        print(f"Dagster job id is {job_id}. Follow your progress at: ")
        print(f"https://dagster.internal.apella.io/runs/{job_id}")
        print("")
        print("This will start a kubernetes job here:")
        print(
            f" https://console.cloud.google.com/kubernetes/job/us-central1/prod-internal-gke/dagster/dagster-run-{job_id}/details?project=prod-internal-c5ac6b"
        )
        print(
            "Go to that console and you'll see logs for this job. But know that it will start another pod with your actual image"
        )
        print(
            "Go to the logs in the link above, and search for 'Waiting for pod xxxxxxxx to initialize'"
        )
        print(
            "Then search for the logs for pod xxxxxxx in Gcloud Kubernetes engine >> Workloads >> namespace 'dagster'"
        )


def async_trainer() -> None:
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--config-filename",
        help="Name of the async trainer config file",
        required=True,
    )
    parser.add_argument(
        "--model-config-module-name",
        help="Module name where the model config is defined",
        type=str,
        default=None,
    )
    args = parser.parse_args()

    with open(args.config_filename, "rb") as config_file:
        config_dictionary = yaml.safe_load(config_file)

    config = AsyncTrainingConfig(**config_dictionary["async_training"])
    trainer = GenericAsyncTrainer(config, args.model_config_module_name)
    trainer.train()


if __name__ == "__main__":
    async_trainer()
