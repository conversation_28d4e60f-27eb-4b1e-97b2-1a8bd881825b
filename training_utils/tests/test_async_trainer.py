import unittest
from unittest.mock import MagicMock, patch

from training_utils.async_trainer import AsyncTrainingConfig, GenericAsyncTrainer


class TestValidateConfigModuleName(unittest.TestCase):
    def test_valid_module_name(self) -> None:
        """Test that a valid module name passes validation."""
        with patch("importlib.import_module") as mock_import:
            mock_import.return_value = MagicMock()
            GenericAsyncTrainer.validate_config_module_name("valid.module.name")
            mock_import.assert_called_once_with("valid.module.name")

    def test_invalid_module_name(self) -> None:
        """Test that an invalid module name raises ImportError."""
        with patch("importlib.import_module") as mock_import:
            mock_import.side_effect = ImportError("No module named 'invalid.module'")
            with self.assertRaises(ImportError) as context:
                GenericAsyncTrainer.validate_config_module_name("invalid.module")
            self.assertEqual(
                str(context.exception), "The config module invalid.module does not exist"
            )

    def test_none_module_name(self) -> None:
        """Test that None module name is handled correctly."""
        with patch("importlib.import_module") as mock_import:
            GenericAsyncTrainer.validate_config_module_name(None)
            mock_import.assert_not_called()


class TestGenerateRunConfig(unittest.TestCase):
    def setUp(self) -> None:
        self.config = AsyncTrainingConfig(
            image_name="test-image",
            memory_spec="8Gi",
            cpu_spec="4000m",
            gpu_type="nvidia-tesla-t4",
            enable_images_persistent_volume=True,
            container_args=["--train", "--evaluate"],
        )

    def test_generate_run_config_with_module_name(self) -> None:
        """Test generating run config with a module name."""
        config = GenericAsyncTrainer.generate_run_config(
            self.config, "test-tag", "test.module.name"
        )

        self.assertEqual(
            config["ops"]["run_generic_training"]["config"]["image_name"], "test-image"
        )
        self.assertEqual(config["ops"]["run_generic_training"]["config"]["image_tag"], "test-tag")
        self.assertEqual(
            config["ops"]["run_generic_training"]["config"]["container_args"],
            ["--train", "--evaluate", "--config-module-name", "test.module.name"],
        )

    def test_generate_run_config_without_module_name(self) -> None:
        """Test generating run config without a module name."""
        config = GenericAsyncTrainer.generate_run_config(self.config, "test-tag")

        self.assertEqual(
            config["ops"]["run_generic_training"]["config"]["image_name"], "test-image"
        )
        self.assertEqual(config["ops"]["run_generic_training"]["config"]["image_tag"], "test-tag")
        self.assertEqual(
            config["ops"]["run_generic_training"]["config"]["container_args"],
            ["--train", "--evaluate"],
        )


if __name__ == "__main__":
    unittest.main()
