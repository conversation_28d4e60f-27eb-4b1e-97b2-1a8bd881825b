[tool.ruff]
lint.ignore = [
   # Trust black to get line length right. Without this, there are cases where black won't reflow a
     # line that's too long (e.g. comments) and ruff complains.
   "E501"
]
# Enable pycodestyle (`E`), Pyflakes (`F`) and isort (`I001`)
lint.select = ["E", "F", "I001"]
line-length = 100

[tool.mypy]
disallow_untyped_defs = true

[[tool.mypy.overrides]]
module = [
    "clearml.*",
    "cloudpickle.*",
    "dill.*",
    "google.*",
    "joblib.*",
]
ignore_missing_imports = true


[tool.poetry]
name = "training_utils"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "~3.10"
clearml = "^1.18.0"
dagster-graphql = "^1.10.14"
git-python = "^1.0.3"
google-cloud-secret-manager = "^2.23.3"
google-cloud-storage = "^2.19.0"
pandas = ">1.0"
pydantic = "^2.11.4"
tenacity = "^8.5.0"
joblib = "^1.5.0"
dill = "^0.4.0"
pandera = "^0.23.1"
cloudpickle = "^3.1.1"
matplotlib = "^3.10.3"


[tool.poetry.group.dev.dependencies]
mypy = "^1.15.0"
pytest = "^7.4.4"
pytest-cov = "^4.1.0"
ruff = "^0.11.9"
pandas-stubs = "^2.2.3.250308"
types-pyyaml = "^6.0.12.20250402"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
